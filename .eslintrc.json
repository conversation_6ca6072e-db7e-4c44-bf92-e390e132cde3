{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["@typescript-eslint", "react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": ["error"], "@typescript-eslint/no-explicit-any": ["warn"]}, "overrides": [{"files": ["electron-main/**/*.ts"], "env": {"node": true, "browser": false}, "rules": {"no-console": "off"}}]}