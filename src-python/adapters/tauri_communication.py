#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tauri 通信基础类
===============
为 Python Sidecar 提供与 Tauri 通信的标准化接口
"""

import sys
import json
import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from abc import ABC, abstractmethod

# 配置日志输出到 stderr
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)


@dataclass
class TauriMessage:
    """Tauri 消息数据结构"""
    type: str  # "push" | "response" | "error"
    source: str = "python"
    command_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    timestamp: float = None
    success: bool = True
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "type": self.type,
            "source": self.source,
            "timestamp": self.timestamp,
            "success": self.success
        }
        
        if self.command_id:
            result["command_id"] = self.command_id
        if self.data:
            result["data"] = self.data
        if self.error:
            result["error"] = self.error
            
        return result


class TauriCommunicator:
    """Tauri 通信基础类"""
    
    def __init__(self, source_name: str = "python"):
        self.source_name = source_name
        self.running = True
        self.command_handlers: Dict[str, Callable] = {}
        
        # 注册基础命令
        self.register_command("ping", self.handle_ping)
        self.register_command("health_check", self.handle_health_check)
    
    def register_command(self, action: str, handler: Callable):
        """注册命令处理器"""
        self.command_handlers[action] = handler
        logger.info(f"Registered command handler: {action}")
    
    def send_push(self, data: Dict[str, Any], data_type: str = "data"):
        """发送实时推送数据到 Tauri"""
        message = TauriMessage(
            type="push",
            source=self.source_name,
            data={"type": data_type, **data}
        )
        self._send_message(message)
    
    def send_response(self, command_id: str, success: bool, data: Optional[Dict[str, Any]] = None, error: Optional[str] = None):
        """发送命令响应到 Tauri"""
        message = TauriMessage(
            type="response",
            source=self.source_name,
            command_id=command_id,
            data=data,
            success=success,
            error=error
        )
        self._send_message(message)
    
    def send_error(self, command_id: Optional[str], error_message: str):
        """发送错误消息到 Tauri"""
        message = TauriMessage(
            type="error",
            source=self.source_name,
            command_id=command_id,
            success=False,
            error=error_message
        )
        self._send_message(message)
    
    def _send_message(self, message: TauriMessage):
        """发送消息到 stdout (Tauri 会读取)"""
        try:
            message_json = json.dumps(message.to_dict(), ensure_ascii=False)
            print(message_json, flush=True)
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
    
    async def start_stdin_listener(self):
        """启动 stdin 监听器，处理来自 Tauri 的命令"""
        logger.info("Starting stdin listener...")
        
        loop = asyncio.get_event_loop()
        reader = asyncio.StreamReader()
        protocol = asyncio.StreamReaderProtocol(reader)
        await loop.connect_read_pipe(lambda: protocol, sys.stdin)
        
        while self.running:
            try:
                line = await reader.readline()
                if not line:
                    break
                
                line_str = line.decode('utf-8').strip()
                if line_str:
                    await self._handle_command_line(line_str)
                    
            except Exception as e:
                logger.error(f"Error reading stdin: {e}")
                break
    
    async def _handle_command_line(self, line: str):
        """处理单行命令"""
        try:
            command = json.loads(line)
            await self.handle_command(command)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON command: {line}, error: {e}")
            self.send_error(None, f"Invalid JSON: {e}")
        except Exception as e:
            logger.error(f"Error handling command: {e}")
            self.send_error(None, f"Command handling error: {e}")
    
    async def handle_command(self, command: Dict[str, Any]):
        """处理收到的命令"""
        command_id = command.get('id', 'unknown')
        action = command.get('action', '')
        params = command.get('params', {})
        
        logger.info(f"Handling command: {action} (ID: {command_id})")
        
        if action in self.command_handlers:
            try:
                result = await self._call_handler(self.command_handlers[action], params)
                self.send_response(command_id, True, result)
            except Exception as e:
                logger.error(f"Command {action} failed: {e}")
                self.send_response(command_id, False, error=str(e))
        else:
            error_msg = f"Unknown action: {action}"
            logger.warning(error_msg)
            self.send_response(command_id, False, error=error_msg)
    
    async def _call_handler(self, handler: Callable, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用命令处理器（支持同步和异步）"""
        if asyncio.iscoroutinefunction(handler):
            return await handler(params)
        else:
            return handler(params)
    
    async def handle_ping(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理心跳检查"""
        return {
            "status": "alive",
            "timestamp": time.time(),
            "source": self.source_name
        }
    
    async def handle_health_check(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理健康检查"""
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "source": self.source_name,
            "running": self.running
        }
    
    def stop(self):
        """停止通信器"""
        logger.info(f"Stopping {self.source_name} communicator...")
        self.running = False


class TradingAdapterBase(ABC):
    """交易适配器基类"""
    
    def __init__(self, adapter_name: str):
        self.adapter_name = adapter_name
        self.communicator = TauriCommunicator(adapter_name)
        self._setup_commands()
    
    def _setup_commands(self):
        """设置命令处理器"""
        # 注册通用命令
        self.communicator.register_command("connect", self.connect)
        self.communicator.register_command("disconnect", self.disconnect)
        self.communicator.register_command("get_status", self.get_status)
        
        # 注册子类特定命令
        self.register_custom_commands()
    
    @abstractmethod
    def register_custom_commands(self):
        """注册自定义命令（由子类实现）"""
        pass
    
    @abstractmethod
    async def connect(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """连接到交易服务（由子类实现）"""
        pass
    
    @abstractmethod
    async def disconnect(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """断开交易服务连接（由子类实现）"""
        pass
    
    @abstractmethod
    async def get_status(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取连接状态（由子类实现）"""
        pass
    
    async def start(self):
        """启动适配器"""
        logger.info(f"Starting {self.adapter_name} adapter...")
        await self.communicator.start_stdin_listener()
    
    def send_realtime_data(self, data_type: str, data: Dict[str, Any]):
        """发送实时数据"""
        self.communicator.send_push(data, data_type)
    
    def stop(self):
        """停止适配器"""
        self.communicator.stop()


if __name__ == "__main__":
    # 测试通信器
    async def test_communicator():
        comm = TauriCommunicator("test")
        
        # 发送测试数据
        comm.send_push({"test": "data"}, "test_data")
        
        # 启动监听器
        await comm.start_stdin_listener()
    
    asyncio.run(test_communicator())
