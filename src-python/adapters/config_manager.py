#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理工具
============
统一管理开发和生产环境的配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, env: str = None):
        # 智能路径检测：支持开发环境和打包环境
        self.project_root = self._detect_project_root()
        self.config_dir = self.project_root / "config"
        self.env = env or os.getenv("APP_ENV", "development")
        self._config = None
        print(f"🔍 项目根目录: {self.project_root}")
        print(f"🔍 配置目录: {self.config_dir}")
    
    def _detect_project_root(self) -> Path:
        """智能检测项目根目录，兼容开发环境和打包环境"""
        # 1. 检查是否在 Tauri 应用包环境中
        tauri_resource_dir = os.getenv("TAURI_APP_RESOURCE_DIR")
        if tauri_resource_dir:
            print(f"🍎 检测到 Tauri 应用包环境: {tauri_resource_dir}")
            return Path(tauri_resource_dir)
        
        # 2. 检查当前工作目录是否是应用包内的 _up_ 目录
        current_dir = Path.cwd()
        if current_dir.name == "_up_" and "Contents/Resources" in str(current_dir):
            print(f"🍎 检测到应用包 _up_ 目录: {current_dir}")
            return current_dir
        
        # 3. 检查脚本文件路径，看是否在应用包内
        script_path = Path(__file__).resolve()
        for parent in script_path.parents:
            if parent.name == "_up_" and "Contents/Resources" in str(parent):
                print(f"🍎 从脚本路径检测到应用包环境: {parent}")
                return parent
        
        # 4. 回退到开发环境的路径计算方式
        project_root = Path(__file__).parent.parent.parent
        print(f"🔧 使用开发环境路径计算: {project_root}")
        return project_root
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self._config is not None:
            return self._config
        
        # 配置文件优先级：user.json > {env}.json > development.json
        config_files = [
            self.config_dir / "user.json",           # 用户自定义配置（最高优先级）
            self.config_dir / f"{self.env}.json",    # 环境配置
            self.config_dir / "development.json"     # 默认配置
        ]
        
        config = {}
        
        for config_file in reversed(config_files):  # 反向加载，让优先级高的覆盖低的
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        file_config = json.load(f)
                        self._merge_config(config, file_config)
                        print(f"✅ 已加载配置文件: {config_file}")
                except Exception as e:
                    print(f"❌ 配置文件加载失败: {config_file}, 错误: {e}")
        
        # 替换环境变量
        self._replace_env_vars(config)
        
        self._config = config
        return config
    
    def get_futu_config(self) -> Dict[str, Any]:
        """获取富途配置"""
        config = self.load_config()
        return config.get("futu", {})
    
    def get_huasheng_config(self) -> Dict[str, Any]:
        """获取华盛配置"""
        config = self.load_config()
        return config.get("huasheng", {})
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        config = self.load_config()
        return config.get("app", {})
    
    def _merge_config(self, target: dict, source: dict):
        """合并配置字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value
    
    def _replace_env_vars(self, config: dict):
        """替换配置中的环境变量"""
        for key, value in config.items():
            if isinstance(value, dict):
                self._replace_env_vars(value)
            elif isinstance(value, str) and value.startswith("${") and value.endswith("}"):
                env_var = value[2:-1]  # 去掉 ${} 
                config[key] = os.getenv(env_var, value)


# 全局配置管理器实例
config_manager = ConfigManager()


def get_futu_config():
    """获取富途配置的便捷函数"""
    return config_manager.get_futu_config()


def get_huasheng_config():
    """获取华盛配置的便捷函数"""
    return config_manager.get_huasheng_config()


def get_app_config():
    """获取应用配置的便捷函数"""
    return config_manager.get_app_config()


if __name__ == "__main__":
    # 测试配置加载
    print("🔧 配置管理器测试")
    print("=" * 50)
    
    futu = get_futu_config()
    print(f"富途配置: {futu}")
    
    huasheng = get_huasheng_config()
    print(f"华盛配置: {huasheng}")
    
    app = get_app_config()
    print(f"应用配置: {app}")