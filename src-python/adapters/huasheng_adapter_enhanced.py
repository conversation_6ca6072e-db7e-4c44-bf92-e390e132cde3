#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华盛适配器增强版 - 使用新的通信基础类
=================================
基于 TradingAdapterBase 实现的华盛交易适配器
"""

import asyncio
import time
import sys
import os
from pathlib import Path
from typing import Dict, Any
from tauri_communication import TradingAdapterBase
import logging

# 智能路径管理：兼容开发环境和打包环境
def setup_python_paths():
    """设置Python模块导入路径，优先使用环境变量"""
    
    # 优先使用 Rust 注入的环境变量，这是最可靠的方式
    resource_dir_str = os.getenv("APP_RESOURCE_DIR")
    
    if resource_dir_str:
        # 打包环境
        print(f"INFO: Detected APP_RESOURCE_DIR: {resource_dir_str}")
        resource_path = Path(resource_dir_str)
        # 工作目录已经是 resource_path，所以可以直接使用相对路径
        # 但为了绝对可靠，我们仍然基于 resource_path 构建完整路径
        paths_to_add = [
            str(resource_path / "src-python" / "huasheng_client" / "src"),
            str(resource_path / "src-python" / "adapters"),
        ]
    else:
        # 开发环境回退方案
        print("INFO: APP_RESOURCE_DIR not set, assuming development mode.")
        script_path = Path(__file__).resolve()
        # 从脚本路径向上找到项目根目录（包含 src-python 的目录）
        project_root = script_path.parent.parent.parent
        paths_to_add = [
            str(project_root / "src-python" / "huasheng_client" / "src"),
            str(project_root / "src-python" / "adapters"),
        ]

    # 添加路径到 sys.path
    for path in paths_to_add:
        if path not in sys.path:
            # 使用 insert(0, ...) 确保我们的路径优先
            sys.path.insert(0, path)
            print(f"🔧 Added to sys.path: {path}")

# 设置路径并导入配置管理器
setup_python_paths()
from config_manager import get_huasheng_config

logger = logging.getLogger(__name__)


class HuashengAdapterEnhanced(TradingAdapterBase):
    """华盛交易适配器增强版"""
    
    def __init__(self):
        super().__init__("huasheng")
        self.trade_server = None
        self.connected = False
        
        # 从配置文件读取账号信息
        config = get_huasheng_config()
        self.account = config.get("account", "***********")
        self.password = config.get("password", "456789")
        
    def register_custom_commands(self):
        """注册华盛特定的命令"""
        # 交易相关命令
        self.communicator.register_command("get_funds", self.get_funds)
        self.communicator.register_command("get_positions", self.get_positions)
        self.communicator.register_command("get_orders", self.get_orders)
        self.communicator.register_command("place_order", self.place_order)
        self.communicator.register_command("cancel_order", self.cancel_order)
        
        # 账户相关命令
        self.communicator.register_command("get_account_info", self.get_account_info)
        self.communicator.register_command("get_trade_history", self.get_trade_history)
        
        # 演示相关命令
        self.communicator.register_command("run_trading_demo", self.run_trading_demo)
        
        # 订阅相关命令
        self.communicator.register_command("subscribe_realtime", self.subscribe_realtime)
        self.communicator.register_command("unsubscribe_realtime", self.unsubscribe_realtime)
        self.communicator.register_command("subscribe_huasheng_data", self.subscribe_huasheng_data)
        self.communicator.register_command("unsubscribe_huasheng_data", self.unsubscribe_huasheng_data)
    
    async def connect(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """连接到华盛交易服务"""
        try:
            # 这里应该实现真实的华盛连接逻辑
            # from Server.ApiServer.api_trade_server import ApiTradeServer
            # self.trade_server = ApiTradeServer()
            
            # 模拟连接过程
            await asyncio.sleep(1)
            self.connected = True
            
            logger.info("华盛交易服务连接成功")
            return {
                "status": "connected",
                "timestamp": time.time(),
                "message": "华盛交易服务连接成功",
                "account": self.account
            }
        except Exception as e:
            logger.error(f"华盛连接失败: {e}")
            self.connected = False
            raise Exception(f"华盛连接失败: {e}")
    
    async def disconnect(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """断开华盛连接"""
        try:
            # 这里应该实现真实的断开逻辑
            self.connected = False
            
            logger.info("华盛交易服务连接已断开")
            return {
                "status": "disconnected",
                "timestamp": time.time(),
                "message": "华盛交易服务连接已断开"
            }
        except Exception as e:
            logger.error(f"华盛断开连接失败: {e}")
            raise Exception(f"华盛断开连接失败: {e}")
    
    async def get_status(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取华盛连接状态"""
        return {
            "adapter": "huasheng",
            "connected": self.connected,
            "timestamp": time.time(),
            "status": "healthy" if self.connected else "disconnected",
            "account": self.account
        }
    
    async def get_funds(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取资金信息"""
        # 模拟资金数据
        funds_data = {
            "available_funds": 500000.00,
            "buying_power": 1000000.00,
            "total_assets": 1200000.00,
            "frozen_funds": 50000.00,
            "currency": "HKD",
            "timestamp": time.time()
        }
        
        return {"funds": funds_data}
    
    async def get_positions(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取持仓信息"""
        # 模拟持仓数据
        positions_data = [
            {
                "stock_code": "HK.00700",
                "stock_name": "腾讯控股",
                "position": 500,
                "avg_cost": 400.50,
                "current_price": 420.30,
                "market_value": 210150.00,
                "unrealized_pnl": 9900.00,
                "unrealized_pnl_ratio": 4.94
            },
            {
                "stock_code": "HK.00941",
                "stock_name": "中国移动",
                "position": 1000,
                "avg_cost": 60.20,
                "current_price": 62.10,
                "market_value": 62100.00,
                "unrealized_pnl": 1900.00,
                "unrealized_pnl_ratio": 3.16
            }
        ]
        
        return {"positions": positions_data}
    
    async def get_orders(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取订单列表"""
        status = params.get("status")
        
        # 模拟订单数据
        orders_data = [
            {
                "order_id": "HS_ORDER_123456",
                "stock_code": "HK.00700",
                "order_type": "BUY",
                "quantity": 100,
                "price": 420.00,
                "status": "FILLED",
                "timestamp": time.time() - 3600
            },
            {
                "order_id": "HS_ORDER_123457",
                "stock_code": "HK.00941",
                "order_type": "SELL",
                "quantity": 200,
                "price": 62.50,
                "status": "PENDING",
                "timestamp": time.time() - 1800
            }
        ]
        
        # 根据状态过滤
        if status:
            orders_data = [order for order in orders_data if order["status"] == status.upper()]
        
        return {"orders": orders_data}
    
    async def place_order(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """下单"""
        # 模拟下单
        order_data = {
            "order_id": f"HS_ORDER_{int(time.time())}",
            "stock_code": params.get("stock_code"),
            "order_type": params.get("order_type"),
            "quantity": params.get("quantity"),
            "price": params.get("price"),
            "order_mode": params.get("order_mode", "LIMIT"),
            "status": "SUBMITTED",
            "timestamp": time.time()
        }
        
        return {"order": order_data}
    
    async def cancel_order(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """撤单"""
        order_id = params.get("order_id")
        return {
            "order_id": order_id,
            "status": "CANCELLED",
            "timestamp": time.time()
        }
    
    async def get_account_info(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取账户信息"""
        account_info = {
            "account": self.account,
            "account_type": "CASH",
            "currency": "HKD",
            "market_access": ["HK", "US"],
            "trading_status": "ACTIVE",
            "timestamp": time.time()
        }
        
        return {"account_info": account_info}
    
    async def get_trade_history(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取交易历史"""
        start_date = params.get("start_date")
        end_date = params.get("end_date")
        
        # 模拟交易历史数据
        trade_history = [
            {
                "trade_id": "TRADE_001",
                "stock_code": "HK.00700",
                "trade_type": "BUY",
                "quantity": 100,
                "price": 415.00,
                "amount": 41500.00,
                "trade_time": time.time() - 86400,
                "commission": 15.00
            },
            {
                "trade_id": "TRADE_002",
                "stock_code": "HK.00941",
                "trade_type": "SELL",
                "quantity": 200,
                "price": 61.80,
                "amount": 12360.00,
                "trade_time": time.time() - 172800,
                "commission": 12.36
            }
        ]
        
        return {"trade_history": trade_history}
    
    async def run_trading_demo(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易演示"""
        demo_result = {
            "demo_type": "华盛交易演示",
            "steps": [
                "连接华盛服务器",
                "查询账户资金",
                "查询持仓信息",
                "模拟下单操作",
                "查询订单状态"
            ],
            "status": "completed",
            "timestamp": time.time()
        }
        
        return {"demo_result": demo_result}
    
    async def subscribe_realtime(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """订阅实时数据"""
        stock_code = params.get("stock_code")
        data_types = params.get("data_types", [])
        
        # 启动实时数据推送任务
        asyncio.create_task(self._push_realtime_data(stock_code, data_types))
        
        return {
            "subscribed": True,
            "stock_code": stock_code,
            "data_types": data_types,
            "timestamp": time.time()
        }
    
    async def unsubscribe_realtime(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """取消订阅实时数据"""
        return {
            "unsubscribed": True,
            "timestamp": time.time()
        }
    
    async def subscribe_huasheng_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """订阅华盛特定数据"""
        return await self.subscribe_realtime(params)
    
    async def unsubscribe_huasheng_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """取消订阅华盛特定数据"""
        return await self.unsubscribe_realtime(params)
    
    async def _push_realtime_data(self, stock_code: str, data_types: list):
        """推送实时数据"""
        for i in range(10):  # 推送10次测试数据
            await asyncio.sleep(3)  # 每3秒推送一次
            
            # 推送资金更新
            if "Funds" in data_types:
                funds_data = {
                    "available_funds": 500000.00 + i * 1000,
                    "total_assets": 1200000.00 + i * 2000,
                    "timestamp": time.time()
                }
                self.send_realtime_data("funds_update", funds_data)
            
            # 推送持仓更新
            if "Position" in data_types:
                position_data = {
                    "stock_code": stock_code,
                    "current_price": 420.30 + i * 0.5,
                    "market_value": (420.30 + i * 0.5) * 500,
                    "unrealized_pnl": 9900.00 + i * 250,
                    "timestamp": time.time()
                }
                self.send_realtime_data("position_update", position_data)


async def main():
    """主函数"""
    adapter = HuashengAdapterEnhanced()
    await adapter.start()


if __name__ == "__main__":
    asyncio.run(main())
