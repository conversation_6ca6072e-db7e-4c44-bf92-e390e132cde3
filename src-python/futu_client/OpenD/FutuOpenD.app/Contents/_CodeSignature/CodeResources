<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		CutZQA38Ozy0s836vFOyoKmBEPA=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		WK2GGcW8sbxcvTGYNE2+XMEvhnU=
		</data>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<data>
		S44cPZt1i/AGK9qCLuRSaafUd1I=
		</data>
		<key>Resources/FTUpdate</key>
		<data>
		H9MCMGmV2TPgP+ZLT4KZbRY4LzA=
		</data>
		<key>Resources/FTWebSocket</key>
		<data>
		kLz4Fn9B0lrCAvmx+tK8mTp76oo=
		</data>
		<key>Resources/GoogleService-Info.plist</key>
		<data>
		hD21AYyut5H4EbLSDYQIAa30ol8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/F3CBasis.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			rqKLJ8KWCKCURbazmIbsMDw9tjs=
			</data>
			<key>requirement</key>
			<string>identifier "cn.futu.F3CBasis" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "6HE4ZULJ45"</string>
		</dict>
		<key>Frameworks/F3CLog.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			eM9q0IorEpYtsfFGPeMRgUvZr+8=
			</data>
			<key>requirement</key>
			<string>identifier "cn.futu.F3CLog" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "6HE4ZULJ45"</string>
		</dict>
		<key>Frameworks/F3CLogUploader.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			7rIF0oStlAw8dVGUjMN0PgF16kw=
			</data>
			<key>requirement</key>
			<string>identifier "cn.futu.F3CLogUploader" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "6HE4ZULJ45"</string>
		</dict>
		<key>Frameworks/F3CLogin.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			AqWG4nn4Bj93BUk0J3eiPy5nc2o=
			</data>
			<key>requirement</key>
			<string>identifier "cn.futu.F3CLogin" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "6HE4ZULJ45"</string>
		</dict>
		<key>Frameworks/F3CReport.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			R72iuqGCy+CvU+6m+jIiP9szb8k=
			</data>
			<key>requirement</key>
			<string>identifier "cn.futu.F3CReport" and anchor apple generic and certificate 1[field.1.2.840.113635.*********] /* exists */ and certificate leaf[field.1.2.840.113635.**********] /* exists */ and certificate leaf[subject.OU] = "6HE4ZULJ45"</string>
		</dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			CzGfivHFQNpBET57+ALqf8C2r5+/Ztc9CAKi8vxEVZQ=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			E9zN20+1FPj2t8xXwFkzOmTvBLTbWZ7XKF4x3YDiB2A=
			</data>
		</dict>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			xdWbqLJCfgs94XCeHbkuypKIssGU0ql0OBB/otagOsY=
			</data>
		</dict>
		<key>Resources/FTUpdate</key>
		<dict>
			<key>hash2</key>
			<data>
			UneUS+f0SJrQAGhhmVqKloWK6n+Id3AQ4G++1IaOHBM=
			</data>
		</dict>
		<key>Resources/FTWebSocket</key>
		<dict>
			<key>hash2</key>
			<data>
			i+JDkIvYJ38rEDpZk38FziGT5D/BJo6GvzIcxON2qj8=
			</data>
		</dict>
		<key>Resources/GoogleService-Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qMWLLF4OX7vSql0hvgeJbFKXLDMWt4ssN7j8b3Zqd9Y=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
