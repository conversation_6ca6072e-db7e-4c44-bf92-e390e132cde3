## GUI中文显示问题解决方案

### 问题描述
用户遇到GUI界面中文字符显示为问号(???)的问题。

### 原因分析
Dear PyGui默认不支持中文字符显示，需要加载适当的中文字体。

### 解决方案

#### 方案1: 英文界面版本 (推荐)
文件: `src/trading_software_gui_english.py`

**特点:**
- 使用英文界面，完全避免中文字体问题
- 功能完整，包括实时行情、交易记录、系统日志
- 自动订阅演示股票 HK.08406
- 运行稳定，无字体兼容性问题

**使用方法:**
```bash
uv run src/trading_software_gui_english.py
```

#### 方案2: 中文界面版本 (带字体修复)
文件: `src/trading_software_gui_fixed.py`

**特点:**
- 完整中文界面
- 尝试加载系统中文字体
- 支持 PingFang、STHeiti 等macOS字体
- 如果字体加载失败，会回退到默认字体

**使用方法:**
```bash
uv run src/trading_software_gui_fixed.py
```

### 测试结果

1. **英文版本**: ✅ 运行正常，功能完整
   - 成功连接OpenD服务器
   - 自动订阅演示股票 HK.08406, HK.00700, HK.00388
   - 界面显示正常，无字符编码问题

2. **中文版本**: ⚠️ 可能存在字体显示问题
   - 功能正常，但中文字符可能显示为问号
   - 需要系统支持中文字体

### 推荐使用

建议使用**英文版本** (`trading_software_gui_english.py`)，因为：
- 完全避免中文字体问题
- 界面清晰，功能完整
- 运行稳定，无兼容性问题
- 日志仍然支持中文，便于调试

### 主要功能

两个版本都包含以下功能：
- 实时股票行情显示
- 交易面板（买入/卖出）
- 交易记录管理
- 系统日志显示
- 自动订阅演示股票
- 数据库存储
- 暗色主题界面

### 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 富途交易软件 / Futu Trading Software                         │
├─────────────────────────────────────────────────────────────┤
│ [Stock Code] [Subscribe] [Connection Status]                 │
├─────────────────────────────────────────────────────────────┤
│ Real-time Quotes │    Trading Panel    │    Trade Records    │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│ │ Code │ Name │...│ │ Stock Code:     │ │ Time │ Code │...│ │
│ │ 08406│ ...  │...│ │ Price:          │ │ 12:34│ 08406│...│ │
│ │ 00700│ ...  │...│ │ Quantity:       │ │ 12:35│ 00700│...│ │
│ └─────────────────┘ │ [BUY] [SELL]    │ └─────────────────┘ │
│                     │ Statistics:      │                     │
│ System Log          │ Subscribed: 3    │                     │
│ ┌─────────────────┐ │ Updates: 123     │                     │
│ │ [INFO] Started  │ │ Trades: 5        │                     │
│ │ [INFO] Connected│ └─────────────────┘                     │
│ └─────────────────┘                                         │
└─────────────────────────────────────────────────────────────┘
```