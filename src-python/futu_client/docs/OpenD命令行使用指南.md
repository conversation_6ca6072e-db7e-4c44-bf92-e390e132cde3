# 富途OpenD命令行版本使用指南

## 📋 概述

您目前使用的是GUI版本的OpenD，但也可以通过命令行方式启动，享受更灵活的配置和更好的服务器部署体验。

## 🚀 命令行启动方式

### 当前配置

您的OpenD配置文件位于：`/Users/<USER>/Desktop/hs/pro_api_demo_python/futunn/OpenD/FutuOpenD.xml`

当前配置：
- 账号：7572066
- 密码：F697373691q
- 监听地址：127.0.0.1
- API端口：11111

### 启动命令

在OpenD目录中执行以下命令：

```bash
# 进入OpenD目录
cd /Users/<USER>/Desktop/hs/pro_api_demo_python/futunn/OpenD

# 方式1: 直接启动（前台运行）
./FutuOpenD.app/Contents/MacOS/FutuOpenD

# 方式2: 后台启动
./FutuOpenD.app/Contents/MacOS/FutuOpenD &

# 方式3: 使用nohup后台启动（推荐服务器使用）
nohup ./FutuOpenD.app/Contents/MacOS/FutuOpenD > opend.log 2>&1 &
```

## 🔧 命令行参数

OpenD支持以下命令行参数：

```bash
# 指定配置文件路径
./FutuOpenD.app/Contents/MacOS/FutuOpenD -cfg_file /path/to/FutuOpenD.xml

# 指定数据文件路径
./FutuOpenD.app/Contents/MacOS/FutuOpenD -data_file /path/to/AppData.dat

# 查看帮助
./FutuOpenD.app/Contents/MacOS/FutuOpenD -h
```

## 🛠️ 配置文件详解

### 基础配置

```xml
<!-- 登录账号 -->
<login_account>7572066</login_account>

<!-- 登录密码（明文） -->
<login_pwd>F697373691q</login_pwd>

<!-- 或使用MD5加密密码 -->
<!-- <login_pwd_md5>your_md5_password</login_pwd_md5> -->

<!-- 监听地址 -->
<ip>127.0.0.1</ip>

<!-- API端口 -->
<api_port>11111</api_port>

<!-- 语言设置 -->
<lang>chs</lang>
```

### 高级配置

```xml
<!-- 日志级别 -->
<log_level>info</log_level>

<!-- 推送协议类型 (0:pb, 1:json) -->
<push_proto_type>0</push_proto_type>

<!-- 推送频率控制(毫秒) -->
<qot_push_frequency>1000</qot_push_frequency>

<!-- 自动抢权限 -->
<auto_hold_quote_right>1</auto_hold_quote_right>

<!-- 到价提醒推送 -->
<price_reminder_push>1</price_reminder_push>

<!-- 期货交易时区 -->
<future_trade_api_time_zone>UTC+8</future_trade_api_time_zone>
```

## 🧪 测试连接

使用我们创建的测试脚本验证OpenD是否正常工作：

```bash
# 确保OpenD已启动
./FutuOpenD.app/Contents/MacOS/FutuOpenD &

# 等待几秒钟让OpenD完全启动
sleep 5

# 运行测试脚本
uv run tests/test_opend_cli.py
```

## 📊 常用操作

### 检查OpenD状态

```bash
# 检查OpenD进程
ps aux | grep FutuOpenD

# 检查端口占用
lsof -i :11111

# 检查日志
tail -f ~/Library/Application Support/Futu/FutuOpenD/logs/FutuOpenD.log
```

### 重启OpenD

```bash
# 停止OpenD
pkill -f FutuOpenD

# 重新启动
./FutuOpenD.app/Contents/MacOS/FutuOpenD &
```

## 🔄 自动化脚本

创建启动脚本：

```bash
#!/bin/bash
# 文件名: start_opend.sh

OPEND_DIR="/Users/<USER>/Desktop/hs/pro_api_demo_python/futunn/OpenD"
OPEND_BIN="$OPEND_DIR/FutuOpenD.app/Contents/MacOS/FutuOpenD"

cd "$OPEND_DIR"

# 停止已存在的OpenD进程
pkill -f FutuOpenD

# 等待进程完全停止
sleep 2

# 启动OpenD
echo "启动OpenD..."
nohup "$OPEND_BIN" > opend.log 2>&1 &

# 等待启动完成
sleep 5

# 检查是否启动成功
if pgrep -f FutuOpenD > /dev/null; then
    echo "✅ OpenD启动成功"
    echo "PID: $(pgrep -f FutuOpenD)"
    echo "日志: $OPEND_DIR/opend.log"
else
    echo "❌ OpenD启动失败"
    exit 1
fi
```

## 📈 性能监控

### 监控脚本

```bash
#!/bin/bash
# 文件名: monitor_opend.sh

while true; do
    if pgrep -f FutuOpenD > /dev/null; then
        echo "$(date): OpenD running (PID: $(pgrep -f FutuOpenD))"
    else
        echo "$(date): OpenD not running, restarting..."
        ./start_opend.sh
    fi
    sleep 60
done
```

### 资源使用监控

```bash
# 查看OpenD进程资源使用
ps aux | grep FutuOpenD

# 查看内存使用
top -pid $(pgrep -f FutuOpenD)

# 查看网络连接
netstat -an | grep 11111
```

## 🐛 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查OpenD是否启动
   - 确认端口11111未被占用
   - 检查防火墙设置

2. **登录失败**
   - 验证账号密码正确
   - 检查网络连接
   - 确认富途账号状态

3. **权限问题**
   - 执行修复脚本：`./fixrun.sh`
   - 检查文件权限：`chmod +x FutuOpenD.app/Contents/MacOS/FutuOpenD`

### 日志分析

```bash
# 查看OpenD日志
tail -f ~/Library/Application Support/Futu/FutuOpenD/logs/FutuOpenD.log

# 查看API调用日志
tail -f ~/Library/Application Support/Futu/FutuOpenD/logs/api.log

# 查看错误日志
grep -i error ~/Library/Application Support/Futu/FutuOpenD/logs/FutuOpenD.log
```

## 🎯 最佳实践

1. **生产环境部署**
   - 使用nohup后台运行
   - 配置定期重启脚本
   - 监控日志文件大小

2. **开发环境**
   - 使用前台运行便于调试
   - 设置debug日志级别
   - 实时查看日志输出

3. **安全建议**
   - 不要在公网暴露OpenD端口
   - 定期更新OpenD版本
   - 使用MD5加密密码

通过以上配置和操作，您就可以完全使用命令行方式管理OpenD了！