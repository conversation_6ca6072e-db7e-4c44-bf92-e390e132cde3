# 富途 OpenAPI 详细使用文档

## 📋 目录

1. [环境准备](#环境准备)
2. [基础连接](#基础连接)
3. [数据订阅机制](#数据订阅机制)
4. [核心数据获取](#核心数据获取)
5. [完整示例代码](#完整示例代码)
6. [常见问题解决](#常见问题解决)
7. [最佳实践](#最佳实践)

---

## 🚀 环境准备

### 1. 安装富途 OpenAPI

```bash
uv add futu-api
```

### 2. 启动富途牛牛客户端

-   下载并安装富途牛牛桌面版
-   登录账户
-   开启 OpenAPI 功能：设置 → 开发者选项 → 启用 OpenAPI

### 3. 确认连接参数

-   **默认地址**: `127.0.0.1`
-   **默认端口**: `11111`
-   **协议**: TCP

---

## 🔌 基础连接

### 连接上下文管理

```python
from futu import *
import pandas as pd

# 创建行情连接上下文
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 执行API调用
    ret, data = quote_ctx.get_stock_quote(['HK.08406'])
    if ret == RET_OK:
        print("连接成功")
    else:
        print(f"连接失败: {data}")
finally:
    # 必须关闭连接
    quote_ctx.close()
```

### 连接状态检查

```python
def check_connection():
    """检查富途OpenAPI连接状态"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    try:
        ret, data = quote_ctx.get_stock_quote(['HK.00700'])  # 测试腾讯股票
        if ret == RET_OK:
            print("✅ 富途OpenAPI连接正常")
            return True
        else:
            print(f"❌ 连接失败: {data}")
            return False
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    finally:
        quote_ctx.close()
```

---

## 📡 数据订阅机制

### 订阅类型说明

| 订阅类型             | 说明     | 用途                       |
| -------------------- | -------- | -------------------------- |
| `SubType.QUOTE`      | 基本行情 | 获取股价、成交量等基础信息 |
| `SubType.TICKER`     | 逐笔交易 | 获取每笔交易的详细信息     |
| `SubType.ORDER_BOOK` | 买卖盘   | 获取买卖档位信息           |
| `SubType.BROKER`     | 经纪队列 | 获取经纪商参与信息         |

### 订阅流程

```python
def subscribe_data(stock_code, sub_types):
    """订阅股票数据"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 订阅数据
        ret_sub, err_message = quote_ctx.subscribe([stock_code], sub_types)
        if ret_sub != RET_OK:
            print(f"订阅失败: {err_message}")
            return False

        # 等待订阅生效
        import time
        time.sleep(1)

        print(f"✅ 成功订阅 {stock_code} 的数据")
        return True

    except Exception as e:
        print(f"订阅异常: {e}")
        return False
    finally:
        quote_ctx.close()

# 使用示例
subscribe_data('HK.08406', [SubType.QUOTE, SubType.TICKER, SubType.ORDER_BOOK, SubType.BROKER])
```

---

## 📊 核心数据获取

### 1. 基本行情数据

#### 获取股票基本信息

```python
def get_basic_quote(stock_code):
    """获取股票基本行情"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 先订阅基本行情
        ret_sub, err_message = quote_ctx.subscribe([stock_code], [SubType.QUOTE])
        if ret_sub != RET_OK:
            print(f"订阅基本行情失败: {err_message}")
            return None

        # 等待订阅生效
        import time
        time.sleep(1)

        # 获取行情数据
        ret, data = quote_ctx.get_stock_quote([stock_code])

        if ret == RET_OK and isinstance(data, pd.DataFrame) and not data.empty:
            row = data.iloc[0]

            quote_info = {
                '股票代码': row['code'],
                '股票名称': row['name'] if 'name' in data.columns else '未知',
                '最新价格': float(row['last_price']),
                '开盘价格': float(row['open_price']),
                '最高价格': float(row['high_price']),
                '最低价格': float(row['low_price']),
                '成交量': int(row['volume']),
                '成交额': float(row['turnover']),
                '涨跌幅': float(row['change_rate']),
                '涨跌额': float(row['change_val']) if 'change_val' in data.columns else 0
            }

            return quote_info
        else:
            print(f"获取基本行情失败: {data}")
            return None

    except Exception as e:
        print(f"获取基本行情异常: {e}")
        return None
    finally:
        quote_ctx.close()

# 使用示例
quote = get_basic_quote('HK.08406')
if quote:
    for key, value in quote.items():
        print(f"{key}: {value}")
```

### 2. 逐笔交易数据

#### 获取逐笔交易记录

```python
def get_tick_data(stock_code, num=20):
    """获取逐笔交易数据"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 订阅逐笔数据
        ret_sub, err_message = quote_ctx.subscribe([stock_code], [SubType.TICKER])
        if ret_sub != RET_OK:
            print(f"订阅逐笔数据失败: {err_message}")
            return None

        import time
        time.sleep(1)

        # 获取逐笔交易数据
        ret, data = quote_ctx.get_rt_ticker(stock_code, num)

        if ret == RET_OK and isinstance(data, pd.DataFrame) and not data.empty:
            tick_list = []

            for _, row in data.iterrows():
                # 解析交易类型
                trade_type = '未知'
                if 'type' in data.columns:
                    raw_type = str(row['type'])
                    type_map = {
                        'AUTO_MATCH': '自动撮合',
                        'AUCTION': '竞价交易',
                        'ODD_LOT': '碎股交易',
                        'OVERSEAS': '海外交易',
                        'BULK': '大宗交易'
                    }
                    trade_type = type_map.get(raw_type, raw_type)

                # 解析交易方向
                direction = '未知'
                if 'ticker_direction' in data.columns:
                    raw_direction = str(row['ticker_direction'])
                    direction_map = {
                        'BUY': '买盘(外盘)',
                        'SELL': '卖盘(内盘)',
                        'NEUTRAL': '中性盘'
                    }
                    direction = direction_map.get(raw_direction, raw_direction)

                tick_info = {
                    '时间': str(row['time']),
                    '价格': float(row['price']),
                    '成交量': int(row['volume']),
                    '成交额': float(row['turnover']),
                    '交易类型': trade_type,
                    '交易方向': direction
                }

                tick_list.append(tick_info)

            return tick_list
        else:
            print(f"获取逐笔交易数据失败: {data}")
            return None

    except Exception as e:
        print(f"获取逐笔交易数据异常: {e}")
        return None
    finally:
        quote_ctx.close()

# 使用示例
ticks = get_tick_data('HK.08406', 10)
if ticks:
    for i, tick in enumerate(ticks, 1):
        print(f"第{i}笔: {tick}")
```

### 3. 买卖盘数据

#### 获取买卖盘档位信息

```python
def get_orderbook_data(stock_code):
    """获取买卖盘数据"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 订阅买卖盘数据
        ret_sub, err_message = quote_ctx.subscribe([stock_code], [SubType.ORDER_BOOK])
        if ret_sub != RET_OK:
            print(f"订阅买卖盘数据失败: {err_message}")
            return None

        import time
        time.sleep(1)

        # 获取买卖盘数据
        ret, data = quote_ctx.get_order_book(stock_code)

        if ret == RET_OK and isinstance(data, dict):
            orderbook_info = {
                '股票代码': data.get('code', ''),
                '股票名称': data.get('name', ''),
                '买盘数据': [],
                '卖盘数据': []
            }

            # 处理买盘数据
            bid_list = data.get('Bid', [])
            for i, bid_info in enumerate(bid_list[:10], 1):  # 取前10档
                if len(bid_info) >= 3:
                    bid_data = {
                        '档位': i,
                        '价格': float(bid_info[0]),
                        '数量': int(bid_info[1]),
                        '笔数': int(bid_info[2])
                    }
                    orderbook_info['买盘数据'].append(bid_data)

            # 处理卖盘数据
            ask_list = data.get('Ask', [])
            for i, ask_info in enumerate(ask_list[:10], 1):  # 取前10档
                if len(ask_info) >= 3:
                    ask_data = {
                        '档位': i,
                        '价格': float(ask_info[0]),
                        '数量': int(ask_info[1]),
                        '笔数': int(ask_info[2])
                    }
                    orderbook_info['卖盘数据'].append(ask_data)

            return orderbook_info
        else:
            print(f"获取买卖盘数据失败: {data}")
            return None

    except Exception as e:
        print(f"获取买卖盘数据异常: {e}")
        return None
    finally:
        quote_ctx.close()

# 使用示例
orderbook = get_orderbook_data('HK.08406')
if orderbook:
    print(f"股票: {orderbook['股票名称']} ({orderbook['股票代码']})")
    print("\n买盘数据:")
    for bid in orderbook['买盘数据'][:5]:
        print(f"  档位{bid['档位']}: {bid['价格']:.3f} × {bid['数量']:,} ({bid['笔数']}笔)")

    print("\n卖盘数据:")
    for ask in orderbook['卖盘数据'][:5]:
        print(f"  档位{ask['档位']}: {ask['价格']:.3f} × {ask['数量']:,} ({ask['笔数']}笔)")
```

### 4. 经纪队列数据

#### 获取买卖盘经纪信息

```python
def get_broker_data(stock_code):
    """获取买卖盘经纪数据"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 订阅经纪队列数据
        ret_sub, err_message = quote_ctx.subscribe([stock_code], [SubType.BROKER])
        if ret_sub != RET_OK:
            print(f"订阅经纪队列数据失败: {err_message}")
            return None

        import time
        time.sleep(1)

        # 获取经纪队列数据
        ret, ask_data, bid_data = quote_ctx.get_broker_queue(stock_code)

        if ret == RET_OK:
            broker_info = {
                '股票代码': stock_code,
                '买盘经纪': [],
                '卖盘经纪': []
            }

            # 处理买盘经纪数据（显示卖方经纪信息）
            if isinstance(bid_data, pd.DataFrame) and not bid_data.empty:
                for _, row in bid_data.head(10).iterrows():
                    broker_data = {
                        '经纪ID': str(row['ask_broker_id']) if 'ask_broker_id' in bid_data.columns else '未知',
                        '经纪名称': str(row['ask_broker_name']) if 'ask_broker_name' in bid_data.columns else '未知',
                        '队列位置': str(row['ask_broker_pos']) if 'ask_broker_pos' in bid_data.columns else '未知'
                    }
                    broker_info['买盘经纪'].append(broker_data)

            # 处理卖盘经纪数据（显示买方经纪信息）
            if isinstance(ask_data, pd.DataFrame) and not ask_data.empty:
                for _, row in ask_data.head(10).iterrows():
                    broker_data = {
                        '经纪ID': str(row['bid_broker_id']) if 'bid_broker_id' in ask_data.columns else '未知',
                        '经纪名称': str(row['bid_broker_name']) if 'bid_broker_name' in ask_data.columns else '未知',
                        '队列位置': str(row['bid_broker_pos']) if 'bid_broker_pos' in ask_data.columns else '未知'
                    }
                    broker_info['卖盘经纪'].append(broker_data)

            return broker_info
        else:
            print(f"获取经纪队列数据失败: {ask_data}")
            return None

    except Exception as e:
        print(f"获取经纪队列数据异常: {e}")
        return None
    finally:
        quote_ctx.close()

# 使用示例
brokers = get_broker_data('HK.08406')
if brokers:
    print(f"股票: {brokers['股票代码']}")

    print("\n买盘经纪 (对手方卖方经纪):")
    for broker in brokers['买盘经纪'][:5]:
        print(f"  {broker['经纪ID']}: {broker['经纪名称']} (位置: {broker['队列位置']})")

    print("\n卖盘经纪 (对手方买方经纪):")
    for broker in brokers['卖盘经纪'][:5]:
        print(f"  {broker['经纪ID']}: {broker['经纪名称']} (位置: {broker['队列位置']})")
```

---

## 🔧 完整示例代码

### 综合数据获取类

```python
class FutuDataFetcher:
    """富途数据获取器"""

    def __init__(self, host='127.0.0.1', port=11111):
        self.host = host
        self.port = port

    def get_all_data(self, stock_code):
        """获取股票的所有数据"""
        print(f"正在获取 {stock_code} 的完整数据...")

        # 1. 基本行情
        print("\n1. 获取基本行情...")
        quote = get_basic_quote(stock_code)

        # 2. 逐笔交易
        print("\n2. 获取逐笔交易数据...")
        ticks = get_tick_data(stock_code, 10)

        # 3. 买卖盘
        print("\n3. 获取买卖盘数据...")
        orderbook = get_orderbook_data(stock_code)

        # 4. 经纪队列
        print("\n4. 获取经纪队列数据...")
        brokers = get_broker_data(stock_code)

        return {
            'quote': quote,
            'ticks': ticks,
            'orderbook': orderbook,
            'brokers': brokers
        }

    def display_summary(self, data):
        """显示数据摘要"""
        if data['quote']:
            quote = data['quote']
            print(f"\n📊 {quote['股票名称']} ({quote['股票代码']})")
            print(f"最新价: {quote['最新价格']:.3f} | 涨跌幅: {quote['涨跌幅']:.2f}%")
            print(f"成交量: {quote['成交量']:,} | 成交额: {quote['成交额']:,.2f}")

        if data['ticks']:
            print(f"\n📈 最新逐笔交易: {len(data['ticks'])}笔")
            latest_tick = data['ticks'][0]
            print(f"最新: {latest_tick['价格']:.3f} × {latest_tick['成交量']:,} ({latest_tick['交易方向']})")

        if data['orderbook']:
            orderbook = data['orderbook']
            if orderbook['买盘数据'] and orderbook['卖盘数据']:
                best_bid = orderbook['买盘数据'][0]['价格']
                best_ask = orderbook['卖盘数据'][0]['价格']
                spread = best_ask - best_bid
                print(f"\n💰 买卖价差: {best_bid:.3f} - {best_ask:.3f} = {spread:.3f}")

        if data['brokers']:
            brokers = data['brokers']
            bid_count = len(brokers['买盘经纪'])
            ask_count = len(brokers['卖盘经纪'])
            print(f"\n🏢 参与经纪: 买盘{bid_count}家 | 卖盘{ask_count}家")

# 使用示例
if __name__ == "__main__":
    fetcher = FutuDataFetcher()

    # 获取08406的所有数据
    all_data = fetcher.get_all_data('HK.08406')

    # 显示摘要
    fetcher.display_summary(all_data)
```

---

## ❗ 常见问题解决

### 1. 连接问题

#### 问题：连接失败或超时

```python
# 解决方案：检查连接状态
def diagnose_connection():
    """诊断连接问题"""
    print("🔍 诊断富途OpenAPI连接...")

    # 1. 检查富途牛牛是否运行
    print("1. 请确认富途牛牛客户端已启动并登录")

    # 2. 检查OpenAPI是否开启
    print("2. 请确认已开启OpenAPI功能")
    print("   路径: 富途牛牛 → 设置 → 开发者选项 → 启用OpenAPI")

    # 3. 测试连接
    try:
        quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        ret, data = quote_ctx.get_stock_quote(['HK.00700'])
        if ret == RET_OK:
            print("✅ 连接测试成功")
        else:
            print(f"❌ 连接测试失败: {data}")
        quote_ctx.close()
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        print("💡 建议检查防火墙设置和端口11111是否被占用")

# 运行诊断
diagnose_connection()
```

### 2. 数据订阅问题

#### 问题：订阅失败或数据为空

```python
def check_subscription_limits():
    """检查订阅限制"""
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 获取订阅限制信息
        ret, data = quote_ctx.get_global_state()
        if ret == RET_OK:
            print("📊 当前订阅状态:")
            print(f"市场状态: {data}")

        # 检查订阅数量限制
        print("\n📋 订阅限制说明:")
        print("- 免费账户: 最多订阅10只股票")
        print("- 付费账户: 根据套餐不同有不同限制")
        print("- 建议及时取消不需要的订阅")

    except Exception as e:
        print(f"检查订阅状态异常: {e}")
    finally:
        quote_ctx.close()
```

### 3. 数据字段问题

#### 问题：某些字段显示为空或 N/A

```python
def handle_missing_data(data, field_name, default_value="未知"):
    """处理缺失数据"""
    try:
        if field_name in data and data[field_name] is not None:
            value = data[field_name]
            if str(value).upper() in ['N/A', 'NAN', 'NONE', '']:
                return default_value
            return value
        return default_value
    except:
        return default_value

# 使用示例
def safe_get_broker_name(row, column_name):
    """安全获取经纪商名称"""
    return handle_missing_data(row, column_name, "未知经纪商")
```

### 4. 权限问题

#### 问题：某些数据需要更高权限

```python
def check_data_permissions():
    """检查数据权限"""
    print("🔐 数据权限说明:")
    print("\n基础数据 (免费):")
    print("- 基本行情 (价格、成交量等)")
    print("- 逐笔交易 (交易时间、价格、数量)")
    print("- 买卖盘 (价格档位、总量)")

    print("\n高级数据 (可能需要付费):")
    print("- 详细经纪商信息")
    print("- 具体订单数量")
    print("- 实时Level 2数据")

    print("\n💡 如需更多数据权限:")
    print("1. 联系富途客服升级账户")
    print("2. 申请相应的市场数据权限")
    print("3. 确认数据使用合规性")
```

---

## 🎯 最佳实践

### 1. 连接管理

```python
from contextlib import contextmanager

@contextmanager
def futu_connection(host='127.0.0.1', port=11111):
    """富途连接上下文管理器"""
    quote_ctx = OpenQuoteContext(host=host, port=port)
    try:
        yield quote_ctx
    finally:
        quote_ctx.close()

# 使用示例
def get_quote_safe(stock_code):
    """安全获取行情数据"""
    with futu_connection() as ctx:
        ret_sub, _ = ctx.subscribe([stock_code], [SubType.QUOTE])
        if ret_sub != RET_OK:
            return None

        import time
        time.sleep(1)

        ret, data = ctx.get_stock_quote([stock_code])
        return data if ret == RET_OK else None
```

### 2. 错误处理

```python
def robust_data_fetch(fetch_function, *args, max_retries=3, **kwargs):
    """健壮的数据获取函数"""
    for attempt in range(max_retries):
        try:
            result = fetch_function(*args, **kwargs)
            if result is not None:
                return result
        except Exception as e:
            print(f"第{attempt + 1}次尝试失败: {e}")
            if attempt < max_retries - 1:
                import time
                time.sleep(1)  # 等待1秒后重试

    print(f"获取数据失败，已重试{max_retries}次")
    return None

# 使用示例
quote_data = robust_data_fetch(get_basic_quote, 'HK.08406')
```

### 3. 数据缓存

```python
import time
from functools import wraps

def cache_data(expire_seconds=60):
    """数据缓存装饰器"""
    cache = {}

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            key = f"{func.__name__}_{args}_{kwargs}"
            current_time = time.time()

            if key in cache:
                data, timestamp = cache[key]
                if current_time - timestamp < expire_seconds:
                    print(f"使用缓存数据: {key}")
                    return data

            # 获取新数据
            result = func(*args, **kwargs)
            cache[key] = (result, current_time)
            return result

        return wrapper
    return decorator

# 使用示例
@cache_data(expire_seconds=30)
def get_cached_quote(stock_code):
    """带缓存的行情获取"""
    return get_basic_quote(stock_code)
```

### 4. 批量数据处理

```python
def batch_get_quotes(stock_codes, batch_size=5):
    """批量获取多只股票行情"""
    results = {}

    for i in range(0, len(stock_codes), batch_size):
        batch = stock_codes[i:i + batch_size]
        print(f"处理批次 {i//batch_size + 1}: {batch}")

        with futu_connection() as ctx:
            # 批量订阅
            ret_sub, _ = ctx.subscribe(batch, [SubType.QUOTE])
            if ret_sub != RET_OK:
                continue

            import time
            time.sleep(1)

            # 批量获取
            ret, data = ctx.get_stock_quote(batch)
            if ret == RET_OK and isinstance(data, pd.DataFrame):
                for _, row in data.iterrows():
                    code = row['code']
                    results[code] = {
                        '股票代码': code,
                        '最新价格': float(row['last_price']),
                        '涨跌幅': float(row['change_rate'])
                    }

        # 避免请求过于频繁
        time.sleep(0.5)

    return results

# 使用示例
stock_list = ['HK.08406', 'HK.00700', 'HK.00005', 'HK.00001']
batch_results = batch_get_quotes(stock_list)
for code, data in batch_results.items():
    print(f"{code}: {data['最新价格']:.3f} ({data['涨跌幅']:+.2f}%)")
```

### 5. 实时数据监控

```python
class RealTimeMonitor:
    """实时数据监控器"""

    def __init__(self, stock_codes):
        self.stock_codes = stock_codes
        self.running = False

    def start_monitoring(self, interval=5):
        """开始监控"""
        self.running = True
        print(f"开始监控 {len(self.stock_codes)} 只股票...")

        while self.running:
            try:
                for code in self.stock_codes:
                    quote = get_basic_quote(code)
                    if quote:
                        print(f"{quote['股票代码']}: {quote['最新价格']:.3f} "
                              f"({quote['涨跌幅']:+.2f}%) - {time.strftime('%H:%M:%S')}")

                time.sleep(interval)

            except KeyboardInterrupt:
                print("\n监控已停止")
                break
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(interval)

    def stop_monitoring(self):
        """停止监控"""
        self.running = False

# 使用示例
monitor = RealTimeMonitor(['HK.08406', 'HK.00700'])
# monitor.start_monitoring(interval=10)  # 每10秒更新一次
```

---

## 📚 总结

本文档涵盖了富途 OpenAPI 的核心使用方法，包括：

1. **环境准备**: 安装配置和连接设置
2. **数据订阅**: 理解订阅机制和类型
3. **数据获取**: 基本行情、逐笔交易、买卖盘、经纪队列
4. **问题解决**: 常见问题的诊断和解决方案
5. **最佳实践**: 连接管理、错误处理、缓存、批量处理

### 🎯 关键要点

-   **必须先订阅再获取数据**
-   **及时关闭连接避免资源泄露**
-   **处理数据时要考虑异常情况**
-   **注意 API 调用频率限制**
-   **了解不同数据的权限要求**

### 🔗 相关资源

-   [富途 OpenAPI 官方文档](https://openapi.futunn.com/)
-   [Python SDK 文档](https://futunnopen.github.io/futu-api-doc/)
-   [富途开发者社区](https://q.futunn.com/developer)

---

_最后更新: 2025-07-13_
