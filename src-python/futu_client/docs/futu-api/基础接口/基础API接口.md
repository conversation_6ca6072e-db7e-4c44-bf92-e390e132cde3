# 基础接口

基础接口提供了系统级别的功能，包括市场状态查询、交易日历、股票基本信息等，这些接口为其他功能提供基础支持。

## 系统状态接口

### get_global_state - 获取全局状态

#### 接口原型

```python
get_global_state()
```

#### 功能说明

获取OpenD的全局状态信息，包括服务器时间、连接状态等。

#### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | dict | 全局状态信息 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取全局状态
    ret, data = quote_ctx.get_global_state()
    
    if ret == ft.RET_OK:
        print("OpenD全局状态:")
        print(f"服务器时间: {data}")
        print("连接状态: 正常")
    else:
        print('获取全局状态失败:', data)
        
finally:
    quote_ctx.close()
```

### get_market_state - 获取市场状态

#### 接口原型

```python
get_market_state(code_list)
```

#### 功能说明

获取指定市场的交易状态信息。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code_list | list | 是 | 市场代码列表，如['HK', 'US', 'CN'] |

#### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | DataFrame | 市场状态DataFrame |

#### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| market | str | 市场代码 |
| market_state | str | 市场状态 |
| market_time | str | 市场当前时间 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取多个市场状态
    ret, data = quote_ctx.get_market_state(['HK', 'US', 'CN'])
    
    if ret == ft.RET_OK:
        print("市场交易状态:")
        print("=" * 40)
        
        for _, row in data.iterrows():
            market = row['market']
            state = row['market_state']
            market_time = row['market_time']
            
            # 状态映射
            state_map = {
                'TRADING': '交易中',
                'PRE_MARKET_TRADING': '盘前交易',
                'AFTER_MARKET_TRADING': '盘后交易',
                'CLOSED': '休市',
                'OPENING_AUCTION': '开盘竞价',
                'CLOSING_AUCTION': '收盘竞价',
                'SUSPENDED': '暂停交易'
            }
            
            state_cn = state_map.get(state, state)
            print(f"{market}市场: {state_cn} ({market_time})")
    else:
        print('获取市场状态失败:', data)
        
finally:
    quote_ctx.close()
```

## 股票信息接口

### get_stock_basicinfo - 获取股票基本信息

#### 接口原型

```python
get_stock_basicinfo(market, stock_type=SecurityType.STOCK, code_list=None)
```

#### 功能说明

获取指定市场的股票基本信息列表。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| market | str | 是 | 市场代码，如'HK'、'US'、'CN' |
| stock_type | SecurityType | 否 | 证券类型，默认STOCK |
| code_list | list | 否 | 指定股票代码列表 |

#### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | DataFrame | 股票基本信息DataFrame |

#### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| name | str | 股票名称 |
| lot_size | int | 每手股数 |
| stock_type | str | 股票类型 |
| stock_child_type | str | 股票子类型 |
| owner_market_of_stock | str | 所属市场 |
| stock_id | str | 股票ID |
| listing_date | str | 上市日期 |
| stock_status | str | 股票状态 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取港股基本信息（前100只）
    ret, data = quote_ctx.get_stock_basicinfo('HK')
    
    if ret == ft.RET_OK:
        print(f"获取到 {len(data)} 只港股信息:")
        print("=" * 80)
        print("代码        名称                每手    上市日期    状态")
        print("-" * 80)
        
        # 显示前20只股票
        for _, row in data.head(20).iterrows():
            code = row['code']
            name = row['name'][:10]  # 限制名称长度
            lot_size = row['lot_size']
            listing_date = row['listing_date']
            status = row['stock_status']
            
            print(f"{code:<10} {name:<15} {lot_size:>6} {listing_date:>12} {status}")
        
        # 统计信息
        print(f"\n统计信息:")
        print(f"总股票数: {len(data)}")
        print(f"正常交易: {len(data[data['stock_status'] == 'NORMAL'])}")
        print(f"停牌股票: {len(data[data['stock_status'] == 'SUSPEND'])}")
        
    else:
        print('获取股票基本信息失败:', data)
        
finally:
    quote_ctx.close()
```

### get_stock_filter - 股票筛选

#### 接口原型

```python
get_stock_filter(market, filter_list=[], plate_code_list=[], begin=0, num=200)
```

#### 功能说明

根据指定条件筛选股票。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| market | str | 是 | 市场代码 |
| filter_list | list | 否 | 筛选条件列表 |
| plate_code_list | list | 否 | 板块代码列表 |
| begin | int | 否 | 数据起始位置 |
| num | int | 否 | 获取数量，最大200 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 创建筛选条件
    filter_list = [
        # 市值大于100亿港元
        ft.SimpleFilter(ft.StockField.MARKET_VAL, ft.FilterOperator.GT, 10000000000),
        # 成交额大于1亿港元
        ft.SimpleFilter(ft.StockField.TURNOVER, ft.FilterOperator.GT, 100000000),
        # 涨跌幅在-5%到5%之间
        ft.SimpleFilter(ft.StockField.CHANGE_RATE, ft.FilterOperator.BETWEEN, -5, 5)
    ]
    
    # 执行筛选
    ret, data = quote_ctx.get_stock_filter('HK', filter_list=filter_list, num=50)
    
    if ret == ft.RET_OK:
        print(f"筛选结果: {len(data)} 只股票")
        print("=" * 100)
        print("代码        名称            最新价    涨跌幅    成交额        市值")
        print("-" * 100)
        
        for _, row in data.iterrows():
            code = row['code']
            name = row['stock_name'][:8]
            price = row['last_price']
            change_rate = row['change_rate']
            turnover = row['turnover']
            market_val = row['market_val']
            
            print(f"{code:<10} {name:<12} {price:>8.2f} {change_rate:>7.2f}% "
                  f"{turnover:>12,.0f} {market_val:>12,.0f}")
    else:
        print('股票筛选失败:', data)
        
finally:
    quote_ctx.close()
```

## 交易日历接口

### get_trading_days - 获取交易日历

#### 接口原型

```python
get_trading_days(market, start=None, end=None)
```

#### 功能说明

获取指定市场的交易日历信息。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| market | str | 是 | 市场代码 |
| start | str | 否 | 开始日期，格式'YYYY-MM-DD' |
| end | str | 否 | 结束日期，格式'YYYY-MM-DD' |

#### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | DataFrame | 交易日历DataFrame |

#### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| time | str | 日期 |
| trade_date_type | str | 日期类型（WHOLE/MORNING/AFTERNOON） |

#### 代码示例

```python
import futu as ft
from datetime import datetime, timedelta

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取本月的交易日历
    today = datetime.now()
    start_date = today.replace(day=1).strftime('%Y-%m-%d')
    end_date = (today.replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)
    end_date = end_date.strftime('%Y-%m-%d')
    
    ret, data = quote_ctx.get_trading_days('HK', start=start_date, end=end_date)
    
    if ret == ft.RET_OK:
        print(f"港股交易日历 ({start_date} 至 {end_date}):")
        print("=" * 50)
        
        # 分类统计
        full_days = data[data['trade_date_type'] == 'WHOLE']
        half_days = data[data['trade_date_type'].isin(['MORNING', 'AFTERNOON'])]
        
        print("完整交易日:")
        for _, row in full_days.iterrows():
            date = row['time']
            weekday = datetime.strptime(date, '%Y-%m-%d').strftime('%A')
            print(f"  {date} ({weekday})")
        
        if not half_days.empty:
            print(f"\n半日交易:")
            for _, row in half_days.iterrows():
                date = row['time']
                trade_type = row['trade_date_type']
                print(f"  {date} ({trade_type})")
        
        print(f"\n统计:")
        print(f"完整交易日: {len(full_days)} 天")
        print(f"半日交易: {len(half_days)} 天")
        print(f"总交易日: {len(data)} 天")
        
    else:
        print('获取交易日历失败:', data)
        
finally:
    quote_ctx.close()
```

## 板块信息接口

### get_plate_list - 获取板块列表

#### 接口原型

```python
get_plate_list(market, plate_class)
```

#### 功能说明

获取指定市场的板块列表。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| market | str | 是 | 市场代码 |
| plate_class | PlateClass | 是 | 板块分类 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取港股行业板块
    ret, data = quote_ctx.get_plate_list('HK', ft.PlateClass.INDUSTRY)
    
    if ret == ft.RET_OK:
        print("港股行业板块:")
        print("=" * 60)
        print("板块代码    板块名称")
        print("-" * 60)
        
        for _, row in data.iterrows():
            plate_code = row['plate_code']
            plate_name = row['plate_name']
            print(f"{plate_code:<12} {plate_name}")
        
        print(f"\n共 {len(data)} 个行业板块")
        
    else:
        print('获取板块列表失败:', data)
        
finally:
    quote_ctx.close()
```

### get_plate_stock - 获取板块成分股

#### 接口原型

```python
get_plate_stock(plate_code)
```

#### 功能说明

获取指定板块的成分股列表。

#### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| plate_code | str | 是 | 板块代码 |

#### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取某个板块的成分股（以科技股为例）
    ret, data = quote_ctx.get_plate_stock('HK.BK1001')  # 科技股板块
    
    if ret == ft.RET_OK:
        print("科技股板块成分股:")
        print("=" * 60)
        print("股票代码    股票名称            权重")
        print("-" * 60)
        
        for _, row in data.iterrows():
            code = row['code']
            name = row['stock_name'][:12]  # 限制名称长度
            weight = row.get('weight', 0)  # 权重可能不存在
            
            print(f"{code:<10} {name:<15} {weight:>8.2f}%")
        
        print(f"\n成分股数量: {len(data)}")
        
    else:
        print('获取板块成分股失败:', data)
        
finally:
    quote_ctx.close()
```

## 系统工具接口

### 连接状态检测

```python
import futu as ft
import time

def check_connection_status():
    """检测连接状态"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        print("正在检测OpenD连接状态...")
        
        # 1. 检测基本连接
        start_time = time.time()
        ret, data = quote_ctx.get_global_state()
        response_time = (time.time() - start_time) * 1000
        
        if ret == ft.RET_OK:
            print(f"✅ OpenD连接正常 (响应时间: {response_time:.1f}ms)")
        else:
            print(f"❌ OpenD连接失败: {data}")
            return False
        
        # 2. 检测市场连接
        ret, market_data = quote_ctx.get_market_state(['HK', 'US'])
        if ret == ft.RET_OK:
            print("✅ 市场数据连接正常")
            for _, row in market_data.iterrows():
                print(f"   {row['market']}市场: {row['market_state']}")
        else:
            print(f"⚠️  市场数据获取异常: {market_data}")
        
        # 3. 检测订阅功能
        test_code = 'HK.00700'
        ret, err_msg = quote_ctx.subscribe([test_code], [ft.SubType.QUOTE])
        if ret == ft.RET_OK:
            print("✅ 数据订阅功能正常")
            
            # 测试数据获取
            ret, quote_data = quote_ctx.get_stock_quote([test_code])
            if ret == ft.RET_OK:
                print(f"✅ 行情数据获取正常 ({test_code}: {quote_data['last_price'].iloc[0]:.2f})")
            else:
                print(f"⚠️  行情数据获取失败: {quote_data}")
        else:
            print(f"❌ 数据订阅失败: {err_msg}")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接检测异常: {e}")
        return False
    
    finally:
        quote_ctx.close()

# 运行连接检测
if __name__ == "__main__":
    check_connection_status()
```

### 系统信息收集

```python
import futu as ft
import platform
import psutil
from datetime import datetime

def collect_system_info():
    """收集系统信息"""
    info = {
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'system': {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': psutil.virtual_memory().total // (1024**3),  # GB
            'memory_available': psutil.virtual_memory().available // (1024**3)  # GB
        },
        'futu_api': {
            'version': ft.__version__ if hasattr(ft, '__version__') else 'Unknown'
        },
        'opend_status': None,
        'market_status': {}
    }
    
    # 检测OpenD状态
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            info['opend_status'] = 'Connected'
        else:
            info['opend_status'] = 'Disconnected'
        
        # 获取市场状态
        ret, market_data = quote_ctx.get_market_state(['HK', 'US', 'CN'])
        if ret == ft.RET_OK:
            for _, row in market_data.iterrows():
                info['market_status'][row['market']] = row['market_state']
    
    except Exception as e:
        info['opend_status'] = f'Error: {e}'
    
    finally:
        quote_ctx.close()
    
    return info

def print_system_report():
    """打印系统报告"""
    info = collect_system_info()
    
    print("=== 系统信息报告 ===")
    print(f"生成时间: {info['timestamp']}")
    
    print(f"\n系统环境:")
    print(f"  操作系统: {info['system']['platform']}")
    print(f"  Python版本: {info['system']['python_version']}")
    print(f"  CPU核心数: {info['system']['cpu_count']}")
    print(f"  内存总量: {info['system']['memory_total']}GB")
    print(f"  可用内存: {info['system']['memory_available']}GB")
    
    print(f"\nFutu API:")
    print(f"  版本: {info['futu_api']['version']}")
    
    print(f"\nOpenD状态:")
    print(f"  连接状态: {info['opend_status']}")
    
    if info['market_status']:
        print(f"\n市场状态:")
        for market, status in info['market_status'].items():
            print(f"  {market}市场: {status}")

# 运行系统报告
if __name__ == "__main__":
    print_system_report()
```

## 注意事项

### 1. 接口调用频率
- 基础接口一般没有严格的频率限制
- 建议缓存不经常变化的数据（如股票基本信息）
- 避免在循环中频繁调用

### 2. 数据缓存
- 股票基本信息、板块信息等变化较少，可以本地缓存
- 交易日历可以提前获取并缓存
- 市场状态需要实时获取

### 3. 错误处理
- 网络异常时基础接口可能失败
- 建议添加重试机制
- 记录错误日志便于排查

### 4. 性能优化
- 批量获取数据而不是单个获取
- 使用合适的数据结构存储结果
- 定期清理过期的缓存数据

基础接口为其他高级功能提供了重要的基础支持，合理使用这些接口可以提高系统的稳定性和效率。