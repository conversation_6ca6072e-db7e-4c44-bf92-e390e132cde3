# 富途牛牛 OpenAPI 文档

> 基于富途牛牛官方文档 v9.3 整理

## 概述

OpenAPI 量化接口，为您的程序化交易，提供丰富的行情和交易接口，满足每一位开发者的量化投资需求，助力您的宽客梦想。

OpenAPI 由 OpenD 和 Futu API 组成：

- **OpenD** 是 Futu API 的网关程序，运行于您的本地电脑或云端服务器，负责中转协议请求到富途后台，并将处理后的数据返回。
- **Futu API** 是富途为主流的编程语言（Python、Java、C#、C++、JavaScript）封装的 API SDK，以方便您调用，降低策略开发难度。

## 📁 文档结构

### 1. 介绍
- [OpenAPI 介绍](介绍/OpenAPI介绍.md)
- [权限和限制](介绍/权限和限制.md)
- [费用说明](介绍/费用说明.md)

### 2. 快速上手
- [可视化 OpenD](快速上手/可视化OpenD.md)
- [编程环境搭建](快速上手/编程环境搭建.md)
- [简易程序运行](快速上手/简易程序运行.md)
- [交易策略搭建示例](快速上手/交易策略搭建示例.md)

### 3. OpenD网关
- [概述](OpenD网关/概述.md)
- [命令行 OpenD](OpenD网关/命令行OpenD.md)
- [运维命令](OpenD网关/运维命令.md)

### 4. 行情接口
- [行情接口总览](行情接口/行情接口总览.md)
- [行情对象](行情接口/行情对象.md)

#### 4.1 实时行情
- **订阅**
  - [订阅反订阅](行情接口/实时行情/订阅/订阅反订阅.md)
  - [获取订阅状态](行情接口/实时行情/订阅/获取订阅状态.md)
- **推送回调**
  - [实时报价回调](行情接口/实时行情/推送回调/实时报价回调.md)
  - [实时摆盘回调](行情接口/实时行情/推送回调/实时摆盘回调.md)
  - [实时K线回调](行情接口/实时行情/推送回调/实时K线回调.md)
  - [实时分时回调](行情接口/实时行情/推送回调/实时分时回调.md)
  - [实时逐笔回调](行情接口/实时行情/推送回调/实时逐笔回调.md)
  - [实时经纪队列回调](行情接口/实时行情/推送回调/实时经纪队列回调.md)
- **拉取**
  - [获取快照](行情接口/实时行情/拉取/获取快照.md)
  - [获取实时报价](行情接口/实时行情/拉取/获取实时报价.md)
  - [获取实时摆盘](行情接口/实时行情/拉取/获取实时摆盘.md)
  - [获取实时K线](行情接口/实时行情/拉取/获取实时K线.md)
  - [获取实时分时](行情接口/实时行情/拉取/获取实时分时.md)
  - [获取实时逐笔](行情接口/实时行情/拉取/获取实时逐笔.md)
  - [获取实时经纪队列](行情接口/实时行情/拉取/获取实时经纪队列.md)

#### 4.2 基本数据
- [股票基本信息](行情接口/基本数据/股票基本信息.md)
- [市场快照](行情接口/基本数据/市场快照.md)
- [板块列表](行情接口/基本数据/板块列表.md)

#### 4.3 全市场筛选
- [条件选股](行情接口/全市场筛选/条件选股.md)
- [获取板块内股票列表](行情接口/全市场筛选/获取板块内股票列表.md)
- [获取板块列表](行情接口/全市场筛选/获取板块列表.md)
- [获取静态数据](行情接口/全市场筛选/获取静态数据.md)
- [获取IPO信息](行情接口/全市场筛选/获取IPO信息.md)
- [获取全局市场状态](行情接口/全市场筛选/获取全局市场状态.md)
- [获取交易日历](行情接口/全市场筛选/获取交易日历.md)

### 5. 交易接口
- [交易接口总览](交易接口/交易接口总览.md)
- [交易对象](交易接口/交易对象.md)

#### 5.1 账户管理
- [账户列表](交易接口/账户管理/账户列表.md)
- [解锁交易](交易接口/账户管理/解锁交易.md)

#### 5.2 资产持仓
- [资金查询](交易接口/资产持仓/资金查询.md)
- [持仓查询](交易接口/资产持仓/持仓查询.md)

#### 5.3 订单管理
- [下单](交易接口/下单.md)
- [修改订单](交易接口/修改订单.md)
- [撤单](交易接口/撤单.md)
- [订单列表查询](交易接口/订单列表查询.md)

#### 5.4 成交记录
- [成交记录查询](交易接口/成交记录查询.md)

### 6. 基础接口
- [基础API接口](基础接口/基础API接口.md)

### 7. 常见问题
- [常见问题解答](常见问题/常见问题解答.md)

### 8. 自定义教程
- [十分钟快速入门](自定义教程/十分钟快速入门.md)
- [完整入门教程](自定义教程/完整入门教程.md)
- [常见问题解答](自定义教程/常见问题解答.md)

## 🌟 使用建议

### 新手学习路径
1. 📖 先阅读 [OpenAPI介绍](介绍/OpenAPI介绍.md) 了解整体架构
2. 🚀 按照 [十分钟快速入门](自定义教程/十分钟快速入门.md) 快速上手
3. 🔧 配置 [编程环境搭建](快速上手/编程环境搭建.md)
4. 📊 学习 [行情接口](行情接口/行情接口总览.md) 获取数据
5. 💰 掌握 [交易接口](交易接口/交易接口总览.md) 执行交易

### 开发者参考路径
1. 📋 查看 [行情接口总览](行情接口/行情接口总览.md) 和 [交易接口总览](交易接口/交易接口总览.md)
2. 🔍 根据需要查找具体API接口文档
3. ❓ 遇到问题查看 [常见问题解答](常见问题/常见问题解答.md)

## 技术架构

### 系统特点

#### 全平台多语言
- **OpenD** 支持 Windows、MacOS、CentOS、Ubuntu
- **Futu API** 支持 Python、Java、C#、C++、JavaScript 等主流语言

#### 稳定极速免费
- 稳定的技术架构，直连交易所一触即达
- 下单最快只需 0.0014 s
- 通过 OpenAPI 交易无附加收费

#### 丰富的投资品类
- 支持美国、香港等多个市场的实时行情、实盘交易及模拟交易

#### 专业的机构服务
- 定制化的行情交易解决方案

## 支持的市场和品种

### 行情数据支持

| 市场 | 品种 | 牛牛用户 |
|------|------|----------|
| 香港市场 | 股票、ETFs、窝轮、牛熊、界内证 | ✓ |
| | 期权 | ✓ |
| | 期货 | ✓ |
| | 指数 | ✓ |
| | 板块 | ✓ |
| 美国市场 | 股票、ETFs | ✓ |
| | OTC 股票 | X |
| | 期权 | ✓ |
| | 期货 | ✓ |
| | 指数 | X |
| | 板块 | ✓ |
| A 股市场 | 股票、ETFs | ✓ |
| | 指数 | ✓ |
| | 板块 | ✓ |
| 新加坡市场 | 股票、ETFs、窝轮、REITs、DLCs | X |
| | 期货 | X |
| 日本市场 | 股票、ETFs、REITs | X |
| | 期货 | X |
| 澳大利亚市场 | 股票、ETFs | X |
| 环球市场 | 外汇 | X |

### 交易能力支持

| 市场 | 品种 | 模拟交易 | 真实交易 |
|------|------|----------|----------|
| 香港市场 | 股票、ETFs、窝轮、牛熊、界内证 | ✓ | ✓ |
| | 期权 | ✓ | ✓ |
| | 期货 | ✓ | ✓ |
| 美国市场 | 股票、ETFs | ✓ | ✓ |
| | 期权 | ✓ | ✓ |
| | 期货 | ✓ | ✓ |
| A 股市场 | A 股通股票 | ✓ | ✓ |
| | 非 A 股通股票 | ✓ | X |
| 新加坡市场 | 股票、ETFs、窝轮、REITs、DLCs | X | X |
| | 期货 | ✓ | ✓ |
| 日本市场 | 股票、ETFs、REITs | X | X |
| | 期货 | ✓ | ✓ |
| 澳大利亚市场 | 股票、ETFs | X | X |

## 账号体系

### 平台账号
平台账号是您在富途的用户 ID（牛牛号），此账号体系适用于富途牛牛 APP、OpenAPI。您可以使用平台账号（牛牛号）和登录密码，登录 OpenD 并获取行情。

### 综合账户
综合账户支持以多种货币在同一个账户内交易不同市场品类（港股、美股、A股通、基金）。您可以通过一个账户进行全市场交易，不需要再管理多个账户。

综合账户包括：
- **综合账户 - 证券**：用于交易全市场的股票、ETFs、期权等证券类产品
- **综合账户 - 期货**：用于交易全市场的期货产品，目前支持香港市场期货、美国市场 CME Group 期货、新加坡市场期货、日本市场期货

## 功能说明

### 行情功能

#### 行情数据获取方式
1. 订阅并接收实时报价、实时 K 线、实时逐笔、实时摆盘等数据推送
2. 拉取最新市场快照，历史 K 线等

### 交易功能

#### 交易方式
真实交易和模拟交易使用同一套交易接口。

## 快速开始

初次接触 OpenAPI，您需要进行如下两步操作：

1. **第一步**：在本地或云端安装并启动一个网关程序 [OpenD](快速上手/可视化OpenD.md)
   - OpenD 以自定义 TCP 协议的方式对外暴露接口，负责中转协议请求到富途服务器，并将处理后的数据返回，该协议接口与编程语言无关

2. **第二步**：下载 Futu API，完成[环境搭建](快速上手/编程环境搭建.md)，以便快速调用
   - 为方便您的使用，富途对主流的编程语言，封装了相应的 API SDK（以下简称 Futu API）

## 版本信息

- **文档版本**: v9.3
- **最后更新**: 2025年7月14日
- **数据来源**: 富途牛牛官方文档

---

*本文档基于富途牛牛官方 OpenAPI 文档整理，仅供学习和参考使用。如有疑问，请参考官方最新文档。*