# 权限和限制

使用富途OpenAPI需要了解相关的权限要求和使用限制，以确保合规使用和最佳体验。

## 账户权限要求

### 1. 基础权限

#### 富途账户要求
- **牛牛号**：需要有效的富途牛牛账号
- **账户状态**：账户状态正常，未被冻结或限制
- **实名认证**：完成实名认证
- **风险评估**：完成风险承受能力评估

#### OpenAPI权限开通
1. 登录富途牛牛APP
2. 进入"我的" -> "设置" -> "OpenAPI"
3. 阅读并同意《富途OpenAPI服务协议》
4. 完成开通流程

### 2. 行情权限

#### 基础行情权限

| 市场 | 数据类型 | 免费用户 | 付费用户 |
|------|----------|----------|----------|
| 港股 | 实时行情 | 延迟15分钟 | 实时 |
| 美股 | 实时行情 | 延迟15分钟 | 实时 |
| A股 | 实时行情 | 延迟15分钟 | 实时 |

#### 高级行情权限

```python
import futu as ft

# 查询用户行情权限
def check_market_authority():
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    markets = ['HK', 'US', 'CN']
    
    for market in markets:
        ret, data = quote_ctx.get_user_security(market)
        if ret == ft.RET_OK:
            print(f"{market}市场权限:")
            for _, security in data.iterrows():
                print(f"  {security['code']}: {security['name']}")
        else:
            print(f"{market}市场权限查询失败: {data}")
    
    quote_ctx.close()

# 检查订阅额度
def check_subscription_quota():
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_subscription_quota()
    if ret == ft.RET_OK:
        print("订阅额度信息:")
        for _, quota in data.iterrows():
            print(f"  {quota['sub_type']}: {quota['remain_quota']}/{quota['total_quota']}")
    else:
        print("订阅额度查询失败:", data)
    
    quote_ctx.close()
```

### 3. 交易权限

#### 基础交易权限

| 权限类型 | 要求 |
|----------|------|
| 模拟交易 | 开通OpenAPI即可 |
| 真实交易 | 需要开通对应市场的交易权限 |

#### 市场交易权限

```python
import futu as ft

# 检查交易权限
def check_trading_authority():
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        # 解锁交易
        ret, data = trd_ctx.unlock_trade("your_password")
        if ret != ft.RET_OK:
            print("交易解锁失败:", data)
            return
        
        # 获取账户列表
        ret, acc_list = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("交易账户权限:")
            for _, acc in acc_list.iterrows():
                print(f"账户 {acc['acc_id']}:")
                print(f"  环境: {acc['trd_env']}")
                print(f"  类型: {acc['acc_type']}")
                print(f"  市场权限: {acc['trd_market_auth']}")
        else:
            print("账户查询失败:", acc_list)
    
    finally:
        trd_ctx.close()
```

## 使用限制

### 1. 行情数据限制

#### 订阅数量限制

| 用户类型 | 港股订阅 | 美股订阅 | A股订阅 | 总订阅数 |
|----------|----------|----------|----------|----------|
| 免费用户 | 200只 | 100只 | 100只 | 400只 |
| 付费用户 | 1000只 | 500只 | 500只 | 2000只 |

#### 历史数据限制

```python
import futu as ft

# 历史数据请求限制示例
def request_history_with_limit():
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 单次请求最大数量限制
    max_count_per_request = 1000
    
    # 分页获取大量历史数据
    def get_large_history_data(code, start_date, end_date):
        all_data = []
        page_req_key = None
        
        while True:
            ret, data, page_req_key = quote_ctx.request_history_kline(
                code=code,
                start=start_date,
                end=end_date,
                max_count=max_count_per_request,
                page_req_key=page_req_key
            )
            
            if ret != ft.RET_OK:
                break
                
            all_data.append(data)
            
            if page_req_key is None:  # 没有更多数据
                break
        
        return all_data
    
    quote_ctx.close()
```

#### 请求频率限制

| 接口类型 | 频率限制 |
|----------|----------|
| 实时行情 | 30次/秒 |
| 历史数据 | 60次/分钟 |
| 订阅管理 | 20次/分钟 |

### 2. 交易限制

#### 下单频率限制

```python
import futu as ft
import time

class OrderRateLimiter:
    def __init__(self, max_orders_per_second=2):
        self.max_orders = max_orders_per_second
        self.order_times = []
    
    def can_place_order(self):
        current_time = time.time()
        
        # 移除1秒前的记录
        self.order_times = [t for t in self.order_times if current_time - t < 1.0]
        
        # 检查是否超过限制
        if len(self.order_times) >= self.max_orders:
            return False
        
        self.order_times.append(current_time)
        return True
    
    def wait_if_needed(self):
        while not self.can_place_order():
            time.sleep(0.1)

# 使用限频器
limiter = OrderRateLimiter(max_orders_per_second=2)

def safe_place_order(trd_ctx, **order_params):
    limiter.wait_if_needed()
    return trd_ctx.place_order(**order_params)
```

#### 账户资金限制

| 限制类型 | 说明 |
|----------|------|
| 最小交易金额 | 各市场有不同要求 |
| 保证金要求 | 融资融券交易需要足够保证金 |
| 当日交易次数 | PDT规则限制（美股） |

### 3. 技术限制

#### 连接数限制

```python
import futu as ft
from threading import Semaphore

class ConnectionManager:
    def __init__(self, max_connections=10):
        self.semaphore = Semaphore(max_connections)
        self.active_connections = []
    
    def get_quote_context(self):
        self.semaphore.acquire()
        ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
        self.active_connections.append(ctx)
        return ctx
    
    def release_quote_context(self, ctx):
        if ctx in self.active_connections:
            self.active_connections.remove(ctx)
            ctx.close()
            self.semaphore.release()
    
    def cleanup_all(self):
        for ctx in self.active_connections:
            ctx.close()
        self.active_connections.clear()
```

#### 数据缓存限制

| 缓存类型 | 限制 |
|----------|------|
| 本地缓存大小 | 默认100MB |
| 缓存过期时间 | 可配置，默认5分钟 |
| 内存使用 | 建议不超过系统内存的50% |

## 合规要求

### 1. 数据使用合规

#### 数据传播限制
- **个人使用**：仅限个人投资决策使用
- **商业使用**：需要获得相应的数据分销许可
- **数据存储**：不得长期存储实时行情数据
- **数据转发**：不得未经授权转发行情数据

#### 合规使用示例

```python
import futu as ft
from datetime import datetime, timedelta

class ComplianceManager:
    def __init__(self):
        self.data_retention_days = 30  # 数据保留天数
        self.allowed_users = []  # 授权用户列表
    
    def is_data_usage_compliant(self, user_id, data_type):
        """检查数据使用是否合规"""
        # 检查用户权限
        if user_id not in self.allowed_users:
            return False, "用户未授权"
        
        # 检查数据类型权限
        if data_type == 'real_time' and not self.has_realtime_permission(user_id):
            return False, "无实时数据权限"
        
        return True, "合规"
    
    def cleanup_old_data(self, data_timestamp):
        """清理过期数据"""
        cutoff_date = datetime.now() - timedelta(days=self.data_retention_days)
        if data_timestamp < cutoff_date:
            return True  # 应该删除
        return False
    
    def log_data_access(self, user_id, data_type, timestamp):
        """记录数据访问日志"""
        log_entry = {
            'user_id': user_id,
            'data_type': data_type,
            'timestamp': timestamp,
            'action': 'data_access'
        }
        # 记录到合规日志
        print(f"合规日志: {log_entry}")
```

### 2. 交易合规

#### 风险控制
- **下单前检查**：确保有足够资金或持仓
- **价格合理性**：避免明显偏离市价的订单
- **频率控制**：避免异常高频交易
- **监控异常**：及时发现和处理异常交易行为

#### 合规交易示例

```python
import futu as ft

class ComplianceTrading:
    def __init__(self, trd_ctx):
        self.trd_ctx = trd_ctx
        self.max_single_order_value = 1000000  # 单笔最大交易金额
        self.max_daily_trades = 100  # 日最大交易次数
        self.daily_trade_count = 0
    
    def compliance_check_order(self, price, qty, code):
        """订单合规检查"""
        order_value = price * qty
        
        # 检查单笔交易金额
        if order_value > self.max_single_order_value:
            return False, f"单笔交易金额超限: {order_value}"
        
        # 检查日交易次数
        if self.daily_trade_count >= self.max_daily_trades:
            return False, "日交易次数超限"
        
        # 检查价格合理性
        ret, quote_data = ft.OpenQuoteContext().get_stock_quote([code])
        if ret == ft.RET_OK:
            current_price = quote_data['last_price'].iloc[0]
            price_deviation = abs(price - current_price) / current_price
            
            if price_deviation > 0.1:  # 偏离超过10%
                return False, f"价格偏离过大: {price_deviation:.2%}"
        
        return True, "合规"
    
    def place_compliant_order(self, **order_params):
        """合规下单"""
        # 合规检查
        is_compliant, message = self.compliance_check_order(
            order_params['price'],
            order_params['qty'],
            order_params['code']
        )
        
        if not is_compliant:
            return ft.RET_ERROR, message
        
        # 执行下单
        ret, data = self.trd_ctx.place_order(**order_params)
        
        if ret == ft.RET_OK:
            self.daily_trade_count += 1
        
        return ret, data
```

## 权限升级

### 1. 行情权限升级

#### 申请实时行情权限
1. 登录富途牛牛APP
2. 进入"行情" -> "行情权限"
3. 选择需要的市场权限包
4. 完成支付流程

#### 权限包说明

| 权限包 | 包含市场 | 月费 | 年费 |
|--------|----------|------|------|
| 港股实时 | 香港市场实时行情 | ¥30 | ¥300 |
| 美股实时 | 美国市场实时行情 | ¥30 | ¥300 |
| A股实时 | A股市场实时行情 | ¥20 | ¥200 |

### 2. 交易权限升级

#### 开通交易权限
1. 完成开户流程
2. 通过风险评估
3. 满足相应市场的开户条件
4. 完成资金入金（真实交易）

### 3. API配额升级

联系富途客服申请更高的API配额：
- 更高的订阅数量限制
- 更高的请求频率限制
- 定制化解决方案

## 监控和告警

### 权限监控脚本

```python
import futu as ft
import time
from datetime import datetime

class PermissionMonitor:
    def __init__(self):
        self.last_check_time = None
        self.alerts = []
    
    def check_all_permissions(self):
        """检查所有权限状态"""
        alerts = []
        
        # 检查行情权限
        quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
        try:
            ret, quota = quote_ctx.get_subscription_quota()
            if ret == ft.RET_OK:
                for _, row in quota.iterrows():
                    usage_ratio = (row['total_quota'] - row['remain_quota']) / row['total_quota']
                    if usage_ratio > 0.8:  # 使用超过80%
                        alerts.append(f"{row['sub_type']} 订阅额度不足: {row['remain_quota']}/{row['total_quota']}")
        finally:
            quote_ctx.close()
        
        # 检查交易权限
        trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
        try:
            ret, acc_list = trd_ctx.get_acc_list()
            if ret != ft.RET_OK:
                alerts.append("交易权限检查失败，可能需要重新解锁")
        finally:
            trd_ctx.close()
        
        self.alerts = alerts
        self.last_check_time = datetime.now()
        
        return alerts
    
    def get_status_report(self):
        """获取权限状态报告"""
        current_alerts = self.check_all_permissions()
        
        report = f"""
权限监控报告 - {self.last_check_time.strftime('%Y-%m-%d %H:%M:%S')}
{'='*50}

当前告警数量: {len(current_alerts)}

"""
        
        if current_alerts:
            report += "告警详情:\n"
            for i, alert in enumerate(current_alerts, 1):
                report += f"{i}. {alert}\n"
        else:
            report += "✓ 所有权限状态正常\n"
        
        return report

# 使用示例
monitor = PermissionMonitor()
print(monitor.get_status_report())
```

通过了解和遵守这些权限要求和使用限制，您可以更好地使用富途OpenAPI，确保合规使用的同时获得最佳的使用体验。