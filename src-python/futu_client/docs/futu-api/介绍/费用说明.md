# 费用说明

富途OpenAPI提供免费和付费两种服务模式，满足不同用户的需求。本文档详细说明各项服务的费用结构。

## 费用概览

### 基础原则
- **API使用免费**：通过OpenAPI进行交易操作本身不收取额外费用
- **行情数据分级收费**：基础延迟行情免费，实时行情需要付费
- **交易佣金标准收费**：按照富途标准佣金收费，与APP交易相同
- **无隐藏费用**：所有费用透明，无额外隐藏收费

## 行情数据费用

### 1. 免费行情服务

#### 延迟行情（免费）
| 市场 | 延迟时间 | 数据类型 | 订阅限制 |
|------|----------|----------|----------|
| 港股 | 15分钟 | 基础报价、K线 | 200只股票 |
| 美股 | 15分钟 | 基础报价、K线 | 100只股票 |
| A股 | 15分钟 | 基础报价、K线 | 100只股票 |

#### 免费服务限制
```python
import futu as ft

def check_free_quota():
    """检查免费额度使用情况"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        ret, quota_data = quote_ctx.get_subscription_quota()
        if ret == ft.RET_OK:
            print("免费订阅额度使用情况:")
            for _, quota in quota_data.iterrows():
                used = quota['total_quota'] - quota['remain_quota']
                print(f"  {quota['sub_type']}: {used}/{quota['total_quota']} "
                      f"({used/quota['total_quota']*100:.1f}%)")
        else:
            print("查询额度失败:", quota_data)
    
    finally:
        quote_ctx.close()
```

### 2. 实时行情费用

#### 港股实时行情
| 套餐类型 | 月费 | 年费 | 优惠 | 包含内容 |
|----------|------|------|------|----------|
| 港股实时Level-1 | ¥30 | ¥300 | 年付8.3折 | 实时报价、实时K线、基础摆盘 |
| 港股实时Level-2 | ¥100 | ¥1000 | 年付8.3折 | Level-1 + 深度摆盘、逐笔成交 |

#### 美股实时行情
| 套餐类型 | 月费 | 年费 | 优惠 | 包含内容 |
|----------|------|------|------|----------|
| 美股实时Level-1 | ¥30 | ¥300 | 年付8.3折 | 实时报价、实时K线 |
| 美股实时Level-2 | ¥200 | ¥2000 | 年付8.3折 | Level-1 + 深度摆盘、逐笔成交 |

#### A股实时行情
| 套餐类型 | 月费 | 年费 | 优惠 | 包含内容 |
|----------|------|------|------|----------|
| A股实时行情 | ¥20 | ¥200 | 年付8.3折 | 沪深实时报价、实时K线 |

### 3. 组合套餐

#### 全市场套餐
| 套餐名称 | 月费 | 年费 | 节省 | 包含市场 |
|----------|------|------|------|----------|
| 全球实时Level-1 | ¥80 | ¥800 | ¥200/年 | 港股+美股+A股 Level-1 |
| 全球实时Level-2 | ¥300 | ¥3000 | ¥600/年 | 港股+美股+A股 Level-2 |

#### 费用计算示例

```python
def calculate_market_data_cost():
    """计算行情数据费用"""
    
    # 单独订阅费用
    individual_costs = {
        'HK_L1': {'monthly': 30, 'yearly': 300},
        'US_L1': {'monthly': 30, 'yearly': 300},
        'CN_L1': {'monthly': 20, 'yearly': 200},
        'HK_L2': {'monthly': 100, 'yearly': 1000},
        'US_L2': {'monthly': 200, 'yearly': 2000}
    }
    
    # 组合套餐费用
    package_costs = {
        'Global_L1': {'monthly': 80, 'yearly': 800},
        'Global_L2': {'monthly': 300, 'yearly': 3000}
    }
    
    print("=== 行情费用计算器 ===\n")
    
    # 计算港股+美股+A股 Level-1单独订阅
    total_individual_monthly = (individual_costs['HK_L1']['monthly'] + 
                               individual_costs['US_L1']['monthly'] + 
                               individual_costs['CN_L1']['monthly'])
    total_individual_yearly = (individual_costs['HK_L1']['yearly'] + 
                              individual_costs['US_L1']['yearly'] + 
                              individual_costs['CN_L1']['yearly'])
    
    print("单独订阅 vs 组合套餐 (Level-1):")
    print(f"单独订阅月费: ¥{total_individual_monthly}")
    print(f"组合套餐月费: ¥{package_costs['Global_L1']['monthly']}")
    print(f"月费节省: ¥{total_individual_monthly - package_costs['Global_L1']['monthly']}")
    
    print(f"\n单独订阅年费: ¥{total_individual_yearly}")
    print(f"组合套餐年费: ¥{package_costs['Global_L1']['yearly']}")
    print(f"年费节省: ¥{total_individual_yearly - package_costs['Global_L1']['yearly']}")

# 运行费用计算
calculate_market_data_cost()
```

## 交易费用

### 1. 基础交易费用

#### 佣金费率
| 市场 | 最低佣金 | 费率 | 备注 |
|------|----------|------|------|
| 港股 | HK$3 | 0.03% | 最低收费HK$3 |
| 美股 | US$0.99 | 无 | 按笔收费 |
| A股 | ¥5 | 0.03% | 最低收费¥5 |

#### 其他费用
| 费用类型 | 港股 | 美股 | A股 |
|----------|------|------|------|
| 平台使用费 | HK$15/笔 | 无 | 无 |
| 交收费 | HK$5/笔 | 无 | 无 |
| 印花税 | 0.1% | 无 | 0.1% |
| 交易征费 | 0.0027% | 无 | 无 |

### 2. 费用计算示例

```python
def calculate_trading_cost(market, trade_amount, shares=None):
    """计算交易费用"""
    
    if market == 'HK':
        # 港股费用计算
        commission_rate = 0.0003  # 0.03%
        commission = max(trade_amount * commission_rate, 3)  # 最低HK$3
        platform_fee = 15  # HK$15/笔
        settlement_fee = 5   # HK$5/笔
        stamp_duty = trade_amount * 0.001  # 0.1%
        trading_fee = trade_amount * 0.000027  # 0.0027%
        
        total_cost = commission + platform_fee + settlement_fee + stamp_duty + trading_fee
        
        return {
            'commission': commission,
            'platform_fee': platform_fee,
            'settlement_fee': settlement_fee,
            'stamp_duty': stamp_duty,
            'trading_fee': trading_fee,
            'total_cost': total_cost,
            'cost_ratio': total_cost / trade_amount * 100
        }
    
    elif market == 'US':
        # 美股费用计算
        commission = 0.99  # US$0.99/笔
        
        return {
            'commission': commission,
            'total_cost': commission,
            'cost_ratio': commission / trade_amount * 100 if trade_amount > 0 else 0
        }
    
    elif market == 'CN':
        # A股费用计算
        commission_rate = 0.0003  # 0.03%
        commission = max(trade_amount * commission_rate, 5)  # 最低¥5
        stamp_duty = trade_amount * 0.001 if trade_amount > 0 else 0  # 仅卖出收取
        
        total_cost = commission + stamp_duty
        
        return {
            'commission': commission,
            'stamp_duty': stamp_duty,
            'total_cost': total_cost,
            'cost_ratio': total_cost / trade_amount * 100
        }

# 使用示例
print("=== 交易费用计算示例 ===\n")

# 港股交易：买入10万港元腾讯
hk_cost = calculate_trading_cost('HK', 100000)
print("港股交易费用 (HK$100,000):")
for key, value in hk_cost.items():
    print(f"  {key}: HK${value:.2f}")

print()

# 美股交易：买入1万美元苹果
us_cost = calculate_trading_cost('US', 10000)
print("美股交易费用 (US$10,000):")
for key, value in us_cost.items():
    print(f"  {key}: US${value:.2f}")

print()

# A股交易：买入5万元茅台
cn_cost = calculate_trading_cost('CN', 50000)
print("A股交易费用 (¥50,000):")
for key, value in cn_cost.items():
    print(f"  {key}: ¥{value:.2f}")
```

### 3. 大客户优惠

#### VIP费率
| 月交易额 | 港股佣金 | 美股佣金 | 优惠幅度 |
|----------|----------|----------|----------|
| ≥100万 | 0.025% | US$0.79/笔 | 8.3折 |
| ≥500万 | 0.02% | US$0.69/笔 | 7折 |
| ≥1000万 | 协商 | 协商 | 定制 |

## 特殊服务费用

### 1. 期货期权

#### 期货交易费用
| 市场 | 费用结构 | 标准费率 |
|------|----------|----------|
| 港股期货 | 按合约价值 | 0.01% |
| 美股期货 | 按手数 | US$2.5/手 |

#### 期权交易费用
| 市场 | 费用结构 | 标准费率 |
|------|----------|----------|
| 港股期权 | 按合约价值 | 0.25% |
| 美股期权 | 按合约数 | US$0.65/张 |

### 2. 融资融券

#### 利息费率
| 融资类型 | 港股利率 | 美股利率 | 计息方式 |
|----------|----------|----------|----------|
| 融资买入 | 年利率6.8% | 年利率7.5% | 按日计息 |
| 融券卖出 | 根据个股 | 根据个股 | 按日计息 |

### 3. 数据服务

#### 高级数据服务
| 服务类型 | 费用 | 说明 |
|----------|------|------|
| 历史数据包 | ¥500/年 | 5年以上历史数据 |
| 实时新闻 | ¥200/月 | 实时财经资讯 |
| 研报数据 | ¥300/月 | 分析师研报 |

## 费用管理

### 1. 费用查询

```python
import futu as ft
from datetime import datetime, timedelta

def query_trading_fees():
    """查询交易费用"""
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        # 解锁交易
        ret, data = trd_ctx.unlock_trade("your_password")
        if ret != ft.RET_OK:
            print("交易解锁失败")
            return
        
        # 查询最近30天的交易记录
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        ret, orders = trd_ctx.order_list_query(
            start=start_date,
            end=end_date,
            status_filter_list=[ft.OrderStatus.FILLED_ALL],
            trd_env=ft.TrdEnv.REAL
        )
        
        if ret == ft.RET_OK:
            total_trade_value = 0
            total_fees = 0
            
            for _, order in orders.iterrows():
                trade_value = order['dealt_qty'] * order['dealt_avg_price']
                total_trade_value += trade_value
                
                # 估算费用（简化计算）
                if 'HK' in order['code']:
                    fees = max(trade_value * 0.0003, 3) + 15 + 5  # 港股费用估算
                else:
                    fees = 0.99  # 美股费用
                
                total_fees += fees
            
            print(f"最近30天交易统计:")
            print(f"  总交易额: {total_trade_value:,.2f}")
            print(f"  估算总费用: {total_fees:,.2f}")
            print(f"  费用比率: {total_fees/total_trade_value*100:.3f}%")
        
    finally:
        trd_ctx.close()
```

### 2. 费用优化建议

#### 降低交易成本的方法

```python
class TradingCostOptimizer:
    def __init__(self):
        self.min_trade_amounts = {
            'HK': 10000,   # 港股建议最小交易额
            'US': 1000,    # 美股建议最小交易额
            'CN': 10000    # A股建议最小交易额
        }
    
    def optimize_order_size(self, market, target_amount):
        """优化订单规模以降低成本"""
        min_amount = self.min_trade_amounts.get(market, 0)
        
        if target_amount < min_amount:
            return {
                'suggested_amount': min_amount,
                'reason': f'建议最小交易额{min_amount}以降低费用比率',
                'cost_improvement': self.calculate_cost_improvement(market, target_amount, min_amount)
            }
        
        return {
            'suggested_amount': target_amount,
            'reason': '当前交易额已达到较优水平',
            'cost_improvement': 0
        }
    
    def calculate_cost_improvement(self, market, original_amount, optimized_amount):
        """计算费用优化效果"""
        original_cost = calculate_trading_cost(market, original_amount)
        optimized_cost = calculate_trading_cost(market, optimized_amount)
        
        original_ratio = original_cost['cost_ratio']
        optimized_ratio = optimized_cost['cost_ratio']
        
        return original_ratio - optimized_ratio
    
    def batch_order_suggestion(self, orders):
        """批量订单优化建议"""
        suggestions = []
        
        for order in orders:
            market = 'HK' if 'HK' in order['code'] else 'US' if 'US' in order['code'] else 'CN'
            amount = order['price'] * order['qty']
            
            optimization = self.optimize_order_size(market, amount)
            if optimization['cost_improvement'] > 0:
                suggestions.append({
                    'code': order['code'],
                    'original_amount': amount,
                    'suggested_amount': optimization['suggested_amount'],
                    'cost_improvement': optimization['cost_improvement'],
                    'reason': optimization['reason']
                })
        
        return suggestions

# 使用示例
optimizer = TradingCostOptimizer()

# 示例订单
sample_orders = [
    {'code': 'HK.00700', 'price': 300, 'qty': 10},  # 小额港股订单
    {'code': 'US.AAPL', 'price': 150, 'qty': 5},     # 小额美股订单
]

suggestions = optimizer.batch_order_suggestion(sample_orders)
if suggestions:
    print("费用优化建议:")
    for suggestion in suggestions:
        print(f"  {suggestion['code']}: {suggestion['reason']}")
        print(f"    费用比率可改善: {suggestion['cost_improvement']:.3f}%")
```

## 费用透明度

### 1. 费用明细

所有费用都会在交易确认和账单中详细列出：
- 佣金费用
- 平台使用费
- 监管费用
- 税费

### 2. 无隐藏费用承诺

富途承诺：
- 所有费用事先明确告知
- 不收取隐藏费用
- 费用标准公开透明
- 提供详细的费用计算说明

### 3. 费用争议处理

如对费用有疑问：
1. 查看详细账单
2. 联系客服查询
3. 提供交易记录核实
4. 及时处理争议

通过合理的费用结构和透明的收费标准，富途OpenAPI为用户提供高性价比的量化交易服务。