# OpenAPI 介绍

## 概述

OpenAPI 量化接口，为您的程序化交易，提供丰富的行情和交易接口，满足每一位开发者的量化投资需求，助力您的宽客梦想。

牛牛用户可以[点击这里](https://www.futunn.com/OpenAPI)了解更多。

## 架构说明

OpenAPI 由 OpenD 和 Futu API 组成：

### OpenD（网关程序）
- **功能**：Futu API 的网关程序，运行于您的本地电脑或云端服务器
- **作用**：负责中转协议请求到富途后台，并将处理后的数据返回
- **特点**：以自定义 TCP 协议的方式对外暴露接口，该协议接口与编程语言无关

### Futu API（SDK）
- **功能**：富途为主流的编程语言封装的 API SDK
- **作用**：方便您调用，降低策略开发难度
- **支持语言**：Python、Java、C#、C++、JavaScript 等主流语言
- **扩展性**：如果您希望使用的语言没有在上述之列，您仍可自行对接裸协议，完成策略开发

## 系统框架图

```
┌─────────────────┐    TCP协议    ┌─────────────────┐    HTTP/TCP    ┌─────────────────┐
│                 │   ────────►   │                 │   ─────────►   │                 │
│   您的策略程序   │              │     OpenD       │               │   富途服务器     │
│   (Futu API)   │   ◄────────   │    (网关程序)    │   ◄─────────   │                 │
│                 │              │                 │               │                 │
└─────────────────┘              └─────────────────┘               └─────────────────┘
```

## 交互时序图

```
策略程序          OpenD           富途服务器
   │               │                │
   │──下单请求────►│                │
   │               │──转发请求─────►│
   │               │                │
   │               │◄─返回结果─────│
   │◄─下单结果────│                │
   │               │                │
   │               │◄─订单推送─────│
   │◄─推送通知────│                │
```

## 初始使用步骤

初次接触 OpenAPI，您需要进行如下两步操作：

### 第一步：安装启动 OpenD
在本地或云端安装并启动一个网关程序 [OpenD](../quick/opend-base.md)。

OpenD 以自定义 TCP 协议的方式对外暴露接口，负责中转协议请求到富途服务器，并将处理后的数据返回，该协议接口与编程语言无关。

### 第二步：环境搭建
下载 Futu API，完成[环境搭建](../quick/env.md)，以便快速调用。

为方便您的使用，富途对主流的编程语言，封装了相应的 API SDK（以下简称 Futu API）。

## 账号体系

OpenAPI 涉及 2 类账号，分别是**平台账号**和**综合账户**。

### 平台账号
- **定义**：您在富途的用户 ID（牛牛号）
- **适用范围**：富途牛牛 APP、OpenAPI
- **用途**：使用平台账号（牛牛号）和登录密码，登录 OpenD 并获取行情

### 综合账户
综合账户支持以多种货币在同一个账户内交易不同市场品类（港股、美股、A股通、基金）。您可以通过一个账户进行全市场交易，不需要再管理多个账户。

综合账户包括：
- **综合账户 - 证券**：用于交易全市场的股票、ETFs、期权等证券类产品
- **综合账户 - 期货**：用于交易全市场的期货产品，目前支持香港市场期货、美国市场 CME Group 期货、新加坡市场期货、日本市场期货

## 功能特点

OpenAPI 的功能主要有两部分：行情和交易。

### 行情功能

#### 行情数据品类
支持香港、美国、A 股市场的行情数据，涉及的品类包括股票、指数、期权、期货等。

获取行情数据需要相关权限，如需了解行情权限的获取方式以及限制规则，请查看[权限和限制](authority.md)。

#### 行情数据获取方式
1. **实时推送**：订阅并接收实时报价、实时 K 线、实时逐笔、实时摆盘等数据推送
2. **主动拉取**：拉取最新市场快照，历史 K 线等

### 交易功能

#### 交易能力
支持香港、美国、A 股、新加坡、日本 5 个市场的交易能力，涉及的品类包括股票、期权、期货等。

#### 交易方式
真实交易和模拟交易使用同一套交易接口。

## 技术特点

### 全平台多语言
- **OpenD 支持**：Windows、MacOS、CentOS、Ubuntu
- **Futu API 支持**：Python、Java、C#、C++、JavaScript 等主流语言

### 稳定极速免费
- **稳定架构**：稳定的技术架构，直连交易所一触即达
- **极速响应**：下单最快只需 0.0014 s
- **免费使用**：通过 OpenAPI 交易无附加收费

### 丰富的投资品类
- **多市场支持**：支持美国、香港等多个市场的实时行情、实盘交易及模拟交易

### 专业的机构服务
- **定制方案**：定制化的行情交易解决方案

## 下一步

- 了解[权限和限制](authority.md)
- 查看[费用说明](fee.md)
- 开始[快速上手](../quick/opend-base.md)教程