# 交易接口总览

富途OpenAPI提供完整的交易功能，支持账户管理、资产持仓查询、订单操作、成交查询等核心交易能力。

## 接口分类

| 模块 | 接口名 | 功能简介 |
|------|--------|----------|
| **账户** | | |
| | `Get Account List` | 获取交易业务账户列表 |
| | `Unlock Trading` | 解锁交易 |
| **资产持仓** | | |
| | `Get Account Financial Information` | 获取账户资金数据 |
| | `Get Maximum Tradable Quantity` | 查询账户最大可买卖数量 |
| | `Get Positions List` | 获取持仓列表 |
| | `Get Margin Trading Data` | 获取融资融券数据 |
| | `Get Cash Flow Summary` | 查询账户现金流水 |
| **订单** | | |
| | `Place Order` | 下单 |
| | `Modify or Cancel Order` | 改单撤单 |
| | `Get Order list` | 查询未完成订单 |
| | `Get Order Fees` | 查询订单费用 |
| | `Get Historical Order List` | 查询历史订单 |
| | `Order Callback` | 订单回调 |
| | `Trade Data Callback` | 订阅交易推送 |
| **成交** | | |
| | `Get Today's Executed Trades` | 查询当日成交 |
| | `Get Historical Executed Trades` | 查询历史成交 |
| | `Trade Execution Callback` | 成交回调 |

## 使用流程

### 1. 交易前准备

1. **获取账户列表**：使用`Get Account List`获取可用的交易账户
2. **解锁交易**：使用`Unlock Trading`解锁交易功能（需要交易密码）
3. **查询资金信息**：使用`Get Account Financial Information`查看可用资金

### 2. 下单前分析

1. **查询最大可买数量**：使用`Get Maximum Tradable Quantity`确认可买卖数量
2. **查询持仓**：使用`Get Positions List`查看当前持仓情况
3. **计算订单费用**：使用`Get Order Fees`预估交易费用

### 3. 订单操作

1. **下单**：使用`Place Order`提交买卖订单
2. **监控订单**：通过`Order Callback`接收订单状态变化
3. **修改/撤单**：使用`Modify or Cancel Order`进行订单管理
4. **查询订单**：使用`Get Order list`查看未完成订单

### 4. 成交查询

1. **实时成交**：通过`Trade Execution Callback`接收成交推送
2. **查询当日成交**：使用`Get Today's Executed Trades`查看当日成交记录
3. **查询历史成交**：使用`Get Historical Executed Trades`查看历史成交

## 交易类型支持

### 股票交易
- **港股**：股票、ETF、窝轮、牛熊证、界内证
- **美股**：股票、ETF
- **A股通**：沪港通、深港通股票

### 衍生品交易
- **期权**：香港期权、美股期权
- **期货**：香港期货、美国期货、新加坡期货、日本期货

### 订单类型
- **限价单**：指定价格买卖
- **市价单**：以市场价格买卖
- **增强限价单**：港股特有订单类型
- **竞价单**：开盘竞价时段使用

## 账户类型

### 模拟账户
- 用于测试和学习
- 与真实交易使用相同接口
- 无资金风险

### 真实账户
- 真实资金交易
- 需要完成开户和资金入账
- 受监管机构监督

## 风控机制

### 交易解锁
- 每次交易需要先解锁
- 支持交易密码解锁
- 可设置自动解锁时长

### 风险控制
- 最大可买卖数量限制
- 资金充足性检查
- 持仓集中度监控

### 订单安全
- 订单确认机制
- 价格合理性检查
- 异常交易监控

## 实时推送

### 订单推送
- 订单状态变化实时推送
- 包括已报、已成、已撤等状态

### 成交推送
- 成交信息实时推送
- 包括成交价格、数量、时间

### 账户推送
- 资金变化推送
- 持仓变化推送

## 注意事项

1. **交易时间**：只能在相应市场的交易时间内进行交易
2. **资金要求**：确保账户有足够资金进行交易
3. **风险提示**：投资有风险，交易需谨慎
4. **接口限制**：注意接口调用频率限制
5. **数据权限**：部分功能需要相应的交易权限

## 费用说明

### 交易费用
- 佣金：根据券商费率收取
- 印花税：部分市场收取
- 平台费：部分市场收取
- 结算费：部分市场收取

### API费用
- OpenAPI交易功能本身免费
- 按正常交易费率收取交易费用
- 无额外API调用费用

## 下一步

- 了解[交易对象](base.md)的创建和配置
- 查看[下单接口](place-order.md)的详细使用方法
- 学习[订单回调](update-order.md)的处理方式
- 掌握[账户管理](get-acc-list.md)的基本操作