# 下单

> 对应官方文档: `/futu-api-doc/trade/place-order.html`

## 接口介绍

执行股票买入、卖出等交易操作。

## 函数原型

```python
place_order(price, qty, code, trd_side, order_type=OrderType.NORMAL, adjust_limit=0, trd_env=TrdEnv.SIMULATE)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| price | float | 订单价格 |
| qty | int | 订单数量 |
| code | str | 股票代码 |
| trd_side | TrdSide | 交易方向 |
| order_type | OrderType | 订单类型 |
| adjust_limit | int | 价格调整限制 |
| trd_env | TrdEnv | 交易环境 |

## 交易方向

| 类型 | 说明 |
|------|------|
| TrdSide.BUY | 买入 |
| TrdSide.SELL | 卖出 |

## 订单类型

| 类型 | 说明 |
|------|------|
| OrderType.NORMAL | 普通订单 |
| OrderType.MARKET | 市价订单 |
| OrderType.ABSOLUTE_LIMIT | 绝对限价单 |
| OrderType.AUCTION | 竞价单 |
| OrderType.AUCTION_LIMIT | 竞价限价单 |
| OrderType.SPECIAL_LIMIT | 特殊限价单 |

## 交易环境

| 类型 | 说明 |
|------|------|
| TrdEnv.SIMULATE | 模拟交易 |
| TrdEnv.REAL | 真实交易 |

## 使用示例

### 基本买入示例

```python
from futu import *

# 创建交易上下文
trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)

# 解锁交易
ret, data = trd_ctx.unlock_trade(password='your_password')
if ret == RET_OK:
    print("解锁交易成功")
else:
    print("解锁交易失败:", data)

# 买入腾讯股票
ret, data = trd_ctx.place_order(
    price=400.0,           # 价格400港元
    qty=100,               # 数量100股
    code='HK.00700',       # 腾讯股票代码
    trd_side=TrdSide.BUY,  # 买入
    order_type=OrderType.NORMAL,  # 普通订单
    trd_env=TrdEnv.SIMULATE       # 模拟交易
)

if ret == RET_OK:
    print("下单成功:")
    print(f"订单号: {data['order_id'][0]}")
    print(f"股票代码: {data['code'][0]}")
    print(f"交易方向: {data['trd_side'][0]}")
    print(f"订单价格: {data['price'][0]}")
    print(f"订单数量: {data['qty'][0]}")
else:
    print("下单失败:", data)

trd_ctx.close()
```

### 卖出示例

```python
from futu import *

trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)

# 解锁交易
ret, data = trd_ctx.unlock_trade(password='your_password')

# 卖出腾讯股票
ret, data = trd_ctx.place_order(
    price=420.0,           # 价格420港元
    qty=100,               # 数量100股
    code='HK.00700',       # 腾讯股票代码
    trd_side=TrdSide.SELL, # 卖出
    order_type=OrderType.NORMAL,  # 普通订单
    trd_env=TrdEnv.SIMULATE       # 模拟交易
)

if ret == RET_OK:
    print("卖出订单创建成功:")
    print(f"订单号: {data['order_id'][0]}")
    print(f"预计收入: {data['price'][0] * data['qty'][0]}")
else:
    print("卖出订单创建失败:", data)

trd_ctx.close()
```

### 市价单示例

```python
from futu import *

trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)

# 解锁交易
ret, data = trd_ctx.unlock_trade(password='your_password')

# 市价买入
ret, data = trd_ctx.place_order(
    price=0.0,             # 市价单价格可以设为0
    qty=100,               # 数量100股
    code='HK.00005',       # 汇丰控股
    trd_side=TrdSide.BUY,  # 买入
    order_type=OrderType.MARKET,  # 市价单
    trd_env=TrdEnv.SIMULATE       # 模拟交易
)

if ret == RET_OK:
    print("市价单创建成功:")
    print(f"订单号: {data['order_id'][0]}")
    print("将以市价执行")
else:
    print("市价单创建失败:", data)

trd_ctx.close()
```

### 交易策略示例

```python
from futu import *
import time

class TradingStrategy:
    def __init__(self):
        self.trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.is_unlocked = False
    
    def unlock_trading(self, password):
        """解锁交易"""
        ret, data = self.trd_ctx.unlock_trade(password=password)
        if ret == RET_OK:
            self.is_unlocked = True
            print("交易解锁成功")
            return True
        else:
            print("交易解锁失败:", data)
            return False
    
    def get_current_price(self, code):
        """获取当前价格"""
        ret, data = self.quote_ctx.get_stock_quote([code])
        if ret == RET_OK:
            return data['cur_price'][0]
        return None
    
    def place_limit_order(self, code, qty, target_price, side):
        """下限价单"""
        if not self.is_unlocked:
            print("交易未解锁")
            return False
        
        ret, data = self.trd_ctx.place_order(
            price=target_price,
            qty=qty,
            code=code,
            trd_side=side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.SIMULATE
        )
        
        if ret == RET_OK:
            order_id = data['order_id'][0]
            side_str = "买入" if side == TrdSide.BUY else "卖出"
            print(f"{side_str}订单创建成功: {order_id}")
            return order_id
        else:
            print("下单失败:", data)
            return None
    
    def momentum_strategy(self, code, position_size=100):
        """动量交易策略"""
        current_price = self.get_current_price(code)
        if not current_price:
            print("无法获取当前价格")
            return
        
        print(f"当前价格: {current_price}")
        
        # 简单动量策略：如果价格上涨，买入
        # 实际应用中需要更复杂的逻辑
        buy_price = current_price * 1.01  # 比当前价高1%买入
        sell_price = current_price * 0.99  # 比当前价低1%卖出
        
        print(f"设置买入价格: {buy_price}")
        print(f"设置卖出价格: {sell_price}")
        
        # 下买单
        buy_order = self.place_limit_order(code, position_size, buy_price, TrdSide.BUY)
        
        # 下卖单
        sell_order = self.place_limit_order(code, position_size, sell_price, TrdSide.SELL)
        
        return buy_order, sell_order
    
    def grid_trading(self, code, base_price, grid_size=0.05, position_size=100):
        """网格交易策略"""
        if not self.is_unlocked:
            print("交易未解锁")
            return
        
        print(f"执行网格交易策略: {code}")
        print(f"基准价格: {base_price}")
        print(f"网格大小: {grid_size}")
        
        orders = []
        
        # 在基准价上下各设置5个网格
        for i in range(1, 6):
            # 买入订单（低于基准价）
            buy_price = base_price * (1 - i * grid_size)
            buy_order = self.place_limit_order(code, position_size, buy_price, TrdSide.BUY)
            if buy_order:
                orders.append(('BUY', buy_order, buy_price))
            
            # 卖出订单（高于基准价）
            sell_price = base_price * (1 + i * grid_size)
            sell_order = self.place_limit_order(code, position_size, sell_price, TrdSide.SELL)
            if sell_order:
                orders.append(('SELL', sell_order, sell_price))
        
        print(f"网格交易设置完成，共{len(orders)}个订单")
        return orders
    
    def close(self):
        """关闭连接"""
        self.trd_ctx.close()
        self.quote_ctx.close()

# 使用示例
strategy = TradingStrategy()

# 解锁交易
if strategy.unlock_trading('your_password'):
    # 执行动量策略
    strategy.momentum_strategy('HK.00700', 100)
    
    # 执行网格交易
    current_price = strategy.get_current_price('HK.00941')
    if current_price:
        strategy.grid_trading('HK.00941', current_price, 0.02, 100)

strategy.close()
```

### 批量下单示例

```python
from futu import *
import time

def batch_place_orders(order_list):
    """批量下单"""
    trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    # 解锁交易
    ret, data = trd_ctx.unlock_trade(password='your_password')
    if ret != RET_OK:
        print("解锁交易失败:", data)
        return
    
    results = []
    
    for order in order_list:
        print(f"下单: {order['code']} {order['side']} {order['qty']}股 @ {order['price']}")
        
        ret, data = trd_ctx.place_order(
            price=order['price'],
            qty=order['qty'],
            code=order['code'],
            trd_side=order['side'],
            order_type=order.get('order_type', OrderType.NORMAL),
            trd_env=TrdEnv.SIMULATE
        )
        
        if ret == RET_OK:
            order_id = data['order_id'][0]
            results.append({
                'success': True,
                'order_id': order_id,
                'code': order['code'],
                'side': order['side'],
                'price': order['price'],
                'qty': order['qty']
            })
            print(f"  成功: 订单号 {order_id}")
        else:
            results.append({
                'success': False,
                'error': data,
                'code': order['code']
            })
            print(f"  失败: {data}")
        
        # 避免下单过快
        time.sleep(0.1)
    
    trd_ctx.close()
    return results

# 准备订单列表
orders = [
    {'code': 'HK.00700', 'side': TrdSide.BUY, 'qty': 100, 'price': 400.0},
    {'code': 'HK.00005', 'side': TrdSide.BUY, 'qty': 200, 'price': 50.0},
    {'code': 'HK.00941', 'side': TrdSide.SELL, 'qty': 100, 'price': 90.0},
    {'code': 'HK.00388', 'side': TrdSide.BUY, 'qty': 500, 'price': 20.0}
]

# 执行批量下单
results = batch_place_orders(orders)

# 统计结果
success_count = sum(1 for r in results if r['success'])
print(f"\n批量下单完成: {success_count}/{len(orders)} 成功")
```

### 智能下单系统

```python
from futu import *
import time

class SmartOrderSystem:
    def __init__(self):
        self.trd_ctx = OpenHKTradeContext(host='127.0.0.1', port=11111)
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.is_unlocked = False
    
    def unlock_trading(self, password):
        """解锁交易"""
        ret, data = self.trd_ctx.unlock_trade(password=password)
        if ret == RET_OK:
            self.is_unlocked = True
            return True
        return False
    
    def get_market_data(self, code):
        """获取市场数据"""
        ret, data = self.quote_ctx.get_stock_quote([code])
        if ret == RET_OK:
            return {
                'cur_price': data['cur_price'][0],
                'high_price': data['high_price'][0],
                'low_price': data['low_price'][0],
                'volume': data['volume'][0],
                'turnover': data['turnover'][0]
            }
        return None
    
    def calculate_order_size(self, code, risk_amount=10000):
        """根据风险金额计算下单数量"""
        market_data = self.get_market_data(code)
        if not market_data:
            return 0
        
        # 假设止损幅度为5%
        stop_loss_ratio = 0.05
        price_per_share = market_data['cur_price']
        risk_per_share = price_per_share * stop_loss_ratio
        
        # 计算最大可买数量
        max_qty = int(risk_amount / risk_per_share)
        
        # 考虑每手股数（港股通常为100股的倍数）
        lot_size = 100
        order_qty = (max_qty // lot_size) * lot_size
        
        return order_qty
    
    def place_smart_order(self, code, side, strategy='market_timing'):
        """智能下单"""
        if not self.is_unlocked:
            print("交易未解锁")
            return None
        
        market_data = self.get_market_data(code)
        if not market_data:
            print("无法获取市场数据")
            return None
        
        current_price = market_data['cur_price']
        
        # 根据策略确定下单价格
        if strategy == 'market_timing':
            # 市场时机策略：在支撑位买入，阻力位卖出
            if side == TrdSide.BUY:
                order_price = current_price * 0.995  # 略低于市价买入
            else:
                order_price = current_price * 1.005  # 略高于市价卖出
                
        elif strategy == 'momentum':
            # 动量策略：跟随价格趋势
            if side == TrdSide.BUY:
                order_price = current_price * 1.002  # 略高于市价买入
            else:
                order_price = current_price * 0.998  # 略低于市价卖出
        
        else:
            order_price = current_price
        
        # 计算下单数量
        order_qty = self.calculate_order_size(code, risk_amount=10000)
        
        if order_qty <= 0:
            print("计算的下单数量为0")
            return None
        
        print(f"智能下单: {code}")
        print(f"当前价格: {current_price}")
        print(f"下单价格: {order_price}")
        print(f"下单数量: {order_qty}")
        print(f"策略: {strategy}")
        
        # 执行下单
        ret, data = self.trd_ctx.place_order(
            price=order_price,
            qty=order_qty,
            code=code,
            trd_side=side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.SIMULATE
        )
        
        if ret == RET_OK:
            order_id = data['order_id'][0]
            print(f"智能下单成功: {order_id}")
            return order_id
        else:
            print("智能下单失败:", data)
            return None
    
    def close(self):
        """关闭连接"""
        self.trd_ctx.close()
        self.quote_ctx.close()

# 使用示例
smart_system = SmartOrderSystem()

if smart_system.unlock_trading('your_password'):
    # 使用市场时机策略买入
    smart_system.place_smart_order('HK.00700', TrdSide.BUY, 'market_timing')
    
    # 使用动量策略卖出
    smart_system.place_smart_order('HK.00005', TrdSide.SELL, 'momentum')

smart_system.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| order_id | str | 订单号 |
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| trd_side | str | 交易方向 |
| order_type | str | 订单类型 |
| order_status | str | 订单状态 |
| qty | int | 订单数量 |
| price | float | 订单价格 |
| create_time | str | 创建时间 |
| updated_time | str | 更新时间 |

## 应用场景

1. **程序化交易**: 自动化执行交易策略
2. **风险管理**: 设置止损止盈订单
3. **套利交易**: 快速响应价格差异
4. **批量交易**: 同时处理多个股票订单

## 注意事项

1. 必须先解锁交易才能下单
2. 模拟交易和真实交易使用不同环境
3. 注意交易时间和市场状态
4. 考虑手续费和滑点影响
5. 做好风险控制和资金管理
6. 遵守相关交易法规和限制