# 查询订单

查询账户的历史和当前订单信息，支持多种筛选条件，帮助跟踪订单状态和交易记录。

## order_list_query - 查询订单列表

### 接口原型

```python
order_list_query(order_id='', status_filter_list=[], code='', start='', end='', trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)
```

### 功能说明

查询指定账户的订单列表，支持按订单ID、状态、股票代码、时间范围等条件筛选。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | str | 否 | 订单号，空字符串表示所有 |
| status_filter_list | list | 否 | 订单状态筛选列表，空列表表示所有状态 |
| code | str | 否 | 股票代码，空字符串表示所有 |
| start | str | 否 | 开始时间，格式'YYYY-MM-DD' |
| end | str | 否 | 结束时间，格式'YYYY-MM-DD' |
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |
| refresh_cache | bool | 否 | 是否刷新缓存，默认False |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 订单数据DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| trd_env | str | 交易环境 |
| order_id | str | 订单号 |
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| trd_side | str | 交易方向（BUY/SELL） |
| order_type | str | 订单类型 |
| order_status | str | 订单状态 |
| qty | int | 订单数量 |
| price | float | 订单价格 |
| create_time | str | 创建时间 |
| updated_time | str | 更新时间 |
| dealt_qty | int | 已成交数量 |
| dealt_avg_price | float | 成交均价 |
| last_err_msg | str | 最后错误信息 |
| remark | str | 订单备注 |
| time_in_force | str | 订单有效期 |
| fill_outside_rth | bool | 是否允许盘前盘后成交 |

### 代码示例

#### 查询所有订单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 查询所有订单
    ret, orders = trd_ctx.order_list_query(trd_env=ft.TrdEnv.SIMULATE)
    
    if ret == ft.RET_OK:
        if orders.empty:
            print("当前没有订单记录")
        else:
            print(f"订单总数: {len(orders)}")
            print("\n订单概览:")
            
            # 显示关键信息
            display_columns = ['order_id', 'code', 'trd_side', 'order_status', 
                             'qty', 'dealt_qty', 'price', 'create_time']
            print(orders[display_columns])
            
            # 订单状态统计
            status_counts = orders['order_status'].value_counts()
            print(f"\n订单状态统计:")
            for status, count in status_counts.items():
                print(f"  {status}: {count}")
    else:
        print('查询订单失败:', orders)
        
finally:
    trd_ctx.close()
```

#### 按状态查询订单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 1. 查询未成交订单
ret, pending_orders = trd_ctx.order_list_query(
    status_filter_list=[ft.OrderStatus.SUBMITTED, ft.OrderStatus.FILLED_PART],
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not pending_orders.empty:
    print("未成交订单:")
    for _, order in pending_orders.iterrows():
        remain_qty = order['qty'] - order['dealt_qty']
        print(f"  {order['code']} - {order['trd_side']} "
              f"{remain_qty}/{order['qty']} @ {order['price']:.2f}")

# 2. 查询已成交订单
ret, filled_orders = trd_ctx.order_list_query(
    status_filter_list=[ft.OrderStatus.FILLED_ALL],
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not filled_orders.empty:
    print(f"\n已成交订单数量: {len(filled_orders)}")
    
    # 计算总成交金额
    filled_orders['total_value'] = filled_orders['dealt_qty'] * filled_orders['dealt_avg_price']
    total_buy_value = filled_orders[filled_orders['trd_side'] == 'BUY']['total_value'].sum()
    total_sell_value = filled_orders[filled_orders['trd_side'] == 'SELL']['total_value'].sum()
    
    print(f"总买入金额: {total_buy_value:,.2f}")
    print(f"总卖出金额: {total_sell_value:,.2f}")

# 3. 查询已撤销订单
ret, cancelled_orders = trd_ctx.order_list_query(
    status_filter_list=[ft.OrderStatus.CANCELLED_ALL, ft.OrderStatus.CANCELLED_PART],
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not cancelled_orders.empty:
    print(f"\n已撤销订单数量: {len(cancelled_orders)}")

trd_ctx.close()
```

#### 按时间范围查询

```python
import futu as ft
from datetime import datetime, timedelta

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 查询最近7天的订单
end_date = datetime.now().strftime('%Y-%m-%d')
start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

ret, recent_orders = trd_ctx.order_list_query(
    start=start_date,
    end=end_date,
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK:
    if recent_orders.empty:
        print(f"最近7天({start_date} 到 {end_date})没有订单")
    else:
        print(f"最近7天订单数量: {len(recent_orders)}")
        
        # 按日期统计
        recent_orders['date'] = recent_orders['create_time'].str[:10]
        daily_counts = recent_orders['date'].value_counts().sort_index()
        
        print("\n每日订单统计:")
        for date, count in daily_counts.items():
            print(f"  {date}: {count} 个订单")
        
        # 显示最近订单
        print("\n最近5个订单:")
        latest_orders = recent_orders.sort_values('create_time', ascending=False).head(5)
        for _, order in latest_orders.iterrows():
            print(f"  {order['create_time']} - {order['code']} "
                  f"{order['trd_side']} {order['qty']} @ {order['price']:.2f} "
                  f"[{order['order_status']}]")

trd_ctx.close()
```

#### 订单性能分析

```python
import futu as ft
import pandas as pd
from datetime import datetime

def analyze_trading_performance(trd_ctx, days=30):
    """分析交易绩效"""
    
    # 获取指定天数的订单
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    ret, orders = trd_ctx.order_list_query(
        start=start_date,
        end=end_date,
        trd_env=ft.TrdEnv.SIMULATE
    )
    
    if ret != ft.RET_OK or orders.empty:
        print("没有订单数据可分析")
        return
    
    print(f"=== 交易绩效分析 ({days}天) ===\n")
    
    # 1. 基本统计
    total_orders = len(orders)
    filled_orders = orders[orders['order_status'] == 'FILLED_ALL']
    cancelled_orders = orders[orders['order_status'].str.contains('CANCELLED')]
    
    fill_rate = len(filled_orders) / total_orders * 100 if total_orders > 0 else 0
    cancel_rate = len(cancelled_orders) / total_orders * 100 if total_orders > 0 else 0
    
    print("1. 订单执行统计:")
    print(f"   总订单数: {total_orders}")
    print(f"   已成交: {len(filled_orders)} ({fill_rate:.1f}%)")
    print(f"   已撤销: {len(cancelled_orders)} ({cancel_rate:.1f}%)")
    
    # 2. 成交分析
    if not filled_orders.empty:
        filled_orders['total_value'] = filled_orders['dealt_qty'] * filled_orders['dealt_avg_price']
        
        buy_orders = filled_orders[filled_orders['trd_side'] == 'BUY']
        sell_orders = filled_orders[filled_orders['trd_side'] == 'SELL']
        
        total_buy_value = buy_orders['total_value'].sum()
        total_sell_value = sell_orders['total_value'].sum()
        
        print(f"\n2. 成交金额统计:")
        print(f"   买入总额: {total_buy_value:,.0f}")
        print(f"   卖出总额: {total_sell_value:,.0f}")
        print(f"   净买入: {total_buy_value - total_sell_value:+,.0f}")
    
    # 3. 股票活跃度
    stock_activity = orders['code'].value_counts().head(10)
    print(f"\n3. 交易活跃股票 (前10):")
    for code, count in stock_activity.items():
        stock_name = orders[orders['code'] == code]['stock_name'].iloc[0]
        print(f"   {stock_name} ({code}): {count} 次")
    
    # 4. 时间分布
    orders['hour'] = pd.to_datetime(orders['create_time']).dt.hour
    hourly_distribution = orders['hour'].value_counts().sort_index()
    
    print(f"\n4. 下单时间分布:")
    for hour in range(9, 17):  # 交易时间
        count = hourly_distribution.get(hour, 0)
        if count > 0:
            print(f"   {hour:02d}:00-{hour:02d}:59: {count} 个订单")

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

analyze_trading_performance(trd_ctx, days=30)

trd_ctx.close()
```

#### 订单状态监控

```python
import futu as ft
import time
from datetime import datetime

class OrderMonitor:
    def __init__(self, trd_ctx):
        self.trd_ctx = trd_ctx
        self.last_check_time = None
    
    def monitor_order_changes(self, check_interval=30):
        """监控订单状态变化"""
        print("开始监控订单状态变化...")
        
        # 获取初始订单状态
        previous_orders = self.get_active_orders()
        self.last_check_time = datetime.now()
        
        while True:
            time.sleep(check_interval)
            
            current_orders = self.get_active_orders()
            changes = self.detect_changes(previous_orders, current_orders)
            
            if changes:
                print(f"\n{datetime.now().strftime('%H:%M:%S')} - 检测到订单变化:")
                for change in changes:
                    print(f"  {change}")
            
            previous_orders = current_orders
    
    def get_active_orders(self):
        """获取活跃订单"""
        ret, orders = self.trd_ctx.order_list_query(
            status_filter_list=[
                ft.OrderStatus.SUBMITTED,
                ft.OrderStatus.FILLED_PART,
                ft.OrderStatus.FILLED_ALL
            ],
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        if ret == ft.RET_OK:
            return orders[['order_id', 'code', 'order_status', 'dealt_qty', 'qty']]
        else:
            return pd.DataFrame()
    
    def detect_changes(self, previous, current):
        """检测订单变化"""
        changes = []
        
        if previous.empty:
            return changes
        
        # 检查状态变化
        for _, prev_order in previous.iterrows():
            order_id = prev_order['order_id']
            current_order = current[current['order_id'] == order_id]
            
            if current_order.empty:
                # 订单不再活跃（可能已撤销或完全成交）
                changes.append(f"订单 {order_id} 已结束")
            else:
                curr = current_order.iloc[0]
                
                # 检查成交数量变化
                if curr['dealt_qty'] != prev_order['dealt_qty']:
                    changes.append(
                        f"订单 {order_id} ({curr['code']}) 新成交 "
                        f"{curr['dealt_qty'] - prev_order['dealt_qty']} 股"
                    )
                
                # 检查状态变化
                if curr['order_status'] != prev_order['order_status']:
                    changes.append(
                        f"订单 {order_id} 状态: {prev_order['order_status']} → {curr['order_status']}"
                    )
        
        # 检查新订单
        new_orders = current[~current['order_id'].isin(previous['order_id'])]
        for _, new_order in new_orders.iterrows():
            changes.append(
                f"新订单 {new_order['order_id']} ({new_order['code']}) - {new_order['order_status']}"
            )
        
        return changes

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

monitor = OrderMonitor(trd_ctx)

# 监控订单变化（每30秒检查一次）
# monitor.monitor_order_changes(check_interval=30)

# 单次检查活跃订单
active_orders = monitor.get_active_orders()
if not active_orders.empty:
    print("当前活跃订单:")
    print(active_orders)
else:
    print("没有活跃订单")

trd_ctx.close()
```

### 订单状态说明

#### OrderStatus（订单状态）

| 常量 | 说明 |
|------|------|
| NONE | 未知状态 |
| UNSUBMITTED | 未提交 |
| WAITING_SUBMIT | 等待提交 |
| SUBMITTING | 提交中 |
| SUBMIT_FAILED | 提交失败 |
| SUBMITTED | 已提交 |
| FILLED_PART | 部分成交 |
| FILLED_ALL | 全部成交 |
| CANCELLING_PART | 部分撤销中 |
| CANCELLED_PART | 部分撤销 |
| CANCELLING_ALL | 全部撤销中 |
| CANCELLED_ALL | 全部撤销 |
| FAILED | 失败 |
| DISABLED | 已失效 |
| DELETED | 已删除 |

### 注意事项

1. **数据量限制**：一次查询的订单数量可能有限制
2. **时间范围**：查询时间跨度不宜过长，建议单次查询不超过1个月
3. **状态同步**：订单状态可能有延迟，使用`refresh_cache=True`获取最新状态
4. **数据完整性**：部分历史订单的详细信息可能不完整
5. **权限要求**：需要交易解锁才能查询订单信息

### 错误处理

```python
import futu as ft

def safe_query_orders(trd_ctx, **query_params):
    """安全查询订单，包含错误处理"""
    try:
        ret, data = trd_ctx.order_list_query(**query_params)
        
        if ret == ft.RET_OK:
            return True, data
        else:
            # 处理常见错误
            error_msg = str(data)
            if "unlock" in error_msg.lower():
                return False, "需要先解锁交易"
            elif "time range" in error_msg.lower():
                return False, "时间范围参数错误"
            elif "account" in error_msg.lower():
                return False, "账户ID无效或无权限"
            else:
                return False, f"查询失败: {error_msg}"
                
    except Exception as e:
        return False, f"查询异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, result = safe_query_orders(
    trd_ctx,
    status_filter_list=[ft.OrderStatus.FILLED_ALL],
    trd_env=ft.TrdEnv.SIMULATE,
    refresh_cache=True
)

if success:
    print(f"查询成功，共 {len(result)} 个订单")
else:
    print("查询失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [下单](place-order.md) - 提交订单
- [修改订单](modify-order.md) - 修改订单
- [撤销订单](cancel-order.md) - 撤销订单