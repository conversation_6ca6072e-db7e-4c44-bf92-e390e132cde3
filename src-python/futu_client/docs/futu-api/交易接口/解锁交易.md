# 解锁交易

解锁交易是进行所有交易操作的前提，包括下单、撤单、查询订单等功能。

## unlock_trade - 解锁交易

### 接口原型

```python
unlock_trade(password, password_md5=None, is_unlock=True)
```

### 功能说明

使用交易密码解锁交易功能，解锁后才能进行下单、撤单等交易操作。支持明文密码和MD5加密密码。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| password | str | 是 | 交易密码明文或MD5值 |
| password_md5 | str | 否 | 密码的MD5值（已弃用，建议直接在password中传MD5） |
| is_unlock | bool | 否 | True为解锁，False为锁定，默认True |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | str | 解锁结果描述，成功时返回成功信息，失败时返回错误描述 |

### 代码示例

#### 基本解锁

```python
import futu as ft

# 创建交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 使用明文密码解锁
    ret, data = trd_ctx.unlock_trade("123456")  # 替换为您的实际交易密码
    
    if ret == ft.RET_OK:
        print('交易解锁成功')
        print('返回信息:', data)
    else:
        print('交易解锁失败:', data)
        
finally:
    trd_ctx.close()
```

#### 使用MD5密码解锁

```python
import futu as ft
import hashlib

def get_md5_password(password):
    """将明文密码转换为MD5"""
    return hashlib.md5(password.encode('utf-8')).hexdigest()

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 将密码转换为MD5
    plain_password = "123456"  # 您的实际密码
    md5_password = get_md5_password(plain_password)
    
    # 使用MD5密码解锁
    ret, data = trd_ctx.unlock_trade(md5_password)
    
    if ret == ft.RET_OK:
        print('MD5密码解锁成功')
    else:
        print('MD5密码解锁失败:', data)
        
finally:
    trd_ctx.close()
```

#### 解锁状态管理

```python
import futu as ft
import time

class TradeUnlocker:
    def __init__(self, trd_ctx, password):
        self.trd_ctx = trd_ctx
        self.password = password
        self.is_unlocked = False
        self.unlock_time = None
    
    def unlock(self):
        """解锁交易"""
        ret, data = self.trd_ctx.unlock_trade(self.password)
        
        if ret == ft.RET_OK:
            self.is_unlocked = True
            self.unlock_time = time.time()
            print('交易解锁成功')
            return True
        else:
            self.is_unlocked = False
            self.unlock_time = None
            print('交易解锁失败:', data)
            return False
    
    def lock(self):
        """锁定交易"""
        ret, data = self.trd_ctx.unlock_trade(self.password, is_unlock=False)
        
        if ret == ft.RET_OK:
            self.is_unlocked = False
            self.unlock_time = None
            print('交易锁定成功')
            return True
        else:
            print('交易锁定失败:', data)
            return False
    
    def check_unlock_status(self):
        """检查解锁状态"""
        if self.is_unlocked and self.unlock_time:
            unlock_duration = time.time() - self.unlock_time
            print(f'交易已解锁 {unlock_duration:.0f} 秒')
            return True
        else:
            print('交易未解锁')
            return False
    
    def auto_unlock_if_needed(self):
        """需要时自动解锁"""
        if not self.is_unlocked:
            print('检测到交易未解锁，正在解锁...')
            return self.unlock()
        return True
    
    def safe_trade_operation(self, operation_func, *args, **kwargs):
        """安全执行交易操作（自动解锁）"""
        # 确保已解锁
        if not self.auto_unlock_if_needed():
            return ft.RET_ERROR, "解锁失败"
        
        # 执行交易操作
        try:
            result = operation_func(*args, **kwargs)
            return result
        except Exception as e:
            return ft.RET_ERROR, f"操作执行异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
unlocker = TradeUnlocker(trd_ctx, "123456")

try:
    # 解锁交易
    if unlocker.unlock():
        # 检查状态
        unlocker.check_unlock_status()
        
        # 执行一些交易操作
        def place_test_order():
            return trd_ctx.place_order(
                price=100.0,
                qty=100,
                code="HK.00700",
                trd_side=ft.TrdSide.BUY,
                trd_env=ft.TrdEnv.SIMULATE
            )
        
        # 安全执行下单
        ret, data = unlocker.safe_trade_operation(place_test_order)
        if ret == ft.RET_OK:
            print('下单成功')
        else:
            print('下单失败:', data)
        
        # 锁定交易（可选）
        # unlocker.lock()
    
finally:
    trd_ctx.close()
```

#### 批量账户解锁

```python
import futu as ft

def unlock_all_accounts(password):
    """解锁所有类型的账户"""
    contexts = [
        ('港股', ft.OpenHKTradeContext(host='127.0.0.1', port=11111)),
        ('美股', ft.OpenUSTradeContext(host='127.0.0.1', port=11111)),
        ('A股', ft.OpenCNTradeContext(host='127.0.0.1', port=11111)),
        ('期货', ft.OpenFutureTradeContext(host='127.0.0.1', port=11111))
    ]
    
    unlock_results = {}
    
    for market_name, trd_ctx in contexts:
        try:
            ret, data = trd_ctx.unlock_trade(password)
            unlock_results[market_name] = {
                'success': ret == ft.RET_OK,
                'message': data,
                'context': trd_ctx
            }
            
            if ret == ft.RET_OK:
                print(f'{market_name}交易解锁成功')
            else:
                print(f'{market_name}交易解锁失败: {data}')
                
        except Exception as e:
            unlock_results[market_name] = {
                'success': False,
                'message': f'解锁异常: {str(e)}',
                'context': trd_ctx
            }
            print(f'{market_name}解锁异常: {e}')
    
    return unlock_results

# 使用示例
password = "123456"  # 您的交易密码
results = unlock_all_accounts(password)

# 显示解锁结果
print("\n解锁结果汇总:")
for market, result in results.items():
    status = "成功" if result['success'] else "失败"
    print(f"  {market}: {status}")

# 清理资源
for market, result in results.items():
    if 'context' in result:
        result['context'].close()
```

#### 定时重新解锁

```python
import futu as ft
import time
import threading
from datetime import datetime

class AutoUnlocker:
    def __init__(self, trd_ctx, password, unlock_interval=3600):
        self.trd_ctx = trd_ctx
        self.password = password
        self.unlock_interval = unlock_interval  # 重新解锁间隔（秒）
        self.running = False
        self.thread = None
        self.last_unlock_time = None
    
    def unlock_once(self):
        """单次解锁"""
        ret, data = self.trd_ctx.unlock_trade(self.password)
        
        if ret == ft.RET_OK:
            self.last_unlock_time = datetime.now()
            print(f'{self.last_unlock_time.strftime("%H:%M:%S")} - 交易解锁成功')
            return True
        else:
            print(f'{datetime.now().strftime("%H:%M:%S")} - 交易解锁失败: {data}')
            return False
    
    def start_auto_unlock(self):
        """开始自动解锁"""
        if self.running:
            print("自动解锁已在运行")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._auto_unlock_loop)
        self.thread.daemon = True
        self.thread.start()
        print(f"自动解锁已启动，间隔 {self.unlock_interval} 秒")
    
    def stop_auto_unlock(self):
        """停止自动解锁"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("自动解锁已停止")
    
    def _auto_unlock_loop(self):
        """自动解锁循环"""
        # 首次解锁
        self.unlock_once()
        
        while self.running:
            time.sleep(self.unlock_interval)
            if self.running:  # 再次检查，避免在sleep期间被停止
                self.unlock_once()
    
    def get_unlock_status(self):
        """获取解锁状态"""
        if self.last_unlock_time:
            elapsed = (datetime.now() - self.last_unlock_time).total_seconds()
            return {
                'last_unlock': self.last_unlock_time.strftime('%Y-%m-%d %H:%M:%S'),
                'elapsed_seconds': elapsed,
                'auto_running': self.running
            }
        else:
            return {
                'last_unlock': None,
                'elapsed_seconds': None,
                'auto_running': self.running
            }

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
auto_unlocker = AutoUnlocker(trd_ctx, "123456", unlock_interval=1800)  # 30分钟重新解锁一次

try:
    # 启动自动解锁
    auto_unlocker.start_auto_unlock()
    
    # 模拟一些交易操作
    for i in range(5):
        # 检查解锁状态
        status = auto_unlocker.get_unlock_status()
        print(f"\n第{i+1}次操作:")
        print(f"  最后解锁时间: {status['last_unlock']}")
        print(f"  已解锁时长: {status['elapsed_seconds']:.0f}秒" if status['elapsed_seconds'] else "  未解锁")
        
        # 执行一些操作（示例：查询账户信息）
        ret, acc_info = trd_ctx.accinfo_query(trd_env=ft.TrdEnv.SIMULATE)
        if ret == ft.RET_OK:
            print(f"  账户查询成功")
        else:
            print(f"  账户查询失败: {acc_info}")
        
        time.sleep(10)  # 等待10秒
    
finally:
    # 停止自动解锁
    auto_unlocker.stop_auto_unlock()
    trd_ctx.close()
```

### 解锁注意事项

#### 安全性考虑

1. **密码安全**：不要在代码中硬编码密码
2. **传输安全**：建议使用加密连接
3. **存储安全**：避免明文存储密码
4. **会话管理**：定期重新解锁以保持会话有效

#### 最佳实践

```python
import futu as ft
import os
import getpass
from cryptography.fernet import Fernet

class SecureTradeUnlocker:
    def __init__(self, trd_ctx):
        self.trd_ctx = trd_ctx
        self.cipher_suite = None
        self.encrypted_password = None
    
    def setup_encryption(self):
        """设置密码加密"""
        # 生成或加载密钥
        key_file = 'trade_key.key'
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                key = f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
        
        self.cipher_suite = Fernet(key)
    
    def encrypt_password(self, password):
        """加密密码"""
        if not self.cipher_suite:
            self.setup_encryption()
        
        encrypted = self.cipher_suite.encrypt(password.encode())
        self.encrypted_password = encrypted
        return encrypted
    
    def decrypt_password(self):
        """解密密码"""
        if not self.cipher_suite or not self.encrypted_password:
            return None
        
        return self.cipher_suite.decrypt(self.encrypted_password).decode()
    
    def safe_input_password(self):
        """安全输入密码"""
        # 优先从环境变量获取
        password = os.getenv('FUTU_TRADE_PASSWORD')
        
        if not password:
            # 从用户输入获取（不显示明文）
            password = getpass.getpass("请输入交易密码: ")
        
        # 加密存储
        self.encrypt_password(password)
        return password
    
    def unlock_with_retry(self, max_retries=3):
        """带重试的解锁"""
        for attempt in range(max_retries):
            password = self.decrypt_password()
            if not password:
                password = self.safe_input_password()
            
            ret, data = self.trd_ctx.unlock_trade(password)
            
            if ret == ft.RET_OK:
                print(f'第{attempt + 1}次尝试：解锁成功')
                return True
            else:
                print(f'第{attempt + 1}次尝试：解锁失败 - {data}')
                # 清除可能错误的密码
                self.encrypted_password = None
                
                if attempt < max_retries - 1:
                    print('请重新输入密码')
        
        print('解锁失败，已达到最大重试次数')
        return False

# 使用示例（需要安装 cryptography: pip install cryptography）
# trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
# secure_unlocker = SecureTradeUnlocker(trd_ctx)
# 
# if secure_unlocker.unlock_with_retry():
#     print("可以开始交易操作")
# else:
#     print("解锁失败，无法进行交易")
# 
# trd_ctx.close()
```

### 错误处理

#### 常见错误及解决方案

```python
import futu as ft

def handle_unlock_errors(trd_ctx, password):
    """处理解锁错误"""
    ret, data = trd_ctx.unlock_trade(password)
    
    if ret == ft.RET_OK:
        return True, "解锁成功"
    
    # 分析错误类型
    error_msg = str(data).lower()
    
    if "password" in error_msg or "密码" in error_msg:
        return False, "密码错误，请检查交易密码"
    elif "locked" in error_msg or "锁定" in error_msg:
        return False, "账户被锁定，请联系客服"
    elif "network" in error_msg or "连接" in error_msg:
        return False, "网络连接问题，请检查网络或OpenD状态"
    elif "permission" in error_msg or "权限" in error_msg:
        return False, "权限不足，请检查账户交易权限"
    elif "time" in error_msg or "时间" in error_msg:
        return False, "非交易时间或服务器时间异常"
    else:
        return False, f"未知错误: {data}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

success, message = handle_unlock_errors(trd_ctx, "123456")
print(f"解锁结果: {message}")

if success:
    print("可以进行交易操作")
    # 执行交易操作...
else:
    print("解锁失败，请根据提示处理")

trd_ctx.close()
```

### 注意事项

1. **密码安全**：不要在代码中硬编码密码，使用环境变量或安全输入
2. **会话保持**：解锁后的会话有时间限制，可能需要定期重新解锁
3. **多市场解锁**：不同市场（港股、美股、A股等）需要分别解锁
4. **错误重试**：解锁失败时应当适当重试，但要避免频繁重试导致账户锁定
5. **资源管理**：使用完毕后及时关闭交易上下文

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [获取账户列表](get-acc-list.md) - 获取可用账户
- [下单](place-order.md) - 交易下单
- [查询账户资金](accinfo-query.md) - 账户资金查询