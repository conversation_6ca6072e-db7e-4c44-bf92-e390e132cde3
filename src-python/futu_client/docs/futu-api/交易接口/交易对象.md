# 交易对象

交易对象是进行股票交易的核心接口，通过创建交易上下文来管理账户、下单、查询持仓等交易操作。

## 交易上下文类型

富途API根据不同市场提供了专门的交易上下文：

### OpenHKTradeContext - 港股交易

用于港股、港股期权、港股期货的交易。

```python
import futu as ft

# 创建港股交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
```

### OpenUSTradeContext - 美股交易

用于美股、美股期权、美股期货的交易。

```python
import futu as ft

# 创建美股交易上下文
trd_ctx = ft.OpenUSTradeContext(host='127.0.0.1', port=11111)
```

### OpenCNTradeContext - A股交易

用于A股（沪深港通）的交易。

```python
import futu as ft

# 创建A股交易上下文
trd_ctx = ft.OpenCNTradeContext(host='127.0.0.1', port=11111)
```

### OpenFutureTradeContext - 期货交易

用于期货合约的交易。

```python
import futu as ft

# 创建期货交易上下文
trd_ctx = ft.OpenFutureTradeContext(host='127.0.0.1', port=11111)
```

## 基本用法

### 构造参数

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| host | str | OpenD的IP地址 | '127.0.0.1' |
| port | int | OpenD的端口号 | 11111 |
| is_encrypt | bool | 是否启用加密 | None |
| is_async_connect | bool | 是否异步连接 | False |

### 连接管理

#### close()
关闭交易上下文连接，释放资源。

```python
trd_ctx.close()
```

## 核心功能

### 1. 交易解锁

在进行任何交易操作前，必须先解锁交易。

#### unlock_trade(password, password_md5=None, is_unlock=True)

**参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| password | str | 是 | 交易密码明文或MD5 |
| password_md5 | str | 否 | 密码的MD5值 |
| is_unlock | bool | 否 | True解锁，False锁定，默认True |

**示例：**

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

# 解锁交易
ret, data = trd_ctx.unlock_trade("123456")  # 您的交易密码
if ret == ft.RET_OK:
    print('交易解锁成功')
else:
    print('交易解锁失败:', data)

trd_ctx.close()
```

### 2. 账户管理

#### get_acc_list()

获取交易账户列表。

**返回DataFrame字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| acc_id | int | 账户ID |
| trd_env | str | 交易环境（REAL/SIMULATE） |
| acc_type | str | 账户类型 |
| card_num | str | 卡号 |
| security_firm | str | 券商 |
| sim_acc_type | str | 模拟账户类型 |

**示例：**

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 获取账户列表
ret, data = trd_ctx.get_acc_list()
if ret == ft.RET_OK:
    print("账户列表:")
    print(data)
else:
    print('获取账户列表失败:', data)

trd_ctx.close()
```

### 3. 下单交易

#### place_order(price, qty, code, trd_side, order_type=OrderType.NORMAL, trd_env=TrdEnv.REAL, acc_id=0)

**参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| price | float | 是 | 价格 |
| qty | int | 是 | 数量 |
| code | str | 是 | 股票代码 |
| trd_side | TrdSide | 是 | 交易方向（买/卖） |
| order_type | OrderType | 否 | 订单类型 |
| trd_env | TrdEnv | 否 | 交易环境 |
| acc_id | int | 否 | 账户ID |

**示例：**

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 下买单
ret, data = trd_ctx.place_order(
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    order_type=ft.OrderType.NORMAL,
    trd_env=ft.TrdEnv.SIMULATE  # 模拟环境
)

if ret == ft.RET_OK:
    print('下单成功:', data)
else:
    print('下单失败:', data)

trd_ctx.close()
```

### 4. 订单管理

#### modify_order(modify_order_op, order_id, qty=0, price=0.0, adjust_limit=0, trd_env=TrdEnv.REAL, acc_id=0)

修改订单。

#### cancel_order(order_id, trd_env=TrdEnv.REAL, acc_id=0)

撤销订单。

#### cancel_all_order(trd_env=TrdEnv.REAL, acc_id=0)

撤销所有订单。

### 5. 查询功能

#### accinfo_query(trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)

查询账户资金。

#### position_list_query(code='', pl_ratio_min=None, pl_ratio_max=None, trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)

查询持仓列表。

#### order_list_query(order_id='', status_filter_list=[], code='', start='', end='', trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)

查询订单列表。

## 完整交易流程示例

```python
import futu as ft
import time

def complete_trading_example():
    """完整交易流程示例"""
    
    # 1. 创建交易上下文
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        # 2. 解锁交易
        ret, data = trd_ctx.unlock_trade("123456")
        if ret != ft.RET_OK:
            print('交易解锁失败:', data)
            return
        print('交易解锁成功')
        
        # 3. 获取账户列表
        ret, acc_data = trd_ctx.get_acc_list()
        if ret != ft.RET_OK:
            print('获取账户失败:', acc_data)
            return
        
        print("可用账户:")
        print(acc_data)
        
        # 使用第一个模拟账户
        sim_acc = acc_data[acc_data['trd_env'] == 'SIMULATE']
        if sim_acc.empty:
            print('没有找到模拟账户')
            return
        
        acc_id = sim_acc.iloc[0]['acc_id']
        print(f"使用账户ID: {acc_id}")
        
        # 4. 查询账户资金
        ret, fund_data = trd_ctx.accinfo_query(trd_env=ft.TrdEnv.SIMULATE, acc_id=acc_id)
        if ret == ft.RET_OK:
            print("账户资金:")
            print(fund_data[['total_assets', 'cash', 'market_val']])
        
        # 5. 查询当前持仓
        ret, pos_data = trd_ctx.position_list_query(trd_env=ft.TrdEnv.SIMULATE, acc_id=acc_id)
        if ret == ft.RET_OK:
            print(f"当前持仓数量: {len(pos_data)}")
            if not pos_data.empty:
                print(pos_data[['code', 'qty', 'nominal_price', 'market_val']])
        
        # 6. 下单买入
        ret, order_data = trd_ctx.place_order(
            price=100.0,
            qty=100,
            code="HK.00700",
            trd_side=ft.TrdSide.BUY,
            order_type=ft.OrderType.NORMAL,
            trd_env=ft.TrdEnv.SIMULATE,
            acc_id=acc_id
        )
        
        if ret == ft.RET_OK:
            order_id = order_data['order_id'].iloc[0]
            print(f'下单成功，订单ID: {order_id}')
            
            # 7. 查询订单状态
            time.sleep(2)  # 等待订单处理
            ret, order_list = trd_ctx.order_list_query(
                trd_env=ft.TrdEnv.SIMULATE, 
                acc_id=acc_id
            )
            if ret == ft.RET_OK:
                latest_order = order_list[order_list['order_id'] == order_id]
                if not latest_order.empty:
                    status = latest_order['order_status'].iloc[0]
                    print(f'订单状态: {status}')
            
            # 8. 撤单（如果需要）
            # ret, cancel_data = trd_ctx.cancel_order(order_id, trd_env=ft.TrdEnv.SIMULATE, acc_id=acc_id)
            # if ret == ft.RET_OK:
            #     print('撤单成功')
        else:
            print('下单失败:', order_data)
            
    except Exception as e:
        print(f'交易过程中发生异常: {e}')
    finally:
        # 9. 关闭连接
        trd_ctx.close()

# 运行示例
if __name__ == "__main__":
    complete_trading_example()
```

## 交易环境说明

### TrdEnv（交易环境）

| 常量 | 说明 |
|------|------|
| REAL | 真实交易环境 |
| SIMULATE | 模拟交易环境 |

### TrdSide（交易方向）

| 常量 | 说明 |
|------|------|
| BUY | 买入 |
| SELL | 卖出 |

### OrderType（订单类型）

| 常量 | 说明 |
|------|------|
| NORMAL | 普通订单（港股：增强限价单） |
| MARKET | 市价单 |
| ABSOLUTE_LIMIT | 绝对限价单 |
| AUCTION | 竞价单 |
| AUCTION_LIMIT | 竞价限价单 |
| SPECIAL_LIMIT | 特别限价单 |

## 回调处理

### 设置交易推送回调

```python
import futu as ft

class TradeOrderHandler(ft.TradeOrderHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super(TradeOrderHandler, self).on_recv_rsp(rsp_pb)
        if ret_code != ft.RET_OK:
            print("订单推送错误:", data)
            return ft.RET_ERROR, data
        
        print("收到订单推送:")
        print(data)
        return ft.RET_OK, data

# 设置回调
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
handler = TradeOrderHandler()
trd_ctx.set_handler(handler)

# 订阅交易推送
trd_ctx.sub_acc_push(acc_id_list=[])
```

## 注意事项

1. **交易解锁**：每次使用交易功能前必须先解锁
2. **账户选择**：确保使用正确的账户ID和交易环境
3. **风险控制**：建议先在模拟环境测试策略
4. **连接管理**：使用完毕后及时关闭连接
5. **错误处理**：对所有交易操作进行充分的错误检查

## 相关接口

- [获取账户列表](get-acc-list.md) - 账户管理
- [下单](place-order.md) - 交易下单
- [查询持仓](position-list-query.md) - 持仓查询
- [查询订单](order-list-query.md) - 订单查询