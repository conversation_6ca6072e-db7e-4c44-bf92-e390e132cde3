# 账户列表

> 对应官方文档: `/futu-api-doc/trade/get-acc-list.html`

## 接口介绍

获取交易账户列表，包括模拟账户和真实账户信息。

## 函数原型

```python
get_acc_list()
```

## 使用示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

ret, data = trd_ctx.get_acc_list()

if ret == RET_OK:
    print("账户列表:")
    print(data)
    for index, row in data.iterrows():
        print(f"账户ID: {row['acc_id']}")
        print(f"账户类型: {row['trd_env']}")
        print(f"交易市场: {row['trd_market']}")
        print(f"账户状态: {row['acc_status']}")
        print(f"账户名称: {row['acc_name']}")
        print("-" * 40)
else:
    print('获取账户列表失败:', data)

trd_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| acc_id | int | 账户ID |
| trd_env | str | 交易环境 |
| trd_market | str | 交易市场 |
| acc_status | str | 账户状态 |
| acc_name | str | 账户名称 |
| card_num | str | 卡号 |
| security_firm | str | 券商名称 |

## 交易环境类型

| 类型 | 说明 |
|------|------|
| TrdEnv.SIMULATE | 模拟交易 |
| TrdEnv.REAL | 真实交易 |

## 交易市场类型

| 类型 | 说明 |
|------|------|
| TrdMarket.HK | 港股交易 |
| TrdMarket.US | 美股交易 |
| TrdMarket.CN | A股交易 |

## 账户筛选示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

ret, data = trd_ctx.get_acc_list()

if ret == RET_OK:
    # 筛选真实账户
    real_accounts = data[data['trd_env'] == 'REAL']
    print(f"真实账户数量: {len(real_accounts)}")
    
    # 筛选模拟账户
    sim_accounts = data[data['trd_env'] == 'SIMULATE']
    print(f"模拟账户数量: {len(sim_accounts)}")
    
    # 按市场分类
    hk_accounts = data[data['trd_market'] == 'HK']
    us_accounts = data[data['trd_market'] == 'US']
    
    print(f"港股账户: {len(hk_accounts)}个")
    print(f"美股账户: {len(us_accounts)}个")
    
    # 显示活跃账户
    active_accounts = data[data['acc_status'] == 'ENABLE']
    print(f"\n活跃账户:")
    for _, row in active_accounts.iterrows():
        print(f"  {row['acc_name']} ({row['trd_market']})")

trd_ctx.close()
```

## 账户切换示例

```python
from futu import *

# 获取港股账户
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

ret, acc_list = trd_ctx.get_acc_list()

if ret == RET_OK and len(acc_list) > 0:
    # 选择第一个账户
    acc_id = acc_list.iloc[0]['acc_id']
    print(f"选择账户ID: {acc_id}")
    
    # 获取账户资金信息
    ret, funds = trd_ctx.get_acc_fund(acc_id=acc_id)
    
    if ret == RET_OK:
        print("账户资金信息:")
        print(funds)
    
    # 获取账户持仓
    ret, positions = trd_ctx.get_position_list(acc_id=acc_id)
    
    if ret == RET_OK:
        print(f"\n持仓数量: {len(positions)}")
        for _, pos in positions.iterrows():
            print(f"  {pos['code']}: {pos['qty']}股")

trd_ctx.close()
```

## 多账户监控示例

```python
from futu import *
import time

def monitor_all_accounts():
    trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)
    
    ret, acc_list = trd_ctx.get_acc_list()
    
    if ret == RET_OK:
        print(f"\n{time.strftime('%H:%M:%S')} 账户监控:")
        
        for _, acc in acc_list.iterrows():
            if acc['acc_status'] == 'ENABLE':
                print(f"\n账户: {acc['acc_name']} ({acc['trd_market']})")
                
                # 获取资金信息
                ret, funds = trd_ctx.get_acc_fund(acc_id=acc['acc_id'])
                if ret == RET_OK:
                    power = funds.iloc[0]['power']
                    print(f"  购买力: {power:,.2f}")
                
                # 获取持仓数量
                ret, positions = trd_ctx.get_position_list(acc_id=acc['acc_id'])
                if ret == RET_OK:
                    print(f"  持仓数量: {len(positions)}只")
    
    trd_ctx.close()

# 定期监控
try:
    while True:
        monitor_all_accounts()
        time.sleep(300)  # 5分钟更新一次
except KeyboardInterrupt:
    print("\n监控已停止")
```

## 应用场景

1. **账户管理**: 管理多个交易账户
2. **策略部署**: 在不同账户间部署交易策略
3. **风险控制**: 监控各账户的风险状况
4. **资金分配**: 在多账户间分配资金

## 注意事项

1. 账户列表需要OpenD网关连接
2. 真实账户操作需要解锁交易密码
3. 不同市场的账户需要分别管理
4. 账户状态可能影响交易功能