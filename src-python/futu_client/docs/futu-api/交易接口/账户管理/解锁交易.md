# 解锁交易

> 对应官方文档: `/futu-api-doc/trade/unlock-trade.html`

## 接口介绍

解锁交易密码，用于真实交易环境下的交易操作。

## 函数原型

```python
unlock_trade(password, password_md5=None, is_unlock=True)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| password | str | 交易密码明文 |
| password_md5 | str | 交易密码MD5（可选） |
| is_unlock | bool | True解锁，False上锁 |

## 使用示例

```python
from futu import *

# 连接真实交易环境
trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, 
                              host='127.0.0.1', 
                              port=11111,
                              is_encrypt=True)

# 解锁交易密码
password = "your_trade_password"
ret, data = trd_ctx.unlock_trade(password)

if ret == RET_OK:
    print("交易密码解锁成功")
    
    # 现在可以进行交易操作
    # 下单示例
    ret, order_data = trd_ctx.place_order(
        price=100.0,
        qty=100,
        code="HK.00700",
        trd_side=TrdSide.BUY,
        order_type=OrderType.NORMAL,
        trd_env=TrdEnv.REAL
    )
    
    if ret == RET_OK:
        print("下单成功:", order_data)
    else:
        print("下单失败:", order_data)

else:
    print("解锁失败:", data)

trd_ctx.close()
```

## MD5密码示例

```python
from futu import *
import hashlib

# 使用MD5加密密码
password = "your_trade_password"
password_md5 = hashlib.md5(password.encode()).hexdigest()

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, 
                              host='127.0.0.1', 
                              port=11111)

# 使用MD5密码解锁
ret, data = trd_ctx.unlock_trade(password_md5=password_md5)

if ret == RET_OK:
    print("MD5密码解锁成功")
else:
    print("MD5密码解锁失败:", data)

trd_ctx.close()
```

## 自动解锁示例

```python
from futu import *
import time

class AutoTradingBot:
    def __init__(self, password, market=TrdMarket.HK):
        self.password = password
        self.market = market
        self.trd_ctx = None
        self.is_unlocked = False
    
    def connect_and_unlock(self):
        """连接并解锁交易"""
        try:
            self.trd_ctx = OpenSecTradeContext(
                filter_trdmarket=self.market,
                host='127.0.0.1',
                port=11111,
                is_encrypt=True
            )
            
            # 解锁交易
            ret, data = self.trd_ctx.unlock_trade(self.password)
            
            if ret == RET_OK:
                self.is_unlocked = True
                print("交易解锁成功")
                return True
            else:
                print("交易解锁失败:", data)
                return False
                
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def ensure_unlocked(self):
        """确保交易已解锁"""
        if not self.is_unlocked:
            return self.connect_and_unlock()
        return True
    
    def place_order_with_unlock(self, code, price, qty, side):
        """自动解锁后下单"""
        if not self.ensure_unlocked():
            return RET_ERROR, "无法解锁交易"
        
        ret, data = self.trd_ctx.place_order(
            price=price,
            qty=qty,
            code=code,
            trd_side=side,
            order_type=OrderType.NORMAL,
            trd_env=TrdEnv.REAL
        )
        
        return ret, data
    
    def close(self):
        """关闭连接"""
        if self.trd_ctx:
            self.trd_ctx.close()

# 使用示例
bot = AutoTradingBot("your_password", TrdMarket.HK)

# 自动解锁并下单
ret, order_data = bot.place_order_with_unlock(
    code="HK.00700",
    price=100.0,
    qty=100,
    side=TrdSide.BUY
)

if ret == RET_OK:
    print("下单成功:", order_data)
else:
    print("下单失败:", order_data)

bot.close()
```

## 上锁交易示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, 
                              host='127.0.0.1', 
                              port=11111)

# 解锁交易
ret, data = trd_ctx.unlock_trade("your_password")

if ret == RET_OK:
    print("交易已解锁")
    
    # 执行交易操作
    # ... 交易代码 ...
    
    # 交易完成后上锁
    ret, data = trd_ctx.unlock_trade("your_password", is_unlock=False)
    
    if ret == RET_OK:
        print("交易已上锁")
    else:
        print("上锁失败:", data)

trd_ctx.close()
```

## 安全最佳实践

```python
import os
from futu import *

# 从环境变量读取密码
password = os.getenv('FUTU_TRADE_PASSWORD')

if not password:
    print("请设置环境变量 FUTU_TRADE_PASSWORD")
    exit(1)

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, 
                              host='127.0.0.1', 
                              port=11111,
                              is_encrypt=True)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade(password)
    
    if ret == RET_OK:
        print("交易解锁成功")
        
        # 进行交易操作
        # ... 交易代码 ...
        
    else:
        print("解锁失败:", data)
        
finally:
    # 确保连接关闭
    trd_ctx.close()
```

## 注意事项

1. **安全性**: 不要在代码中硬编码密码
2. **环境**: 仅在真实交易环境下需要解锁
3. **频率**: 解锁后有效期较长，无需频繁解锁
4. **错误处理**: 解锁失败时要有适当的错误处理
5. **连接加密**: 建议使用加密连接传输密码

## 应用场景

1. **自动化交易**: 程序化交易系统
2. **批量操作**: 批量下单或撤单
3. **实时交易**: 基于实时数据的交易决策
4. **风险管理**: 自动止损止盈操作