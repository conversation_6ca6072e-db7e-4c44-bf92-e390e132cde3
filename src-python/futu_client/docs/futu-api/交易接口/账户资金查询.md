# 查询账户资金

查询交易账户的资金信息，包括总资产、现金、购买力、盈亏等详细财务数据。

## accinfo_query - 查询账户资金

### 接口原型

```python
accinfo_query(trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)
```

### 功能说明

查询指定账户的资金信息，包括现金、市值、盈亏、购买力等详细数据。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |
| refresh_cache | bool | 否 | 是否刷新缓存，默认False |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 账户资金DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| power | float | 购买力 |
| max_power_short | float | 最大做空购买力 |
| net_cash_power | float | 现金购买力 |
| total_assets | float | 总资产 |
| cash | float | 现金 |
| market_val | float | 市值 |
| long_mv | float | 多头市值 |
| short_mv | float | 空头市值 |
| pending_asset | float | 待交收资产 |
| max_withdraw | float | 最大可提取资金 |
| currency | str | 币种 |
| unrealized_pl | float | 未实现盈亏 |
| realized_pl | float | 已实现盈亏 |
| interest_charges | float | 利息费用 |
| dividend | float | 分红 |

### 代码示例

#### 基本资金查询

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 查询账户资金
    ret, acc_info = trd_ctx.accinfo_query(trd_env=ft.TrdEnv.SIMULATE)
    
    if ret == ft.RET_OK:
        acc_data = acc_info.iloc[0]  # 获取第一行数据
        
        print("=== 账户资金概览 ===")
        print(f"总资产: {acc_data['total_assets']:,.2f} {acc_data['currency']}")
        print(f"现金: {acc_data['cash']:,.2f} {acc_data['currency']}")
        print(f"市值: {acc_data['market_val']:,.2f} {acc_data['currency']}")
        print(f"购买力: {acc_data['power']:,.2f} {acc_data['currency']}")
        print(f"未实现盈亏: {acc_data['unrealized_pl']:+,.2f} {acc_data['currency']}")
        print(f"已实现盈亏: {acc_data['realized_pl']:+,.2f} {acc_data['currency']}")
        
        # 计算资产比例
        if acc_data['total_assets'] > 0:
            cash_ratio = acc_data['cash'] / acc_data['total_assets'] * 100
            market_ratio = acc_data['market_val'] / acc_data['total_assets'] * 100
            
            print(f"\n=== 资产配置 ===")
            print(f"现金比例: {cash_ratio:.1f}%")
            print(f"股票比例: {market_ratio:.1f}%")
    else:
        print('查询账户资金失败:', acc_info)
        
finally:
    trd_ctx.close()
```

#### 多账户资金对比

```python
import futu as ft
import pandas as pd

def compare_accounts_funds(trd_ctx):
    """对比多个账户的资金情况"""
    
    # 获取账户列表
    ret, acc_list = trd_ctx.get_acc_list()
    if ret != ft.RET_OK:
        print("获取账户列表失败:", acc_list)
        return
    
    print("=== 多账户资金对比 ===\n")
    
    funds_summary = []
    
    for _, account in acc_list.iterrows():
        acc_id = account['acc_id']
        trd_env_str = account['trd_env']
        trd_env = ft.TrdEnv.SIMULATE if trd_env_str == 'SIMULATE' else ft.TrdEnv.REAL
        
        # 查询账户资金
        ret, acc_info = trd_ctx.accinfo_query(trd_env=trd_env, acc_id=acc_id)
        
        if ret == ft.RET_OK:
            funds = acc_info.iloc[0]
            
            funds_summary.append({
                'Account_ID': acc_id,
                'Environment': trd_env_str,
                'Currency': funds['currency'],
                'Total_Assets': funds['total_assets'],
                'Cash': funds['cash'],
                'Market_Value': funds['market_val'],
                'Unrealized_PL': funds['unrealized_pl'],
                'Realized_PL': funds['realized_pl']
            })
            
            print(f"账户 {acc_id} ({trd_env_str}):")
            print(f"  总资产: {funds['total_assets']:,.2f} {funds['currency']}")
            print(f"  现金: {funds['cash']:,.2f} {funds['currency']}")
            print(f"  市值: {funds['market_val']:,.2f} {funds['currency']}")
            print(f"  盈亏: {funds['unrealized_pl']:+,.2f} / {funds['realized_pl']:+,.2f}")
            print("")
        else:
            print(f"账户 {acc_id} 资金查询失败: {acc_info}")
    
    # 创建汇总表
    if funds_summary:
        summary_df = pd.DataFrame(funds_summary)
        print("资金汇总表:")
        print(summary_df.to_string(index=False, float_format='%.2f'))
        
        # 计算总计（按币种分组）
        print("\n按币种汇总:")
        currency_summary = summary_df.groupby('Currency').agg({
            'Total_Assets': 'sum',
            'Cash': 'sum',
            'Market_Value': 'sum',
            'Unrealized_PL': 'sum',
            'Realized_PL': 'sum'
        })
        print(currency_summary)

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

compare_accounts_funds(trd_ctx)

trd_ctx.close()
```

#### 资金监控和预警

```python
import futu as ft
import time
from datetime import datetime

class FundsMonitor:
    def __init__(self, trd_ctx, acc_id=0, trd_env=ft.TrdEnv.SIMULATE):
        self.trd_ctx = trd_ctx
        self.acc_id = acc_id
        self.trd_env = trd_env
        self.alert_thresholds = {
            'low_cash_ratio': 10.0,      # 现金比例低于10%
            'high_loss_ratio': -20.0,    # 总盈亏比例低于-20%
            'low_buying_power': 10000,   # 购买力低于10000
        }
        self.last_funds = None
    
    def check_funds_alerts(self):
        """检查资金预警"""
        ret, acc_info = self.trd_ctx.accinfo_query(
            trd_env=self.trd_env, 
            acc_id=self.acc_id,
            refresh_cache=True
        )
        
        if ret != ft.RET_OK:
            return None, []
        
        funds = acc_info.iloc[0]
        alerts = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 1. 现金比例预警
        if funds['total_assets'] > 0:
            cash_ratio = funds['cash'] / funds['total_assets'] * 100
            if cash_ratio < self.alert_thresholds['low_cash_ratio']:
                alerts.append({
                    'type': 'LOW_CASH_RATIO',
                    'message': f'现金比例过低: {cash_ratio:.1f}%',
                    'current': cash_ratio,
                    'threshold': self.alert_thresholds['low_cash_ratio'],
                    'time': current_time
                })
        
        # 2. 总盈亏预警
        total_pl = funds['unrealized_pl'] + funds['realized_pl']
        if funds['total_assets'] > 0:
            pl_ratio = total_pl / funds['total_assets'] * 100
            if pl_ratio < self.alert_thresholds['high_loss_ratio']:
                alerts.append({
                    'type': 'HIGH_LOSS_RATIO',
                    'message': f'总亏损比例过高: {pl_ratio:.1f}%',
                    'current': pl_ratio,
                    'threshold': self.alert_thresholds['high_loss_ratio'],
                    'time': current_time
                })
        
        # 3. 购买力预警
        if funds['power'] < self.alert_thresholds['low_buying_power']:
            alerts.append({
                'type': 'LOW_BUYING_POWER',
                'message': f'购买力不足: {funds["power"]:,.0f}',
                'current': funds['power'],
                'threshold': self.alert_thresholds['low_buying_power'],
                'time': current_time
            })
        
        return funds, alerts
    
    def monitor_funds_changes(self, check_interval=60):
        """监控资金变化"""
        print("开始监控账户资金变化...")
        
        while True:
            funds, alerts = self.check_funds_alerts()
            
            if funds is not None:
                # 检查资金变化
                if self.last_funds is not None:
                    changes = self.detect_funds_changes(self.last_funds, funds)
                    if changes:
                        print(f"\n{datetime.now().strftime('%H:%M:%S')} - 资金变化:")
                        for change in changes:
                            print(f"  {change}")
                
                # 显示预警
                if alerts:
                    print(f"\n{datetime.now().strftime('%H:%M:%S')} - 资金预警:")
                    for alert in alerts:
                        print(f"  [{alert['type']}] {alert['message']}")
                
                self.last_funds = funds
            
            time.sleep(check_interval)
    
    def detect_funds_changes(self, previous, current):
        """检测资金变化"""
        changes = []
        threshold = 1000  # 变化超过1000才报告
        
        # 检查各项资金变化
        fields_to_check = [
            ('total_assets', '总资产'),
            ('cash', '现金'),
            ('market_val', '市值'),
            ('unrealized_pl', '未实现盈亏'),
            ('realized_pl', '已实现盈亏')
        ]
        
        for field, name in fields_to_check:
            prev_val = previous[field]
            curr_val = current[field]
            change = curr_val - prev_val
            
            if abs(change) >= threshold:
                changes.append(f"{name}: {prev_val:,.0f} → {curr_val:,.0f} ({change:+,.0f})")
        
        return changes
    
    def generate_funds_report(self):
        """生成资金报告"""
        funds, alerts = self.check_funds_alerts()
        
        if funds is None:
            return "无法获取资金数据"
        
        report = f"""
=== 账户资金报告 ===
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
币种: {funds['currency']}

基本信息:
- 总资产: {funds['total_assets']:,.2f}
- 现金: {funds['cash']:,.2f}
- 市值: {funds['market_val']:,.2f}
- 购买力: {funds['power']:,.2f}

盈亏情况:
- 未实现盈亏: {funds['unrealized_pl']:+,.2f}
- 已实现盈亏: {funds['realized_pl']:+,.2f}
- 总盈亏: {funds['unrealized_pl'] + funds['realized_pl']:+,.2f}

风险指标:
- 现金比例: {funds['cash']/funds['total_assets']*100:.1f}%
- 股票比例: {funds['market_val']/funds['total_assets']*100:.1f}%
- 总盈亏比例: {(funds['unrealized_pl'] + funds['realized_pl'])/funds['total_assets']*100:+.1f}%
"""
        
        if alerts:
            report += "\n预警信息:\n"
            for alert in alerts:
                report += f"- [{alert['type']}] {alert['message']}\n"
        else:
            report += "\n✓ 无风险预警\n"
        
        return report

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

monitor = FundsMonitor(trd_ctx)

# 生成当前资金报告
print(monitor.generate_funds_report())

# 检查单次预警
funds, alerts = monitor.check_funds_alerts()
if alerts:
    print("\n当前预警:")
    for alert in alerts:
        print(f"  {alert['message']}")
else:
    print("\n账户资金状况正常")

# 持续监控（可选）
# monitor.monitor_funds_changes(check_interval=30)

trd_ctx.close()
```

#### 资金使用效率分析

```python
import futu as ft
from datetime import datetime, timedelta

def analyze_funds_efficiency(trd_ctx, days=30):
    """分析资金使用效率"""
    
    # 获取当前资金状况
    ret, acc_info = trd_ctx.accinfo_query(trd_env=ft.TrdEnv.SIMULATE)
    if ret != ft.RET_OK:
        print("获取资金信息失败:", acc_info)
        return
    
    current_funds = acc_info.iloc[0]
    
    # 获取历史交易记录
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
    
    ret, orders = trd_ctx.order_list_query(
        start=start_date,
        end=end_date,
        status_filter_list=[ft.OrderStatus.FILLED_ALL],
        trd_env=ft.TrdEnv.SIMULATE
    )
    
    if ret != ft.RET_OK or orders.empty:
        print("没有交易记录可分析")
        return
    
    print(f"=== 资金使用效率分析 ({days}天) ===\n")
    
    # 1. 基本资金信息
    print("1. 当前资金状况:")
    print(f"   总资产: {current_funds['total_assets']:,.2f} {current_funds['currency']}")
    print(f"   现金: {current_funds['cash']:,.2f} ({current_funds['cash']/current_funds['total_assets']*100:.1f}%)")
    print(f"   市值: {current_funds['market_val']:,.2f} ({current_funds['market_val']/current_funds['total_assets']*100:.1f}%)")
    print(f"   购买力: {current_funds['power']:,.2f}")
    
    # 2. 交易活跃度
    orders['deal_value'] = orders['dealt_qty'] * orders['dealt_avg_price']
    
    buy_orders = orders[orders['trd_side'] == 'BUY']
    sell_orders = orders[orders['trd_side'] == 'SELL']
    
    total_buy_value = buy_orders['deal_value'].sum()
    total_sell_value = sell_orders['deal_value'].sum()
    total_trade_value = total_buy_value + total_sell_value
    
    print(f"\n2. 交易活跃度:")
    print(f"   买入总额: {total_buy_value:,.0f}")
    print(f"   卖出总额: {total_sell_value:,.0f}")
    print(f"   总交易额: {total_trade_value:,.0f}")
    print(f"   日均交易额: {total_trade_value/days:,.0f}")
    
    # 3. 资金周转率
    if current_funds['total_assets'] > 0:
        turnover_rate = total_trade_value / current_funds['total_assets'] * 100
        print(f"   资金周转率: {turnover_rate:.1f}%")
        
        # 按月化年化计算
        monthly_turnover = turnover_rate * 30 / days
        annual_turnover = turnover_rate * 365 / days
        print(f"   月化周转率: {monthly_turnover:.1f}%")
        print(f"   年化周转率: {annual_turnover:.1f}%")
    
    # 4. 资金利用率评估
    cash_ratio = current_funds['cash'] / current_funds['total_assets'] * 100
    
    print(f"\n3. 资金利用率评估:")
    if cash_ratio > 50:
        efficiency = "较低 - 现金比例过高"
    elif cash_ratio > 20:
        efficiency = "中等 - 现金比例适中"
    elif cash_ratio > 5:
        efficiency = "较高 - 资金充分利用"
    else:
        efficiency = "很高 - 需注意风险"
    
    print(f"   现金比例: {cash_ratio:.1f}%")
    print(f"   利用率评估: {efficiency}")
    
    # 5. 盈亏效率
    total_pl = current_funds['unrealized_pl'] + current_funds['realized_pl']
    if current_funds['total_assets'] > 0:
        pl_ratio = total_pl / current_funds['total_assets'] * 100
        print(f"\n4. 盈亏效率:")
        print(f"   总盈亏: {total_pl:+,.2f}")
        print(f"   盈亏比例: {pl_ratio:+.2f}%")
        
        # 年化收益率估算
        annual_return = pl_ratio * 365 / days
        print(f"   年化收益率: {annual_return:+.2f}% (估算)")

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

analyze_funds_efficiency(trd_ctx, days=30)

trd_ctx.close()
```

### 字段详细说明

#### 购买力相关

- **power**: 总购买力，考虑保证金和风险控制
- **max_power_short**: 最大做空购买力
- **net_cash_power**: 净现金购买力

#### 资产组成

- **total_assets**: 总资产 = 现金 + 市值
- **cash**: 可用现金
- **market_val**: 持仓市值
- **long_mv**: 多头持仓市值
- **short_mv**: 空头持仓市值

#### 盈亏指标

- **unrealized_pl**: 未实现盈亏（浮动盈亏）
- **realized_pl**: 已实现盈亏（已平仓盈亏）

### 注意事项

1. **数据时效性**：资金数据可能有延迟，使用`refresh_cache=True`获取最新数据
2. **币种统一**：不同市场的资金以不同币种计价
3. **购买力计算**：购买力受到保证金要求和风险控制影响
4. **盈亏计算**：盈亏可能受汇率变动影响
5. **数据准确性**：部分数据可能因为结算时间而有差异

### 错误处理

```python
import futu as ft

def safe_query_funds(trd_ctx, **query_params):
    """安全查询资金，包含错误处理"""
    try:
        ret, data = trd_ctx.accinfo_query(**query_params)
        
        if ret == ft.RET_OK:
            return True, data
        else:
            # 处理常见错误
            error_msg = str(data)
            if "unlock" in error_msg.lower():
                return False, "需要先解锁交易"
            elif "account" in error_msg.lower():
                return False, "账户ID无效或无权限"
            elif "permission" in error_msg.lower():
                return False, "没有查询权限"
            else:
                return False, f"查询失败: {error_msg}"
                
    except Exception as e:
        return False, f"查询异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, result = safe_query_funds(
    trd_ctx,
    trd_env=ft.TrdEnv.SIMULATE,
    refresh_cache=True
)

if success:
    funds = result.iloc[0]
    print(f"查询成功 - 总资产: {funds['total_assets']:,.2f} {funds['currency']}")
else:
    print("查询失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [获取账户列表](get-acc-list.md) - 账户管理
- [查询持仓](position-list-query.md) - 持仓查询
- [查询订单](order-list-query.md) - 订单查询