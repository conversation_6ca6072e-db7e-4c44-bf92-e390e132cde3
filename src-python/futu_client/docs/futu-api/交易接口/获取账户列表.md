# 获取账户列表

获取当前用户的交易账户列表，包括真实账户和模拟账户信息。

## get_acc_list - 获取账户列表

### 接口原型

```python
get_acc_list()
```

### 功能说明

获取交易业务账户列表，返回用户可用的所有交易账户信息，包括账户ID、类型、券商等。

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 账户信息DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| acc_id | int | 账户ID，用于其他交易接口 |
| trd_env | str | 交易环境（REAL/SIMULATE） |
| acc_type | str | 账户类型 |
| card_num | str | 账户卡号 |
| security_firm | str | 券商名称 |
| sim_acc_type | str | 模拟账户类型 |
| trd_market_auth | list | 交易市场权限列表 |

### 代码示例

#### 基本用法

```python
import futu as ft

# 创建交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 获取账户列表
    ret, data = trd_ctx.get_acc_list()
    if ret == ft.RET_OK:
        print("账户列表:")
        print(data)
        
        # 显示账户基本信息
        for index, row in data.iterrows():
            print(f"\n账户 {index + 1}:")
            print(f"  账户ID: {row['acc_id']}")
            print(f"  交易环境: {row['trd_env']}")
            print(f"  账户类型: {row['acc_type']}")
            print(f"  券商: {row['security_firm']}")
            print(f"  交易权限: {row['trd_market_auth']}")
    else:
        print('获取账户列表失败:', data)
        
finally:
    trd_ctx.close()
```

#### 筛选特定账户

```python
import futu as ft

def get_account_by_type(trd_ctx, trd_env="SIMULATE"):
    """获取指定环境的账户"""
    ret, data = trd_ctx.get_acc_list()
    if ret != ft.RET_OK:
        return None, data
    
    # 筛选指定环境的账户
    filtered_accounts = data[data['trd_env'] == trd_env]
    
    if filtered_accounts.empty:
        return None, f"没有找到{trd_env}环境的账户"
    
    return filtered_accounts, "成功"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 获取模拟账户
sim_accounts, msg = get_account_by_type(trd_ctx, "SIMULATE")
if sim_accounts is not None:
    print("模拟账户:")
    print(sim_accounts[['acc_id', 'acc_type', 'security_firm']])
    
    # 使用第一个模拟账户
    sim_acc_id = sim_accounts.iloc[0]['acc_id']
    print(f"选择账户ID: {sim_acc_id}")
else:
    print("获取模拟账户失败:", msg)

# 获取真实账户
real_accounts, msg = get_account_by_type(trd_ctx, "REAL")
if real_accounts is not None:
    print("\n真实账户:")
    print(real_accounts[['acc_id', 'acc_type', 'security_firm']])
else:
    print("获取真实账户失败:", msg)

trd_ctx.close()
```

#### 账户权限检查

```python
import futu as ft

def check_market_permission(trd_ctx, market="HK"):
    """检查市场交易权限"""
    ret, data = trd_ctx.get_acc_list()
    if ret != ft.RET_OK:
        return False, data
    
    permissions = {}
    for _, account in data.iterrows():
        acc_id = account['acc_id']
        trd_env = account['trd_env']
        market_auth = account['trd_market_auth']
        
        has_permission = market in market_auth if market_auth else False
        
        permissions[f"{trd_env}_{acc_id}"] = {
            'acc_id': acc_id,
            'trd_env': trd_env,
            'has_permission': has_permission,
            'all_permissions': market_auth
        }
    
    return True, permissions

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, permissions = check_market_permission(trd_ctx, "HK")
if success:
    print("港股交易权限检查:")
    for key, info in permissions.items():
        status = "有权限" if info['has_permission'] else "无权限"
        print(f"账户{info['acc_id']}({info['trd_env']}): {status}")
        print(f"  全部权限: {info['all_permissions']}")
else:
    print("权限检查失败:", permissions)

trd_ctx.close()
```

#### 账户管理器

```python
import futu as ft

class AccountManager:
    def __init__(self, trd_ctx):
        self.trd_ctx = trd_ctx
        self.accounts = None
        self._load_accounts()
    
    def _load_accounts(self):
        """加载账户信息"""
        ret, data = self.trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            self.accounts = data
        else:
            print(f"加载账户失败: {data}")
    
    def get_accounts_by_env(self, trd_env):
        """按环境获取账户"""
        if self.accounts is None:
            return []
        return self.accounts[self.accounts['trd_env'] == trd_env]
    
    def get_default_account(self, trd_env="SIMULATE", market="HK"):
        """获取默认账户"""
        accounts = self.get_accounts_by_env(trd_env)
        if accounts.empty:
            return None
        
        # 筛选有指定市场权限的账户
        for _, account in accounts.iterrows():
            market_auth = account['trd_market_auth']
            if market_auth and market in market_auth:
                return account['acc_id']
        
        # 如果没有找到有权限的账户，返回第一个
        return accounts.iloc[0]['acc_id']
    
    def get_account_info(self, acc_id):
        """获取指定账户信息"""
        if self.accounts is None:
            return None
        
        account = self.accounts[self.accounts['acc_id'] == acc_id]
        if account.empty:
            return None
        
        return account.iloc[0].to_dict()
    
    def list_all_accounts(self):
        """列出所有账户"""
        if self.accounts is None:
            print("没有账户信息")
            return
        
        print("所有账户:")
        print("-" * 60)
        for _, account in self.accounts.iterrows():
            print(f"账户ID: {account['acc_id']}")
            print(f"环境: {account['trd_env']}")
            print(f"类型: {account['acc_type']}")
            print(f"券商: {account['security_firm']}")
            print(f"权限: {account['trd_market_auth']}")
            print("-" * 60)

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 创建账户管理器
account_mgr = AccountManager(trd_ctx)

# 列出所有账户
account_mgr.list_all_accounts()

# 获取默认模拟账户
default_sim_acc = account_mgr.get_default_account("SIMULATE", "HK")
if default_sim_acc:
    print(f"\n默认模拟账户: {default_sim_acc}")
    
    # 获取账户详细信息
    acc_info = account_mgr.get_account_info(default_sim_acc)
    print("账户详情:", acc_info)

trd_ctx.close()
```

### 账户类型说明

#### trd_env（交易环境）

| 值 | 说明 |
|----|------|
| REAL | 真实交易环境 |
| SIMULATE | 模拟交易环境 |

#### acc_type（账户类型）

| 值 | 说明 |
|----|------|
| CASH | 现金账户 |
| MARGIN | 保证金账户 |

#### trd_market_auth（交易市场权限）

| 值 | 说明 |
|----|------|
| HK | 港股交易权限 |
| US | 美股交易权限 |
| CN | A股交易权限 |
| FUTURES | 期货交易权限 |

### 注意事项

1. **解锁要求**：调用此接口前需要先解锁交易
2. **权限检查**：确认账户具有相应的市场交易权限
3. **账户选择**：在后续交易操作中使用正确的账户ID
4. **环境区分**：真实环境和模拟环境的账户是分开的
5. **券商差异**：不同券商的账户可能有不同的交易规则

### 常见问题

**Q: 为什么获取不到账户列表？**
A: 请确保已经正确解锁交易，并且OpenD已登录富途账号。

**Q: 模拟账户和真实账户有什么区别？**
A: 模拟账户使用虚拟资金，不会产生真实交易；真实账户使用实际资金，会产生真实的交易记录。

**Q: 如何知道账户有哪些交易权限？**
A: 查看`trd_market_auth`字段，包含该账户的所有交易市场权限。

### 错误处理

```python
import futu as ft

def safe_get_account_list(trd_ctx):
    """安全获取账户列表"""
    try:
        ret, data = trd_ctx.get_acc_list()
        
        if ret == ft.RET_OK:
            if data.empty:
                return False, "账户列表为空，请检查账户权限"
            return True, data
        else:
            error_msg = str(data)
            if "unlock" in error_msg.lower():
                return False, "需要先解锁交易"
            elif "login" in error_msg.lower():
                return False, "需要先登录OpenD"
            else:
                return False, f"获取失败: {error_msg}"
                
    except Exception as e:
        return False, f"获取异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

# 先尝试解锁
ret, data = trd_ctx.unlock_trade("123456")
if ret != ft.RET_OK:
    print("解锁失败:", data)
else:
    # 安全获取账户列表
    success, result = safe_get_account_list(trd_ctx)
    if success:
        print("获取账户成功:")
        print(result)
    else:
        print("获取账户失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [解锁交易](unlock.md) - 交易解锁
- [获取账户资金](accinfo-query.md) - 查询账户资金
- [获取持仓](position-list-query.md) - 查询账户持仓