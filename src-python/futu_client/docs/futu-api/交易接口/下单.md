# 下单

下单是交易的核心功能，支持多种订单类型和交易方向，可用于买入、卖出股票。

## place_order - 下单

### 接口原型

```python
place_order(price, qty, code, trd_side, order_type=OrderType.NORMAL, trd_env=TrdEnv.REAL, acc_id=0, remark='')
```

### 功能说明

提交买卖订单到交易所。支持限价单、市价单等多种订单类型。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| price | float | 是 | 订单价格（市价单可传0） |
| qty | int | 是 | 订单数量 |
| code | str | 是 | 股票代码，例如'HK.00700' |
| trd_side | TrdSide | 是 | 交易方向，买入或卖出 |
| order_type | OrderType | 否 | 订单类型，默认NORMAL |
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |
| remark | str | 否 | 订单备注 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 下单结果DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| trd_env | str | 交易环境 |
| order_id | str | 订单号 |
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| qty | int | 订单数量 |
| price | float | 订单价格 |
| create_time | str | 创建时间 |
| updated_time | str | 更新时间 |
| order_type | str | 订单类型 |
| order_status | str | 订单状态 |
| trd_side | str | 交易方向 |

### 代码示例

#### 基本下单

```python
import futu as ft

# 创建港股交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 买入腾讯100股，价格100港元
    ret, data = trd_ctx.place_order(
        price=100.0,
        qty=100,
        code="HK.00700",
        trd_side=ft.TrdSide.BUY,
        order_type=ft.OrderType.NORMAL,
        trd_env=ft.TrdEnv.SIMULATE  # 使用模拟环境
    )
    
    if ret == ft.RET_OK:
        print('下单成功:')
        print(f"订单ID: {data['order_id'].iloc[0]}")
        print(f"股票: {data['stock_name'].iloc[0]} ({data['code'].iloc[0]})")
        print(f"数量: {data['qty'].iloc[0]}")
        print(f"价格: {data['price'].iloc[0]}")
        print(f"方向: {data['trd_side'].iloc[0]}")
    else:
        print('下单失败:', data)
        
finally:
    trd_ctx.close()
```

#### 批量下单

```python
import futu as ft

def batch_place_orders(orders_info):
    """批量下单"""
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        # 解锁交易
        ret, data = trd_ctx.unlock_trade("123456")
        if ret != ft.RET_OK:
            print('交易解锁失败:', data)
            return
        
        results = []
        for order in orders_info:
            ret, data = trd_ctx.place_order(
                price=order['price'],
                qty=order['qty'],
                code=order['code'],
                trd_side=order['trd_side'],
                order_type=order.get('order_type', ft.OrderType.NORMAL),
                trd_env=ft.TrdEnv.SIMULATE
            )
            
            if ret == ft.RET_OK:
                order_id = data['order_id'].iloc[0]
                results.append({
                    'code': order['code'],
                    'order_id': order_id,
                    'status': 'SUCCESS'
                })
                print(f"下单成功: {order['code']}, 订单ID: {order_id}")
            else:
                results.append({
                    'code': order['code'],
                    'order_id': None,
                    'status': 'FAILED',
                    'error': str(data)
                })
                print(f"下单失败: {order['code']}, 错误: {data}")
        
        return results
        
    finally:
        trd_ctx.close()

# 批量下单示例
orders = [
    {'code': 'HK.00700', 'price': 100.0, 'qty': 100, 'trd_side': ft.TrdSide.BUY},
    {'code': 'HK.00001', 'price': 50.0, 'qty': 200, 'trd_side': ft.TrdSide.BUY},
    {'code': 'HK.00005', 'price': 30.0, 'qty': 300, 'trd_side': ft.TrdSide.BUY}
]

results = batch_place_orders(orders)
print("批量下单结果:", results)
```

#### 不同订单类型示例

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 1. 限价单（默认）
ret, data = trd_ctx.place_order(
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    order_type=ft.OrderType.NORMAL,
    trd_env=ft.TrdEnv.SIMULATE
)
print("限价单:", "成功" if ret == ft.RET_OK else f"失败-{data}")

# 2. 市价单
ret, data = trd_ctx.place_order(
    price=0,  # 市价单价格传0
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    order_type=ft.OrderType.MARKET,
    trd_env=ft.TrdEnv.SIMULATE
)
print("市价单:", "成功" if ret == ft.RET_OK else f"失败-{data}")

# 3. 竞价单（开盘竞价时使用）
ret, data = trd_ctx.place_order(
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    order_type=ft.OrderType.AUCTION,
    trd_env=ft.TrdEnv.SIMULATE
)
print("竞价单:", "成功" if ret == ft.RET_OK else f"失败-{data}")

trd_ctx.close()
```

#### 智能下单策略

```python
import futu as ft
import time

class SmartOrderManager:
    def __init__(self, trd_ctx, quote_ctx):
        self.trd_ctx = trd_ctx
        self.quote_ctx = quote_ctx
    
    def place_order_with_price_check(self, code, qty, trd_side, target_price, tolerance=0.02):
        """下单前检查价格合理性"""
        # 获取当前报价
        ret, quote_data = self.quote_ctx.get_stock_quote([code])
        if ret != ft.RET_OK:
            return False, "无法获取报价数据"
        
        current_price = quote_data['last_price'].iloc[0]
        
        # 检查价格偏差
        if trd_side == ft.TrdSide.BUY:
            # 买入价格不能太高于当前价
            if target_price > current_price * (1 + tolerance):
                return False, f"买入价格过高: {target_price} > {current_price * (1 + tolerance):.2f}"
        else:
            # 卖出价格不能太低于当前价
            if target_price < current_price * (1 - tolerance):
                return False, f"卖出价格过低: {target_price} < {current_price * (1 - tolerance):.2f}"
        
        # 下单
        ret, data = self.trd_ctx.place_order(
            price=target_price,
            qty=qty,
            code=code,
            trd_side=trd_side,
            order_type=ft.OrderType.NORMAL,
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        return ret == ft.RET_OK, data
    
    def place_order_by_percentage(self, code, qty, trd_side, percentage_offset=0):
        """按当前价格的百分比偏移下单"""
        ret, quote_data = self.quote_ctx.get_stock_quote([code])
        if ret != ft.RET_OK:
            return False, "无法获取报价数据"
        
        current_price = quote_data['last_price'].iloc[0]
        target_price = current_price * (1 + percentage_offset)
        
        # 价格精度处理（港股最小0.01）
        target_price = round(target_price, 2)
        
        ret, data = self.trd_ctx.place_order(
            price=target_price,
            qty=qty,
            code=code,
            trd_side=trd_side,
            order_type=ft.OrderType.NORMAL,
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        if ret == ft.RET_OK:
            print(f"下单成功: {code}, 当前价{current_price}, 下单价{target_price}")
        
        return ret == ft.RET_OK, data

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 订阅报价
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
    trd_ctx.unlock_trade("123456")
    
    smart_order = SmartOrderManager(trd_ctx, quote_ctx)
    
    # 按当前价格下跌1%的价格买入
    success, result = smart_order.place_order_by_percentage(
        'HK.00700', 100, ft.TrdSide.BUY, -0.01
    )
    
    if success:
        print("智能下单成功:", result['order_id'].iloc[0])
    else:
        print("智能下单失败:", result)
        
finally:
    quote_ctx.close()
    trd_ctx.close()
```

### 订单类型详解

#### OrderType（订单类型）

| 类型 | 说明 | 适用市场 | 备注 |
|------|------|----------|------|
| NORMAL | 普通订单 | 港股、美股、A股 | 港股为增强限价单 |
| MARKET | 市价单 | 港股、美股 | 以市场最优价成交 |
| ABSOLUTE_LIMIT | 绝对限价单 | 港股 | 只能以指定价格成交 |
| AUCTION | 竞价单 | 港股 | 开盘竞价时段使用 |
| AUCTION_LIMIT | 竞价限价单 | 港股 | 竞价时段的限价单 |
| SPECIAL_LIMIT | 特别限价单 | 港股 | 特殊限价单 |

#### TrdSide（交易方向）

| 类型 | 说明 |
|------|------|
| BUY | 买入 |
| SELL | 卖出 |
| SELL_SHORT | 卖空（需要相应权限） |
| BUY_BACK | 买回（平仓卖空） |

### 下单限制

#### 最小交易单位

| 市场 | 股票 | 期权 | 期货 |
|------|------|------|------|
| 港股 | 100股（1手） | 1张 | 1张 |
| 美股 | 1股 | 1张 | 1张 |
| A股 | 100股（1手） | - | - |

#### 价格精度

| 市场 | 价格范围 | 最小价差 |
|------|----------|----------|
| 港股 | ≤0.25 | 0.001 |
| 港股 | 0.25-0.5 | 0.005 |
| 港股 | 0.5-10 | 0.01 |
| 港股 | 10-20 | 0.02 |
| 港股 | 20-100 | 0.05 |
| 港股 | 100-200 | 0.1 |
| 港股 | 200-500 | 0.2 |
| 港股 | ≥500 | 0.5 |
| 美股 | 全部 | 0.01 |

### 注意事项

1. **交易解锁**：下单前必须先解锁交易
2. **资金检查**：确保账户有足够资金
3. **交易时间**：只能在市场开盘时间下单
4. **价格合理性**：价格应在合理范围内
5. **数量限制**：遵守最小交易单位
6. **风险控制**：建议先在模拟环境测试

### 错误处理

```python
import futu as ft

def safe_place_order(trd_ctx, **order_params):
    """安全下单，包含错误处理"""
    try:
        ret, data = trd_ctx.place_order(**order_params)
        
        if ret == ft.RET_OK:
            order_id = data['order_id'].iloc[0]
            return True, order_id
        else:
            # 处理常见错误
            error_msg = str(data)
            if "资金不足" in error_msg:
                return False, "账户资金不足"
            elif "超出涨跌停" in error_msg:
                return False, "价格超出涨跌停限制"
            elif "非交易时间" in error_msg:
                return False, "当前非交易时间"
            else:
                return False, f"下单失败: {error_msg}"
                
    except Exception as e:
        return False, f"下单异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, result = safe_place_order(
    trd_ctx,
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    trd_env=ft.TrdEnv.SIMULATE
)

if success:
    print(f"下单成功，订单ID: {result}")
else:
    print(f"下单失败: {result}")

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [修改订单](modify-order.md) - 修改已有订单
- [撤销订单](cancel-order.md) - 撤销订单
- [查询订单](order-list-query.md) - 查询订单状态