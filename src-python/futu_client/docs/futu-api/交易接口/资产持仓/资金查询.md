# 资金查询

> 对应官方文档: `/futu-api-doc/trade/accinfo-query.html`

## 接口介绍

查询交易账户的资金信息，包括总资产、可用资金、购买力等。

## 函数原型

```python
get_acc_fund(trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, refresh_cache=False, currency=Currency.NONE)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| trd_env | TrdEnv | 交易环境 |
| acc_id | int | 账户ID |
| acc_index | int | 账户索引 |
| refresh_cache | bool | 是否刷新缓存 |
| currency | Currency | 币种 |

## 使用示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

# 解锁交易（真实环境需要）
ret, data = trd_ctx.unlock_trade("your_password")

if ret == RET_OK:
    # 查询资金信息
    ret, fund_data = trd_ctx.get_acc_fund(trd_env=TrdEnv.REAL)
    
    if ret == RET_OK:
        print("账户资金信息:")
        fund = fund_data.iloc[0]
        
        print(f"总资产: {fund['total_assets']:,.2f}")
        print(f"净资产: {fund['net_assets']:,.2f}")
        print(f"现金: {fund['cash']:,.2f}")
        print(f"购买力: {fund['power']:,.2f}")
        print(f"最大购买力: {fund['max_power']:,.2f}")
        print(f"市值: {fund['market_val']:,.2f}")
        print(f"冻结资金: {fund['frozen_cash']:,.2f}")
        print(f"可提取现金: {fund['avl_withdrawal_cash']:,.2f}")
        
    else:
        print("查询资金失败:", fund_data)
else:
    print("解锁失败:", data)

trd_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| total_assets | float | 总资产 |
| net_assets | float | 净资产 |
| cash | float | 现金 |
| power | float | 购买力 |
| max_power | float | 最大购买力 |
| market_val | float | 市值 |
| frozen_cash | float | 冻结资金 |
| avl_withdrawal_cash | float | 可提取现金 |
| currency | str | 币种 |
| unrealized_pl | float | 未实现盈亏 |
| realized_pl | float | 已实现盈亏 |

## 多币种查询示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

# 查询不同币种的资金
currencies = [Currency.HKD, Currency.USD, Currency.CNY]
currency_names = ['港币', '美元', '人民币']

for currency, name in zip(currencies, currency_names):
    ret, fund_data = trd_ctx.get_acc_fund(currency=currency)
    
    if ret == RET_OK and len(fund_data) > 0:
        fund = fund_data.iloc[0]
        print(f"\n{name}资金:")
        print(f"  现金: {fund['cash']:,.2f}")
        print(f"  购买力: {fund['power']:,.2f}")
        print(f"  市值: {fund['market_val']:,.2f}")

trd_ctx.close()
```

## 资金监控示例

```python
from futu import *
import time

def monitor_account_fund():
    trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)
    
    try:
        while True:
            ret, fund_data = trd_ctx.get_acc_fund(refresh_cache=True)
            
            if ret == RET_OK:
                fund = fund_data.iloc[0]
                
                print(f"\n{time.strftime('%H:%M:%S')} 资金状况:")
                print(f"总资产: {fund['total_assets']:,.2f}")
                print(f"购买力: {fund['power']:,.2f}")
                print(f"未实现盈亏: {fund['unrealized_pl']:+,.2f}")
                
                # 计算资金使用率
                usage_rate = (fund['market_val'] / fund['total_assets']) * 100
                print(f"资金使用率: {usage_rate:.1f}%")
                
                # 风险警告
                if usage_rate > 80:
                    print("⚠️ 资金使用率较高，注意风险控制")
                
            time.sleep(60)  # 每分钟更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    finally:
        trd_ctx.close()

# 运行监控
monitor_account_fund()
```

## 资金分析示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

def analyze_account_fund():
    ret, fund_data = trd_ctx.get_acc_fund()
    
    if ret == RET_OK:
        fund = fund_data.iloc[0]
        
        print("资金分析报告:")
        print("=" * 40)
        
        # 基础信息
        print(f"总资产: {fund['total_assets']:,.2f}")
        print(f"净资产: {fund['net_assets']:,.2f}")
        print(f"现金: {fund['cash']:,.2f}")
        print(f"市值: {fund['market_val']:,.2f}")
        
        # 比例分析
        cash_ratio = (fund['cash'] / fund['total_assets']) * 100
        market_ratio = (fund['market_val'] / fund['total_assets']) * 100
        
        print(f"\n资产结构:")
        print(f"现金比例: {cash_ratio:.1f}%")
        print(f"股票比例: {market_ratio:.1f}%")
        
        # 购买力分析
        available_power = fund['power'] - fund['frozen_cash']
        print(f"\n购买力状况:")
        print(f"总购买力: {fund['power']:,.2f}")
        print(f"冻结资金: {fund['frozen_cash']:,.2f}")
        print(f"可用购买力: {available_power:,.2f}")
        
        # 盈亏分析
        total_pl = fund['unrealized_pl'] + fund['realized_pl']
        pl_ratio = (total_pl / fund['total_assets']) * 100
        
        print(f"\n盈亏分析:")
        print(f"未实现盈亏: {fund['unrealized_pl']:+,.2f}")
        print(f"已实现盈亏: {fund['realized_pl']:+,.2f}")
        print(f"总盈亏: {total_pl:+,.2f}")
        print(f"收益率: {pl_ratio:+.2f}%")
        
        # 风险提示
        print(f"\n风险评估:")
        if cash_ratio < 10:
            print("⚠️ 现金比例偏低，建议保留适当现金")
        if market_ratio > 90:
            print("⚠️ 股票仓位过高，注意分散风险")
        if fund['unrealized_pl'] < -fund['total_assets'] * 0.1:
            print("⚠️ 浮亏较大，注意止损")

analyze_account_fund()
trd_ctx.close()
```

## 应用场景

1. **风险管理**: 监控账户资金状况
2. **仓位控制**: 根据资金情况调整仓位
3. **投资决策**: 基于资金状况制定投资策略
4. **绩效分析**: 计算投资收益和风险指标

## 注意事项

1. 真实账户需要先解锁交易密码
2. 资金数据可能有延迟，建议刷新缓存
3. 不同币种需要分别查询
4. 购买力包含杠杆效应，注意风险控制