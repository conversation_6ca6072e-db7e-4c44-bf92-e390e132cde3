# 持仓查询

> 对应官方文档: `/futu-api-doc/trade/position-list-query.html`

## 接口介绍

查询交易账户的持仓信息，包括持仓股票、数量、成本价、盈亏等。

## 函数原型

```python
get_position_list(code='', pl_ratio_min=None, pl_ratio_max=None, trd_env=TrdEnv.REAL, acc_id=0, acc_index=0, refresh_cache=False)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码，空字符串表示全部 |
| pl_ratio_min | float | 盈亏比例下限 |
| pl_ratio_max | float | 盈亏比例上限 |
| trd_env | TrdEnv | 交易环境 |
| acc_id | int | 账户ID |
| acc_index | int | 账户索引 |
| refresh_cache | bool | 是否刷新缓存 |

## 使用示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

# 解锁交易（真实环境需要）
ret, data = trd_ctx.unlock_trade("your_password")

if ret == RET_OK:
    # 查询所有持仓
    ret, position_data = trd_ctx.get_position_list(trd_env=TrdEnv.REAL)
    
    if ret == RET_OK:
        print("持仓信息:")
        print(position_data)
        
        if len(position_data) > 0:
            for index, pos in position_data.iterrows():
                print(f"\n股票: {pos['code']} - {pos['stock_name']}")
                print(f"数量: {pos['qty']}")
                print(f"可卖数量: {pos['can_sell_qty']}")
                print(f"成本价: {pos['cost_price']:.3f}")
                print(f"当前价: {pos['nominal_price']:.3f}")
                print(f"市值: {pos['market_val']:,.2f}")
                print(f"盈亏: {pos['unrealized_pl']:+,.2f}")
                print(f"盈亏比例: {pos['unrealized_pl_ratio']:+.2f}%")
                print("-" * 50)
        else:
            print("暂无持仓")
            
    else:
        print("查询持仓失败:", position_data)
else:
    print("解锁失败:", data)

trd_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| qty | int | 持仓数量 |
| can_sell_qty | int | 可卖数量 |
| cost_price | float | 成本价 |
| cost_price_valid | bool | 成本价是否有效 |
| market_val | float | 市值 |
| nominal_price | float | 当前价 |
| unrealized_pl | float | 未实现盈亏 |
| unrealized_pl_ratio | float | 盈亏比例 |
| realized_pl | float | 已实现盈亏 |
| today_buy_qty | int | 今日买入数量 |
| today_buy_val | float | 今日买入价值 |
| today_sell_qty | int | 今日卖出数量 |
| today_sell_val | float | 今日卖出价值 |

## 持仓分析示例

```python
from futu import *
import pandas as pd

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

def analyze_positions():
    ret, positions = trd_ctx.get_position_list()
    
    if ret == RET_OK and len(positions) > 0:
        print("持仓分析报告:")
        print("=" * 50)
        
        # 基础统计
        total_market_val = positions['market_val'].sum()
        total_unrealized_pl = positions['unrealized_pl'].sum()
        total_realized_pl = positions['realized_pl'].sum()
        
        print(f"总持仓市值: {total_market_val:,.2f}")
        print(f"总未实现盈亏: {total_unrealized_pl:+,.2f}")
        print(f"总已实现盈亏: {total_realized_pl:+,.2f}")
        
        # 盈亏分析
        profit_positions = positions[positions['unrealized_pl'] > 0]
        loss_positions = positions[positions['unrealized_pl'] < 0]
        
        print(f"\n盈利股票: {len(profit_positions)}只")
        print(f"亏损股票: {len(loss_positions)}只")
        
        if len(profit_positions) > 0:
            print(f"盈利总额: {profit_positions['unrealized_pl'].sum():+,.2f}")
            print("最大盈利:")
            max_profit = profit_positions.loc[profit_positions['unrealized_pl'].idxmax()]
            print(f"  {max_profit['stock_name']}: {max_profit['unrealized_pl']:+,.2f}")
        
        if len(loss_positions) > 0:
            print(f"亏损总额: {loss_positions['unrealized_pl'].sum():+,.2f}")
            print("最大亏损:")
            max_loss = loss_positions.loc[loss_positions['unrealized_pl'].idxmin()]
            print(f"  {max_loss['stock_name']}: {max_loss['unrealized_pl']:+,.2f}")
        
        # 仓位分析
        print(f"\n仓位分布:")
        positions_sorted = positions.sort_values('market_val', ascending=False)
        for i, (_, pos) in enumerate(positions_sorted.head(5).iterrows()):
            weight = (pos['market_val'] / total_market_val) * 100
            print(f"  {i+1}. {pos['stock_name']}: {weight:.1f}%")
        
        # 今日交易
        today_buy_val = positions['today_buy_val'].sum()
        today_sell_val = positions['today_sell_val'].sum()
        
        if today_buy_val > 0 or today_sell_val > 0:
            print(f"\n今日交易:")
            print(f"买入价值: {today_buy_val:,.2f}")
            print(f"卖出价值: {today_sell_val:,.2f}")
            print(f"净流入: {today_buy_val - today_sell_val:+,.2f}")

analyze_positions()
trd_ctx.close()
```

## 筛选查询示例

```python
from futu import *

trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)

# 查询特定股票持仓
ret, position_data = trd_ctx.get_position_list(code='HK.00700')

if ret == RET_OK and len(position_data) > 0:
    pos = position_data.iloc[0]
    print(f"腾讯持仓:")
    print(f"数量: {pos['qty']}")
    print(f"成本价: {pos['cost_price']:.3f}")
    print(f"当前价: {pos['nominal_price']:.3f}")
    print(f"盈亏: {pos['unrealized_pl']:+,.2f}")

# 查询盈利股票
ret, profit_positions = trd_ctx.get_position_list(pl_ratio_min=0.05)  # 盈利5%以上

if ret == RET_OK and len(profit_positions) > 0:
    print(f"\n盈利股票({len(profit_positions)}只):")
    for _, pos in profit_positions.iterrows():
        print(f"{pos['stock_name']}: {pos['unrealized_pl_ratio']:+.2f}%")

# 查询亏损股票
ret, loss_positions = trd_ctx.get_position_list(pl_ratio_max=-0.05)  # 亏损5%以上

if ret == RET_OK and len(loss_positions) > 0:
    print(f"\n亏损股票({len(loss_positions)}只):")
    for _, pos in loss_positions.iterrows():
        print(f"{pos['stock_name']}: {pos['unrealized_pl_ratio']:+.2f}%")

trd_ctx.close()
```

## 实时监控示例

```python
from futu import *
import time

def monitor_positions():
    trd_ctx = OpenSecTradeContext(filter_trdmarket=TrdMarket.HK, host='127.0.0.1', port=11111)
    
    try:
        while True:
            ret, positions = trd_ctx.get_position_list(refresh_cache=True)
            
            if ret == RET_OK:
                print(f"\n{time.strftime('%H:%M:%S')} 持仓监控:")
                
                if len(positions) > 0:
                    total_pl = positions['unrealized_pl'].sum()
                    print(f"总盈亏: {total_pl:+,.2f}")
                    
                    # 显示变化较大的持仓
                    significant_changes = positions[abs(positions['unrealized_pl_ratio']) > 3]
                    
                    if len(significant_changes) > 0:
                        print("重要变化:")
                        for _, pos in significant_changes.iterrows():
                            direction = "📈" if pos['unrealized_pl'] > 0 else "📉"
                            print(f"  {direction} {pos['stock_name']}: {pos['unrealized_pl_ratio']:+.2f}%")
                    
                    # 风险提醒
                    large_losses = positions[positions['unrealized_pl_ratio'] < -10]
                    if len(large_losses) > 0:
                        print("⚠️ 大额亏损提醒:")
                        for _, pos in large_losses.iterrows():
                            print(f"  {pos['stock_name']}: {pos['unrealized_pl_ratio']:+.2f}%")
                
                else:
                    print("暂无持仓")
            
            time.sleep(30)  # 30秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    finally:
        trd_ctx.close()

# 运行监控
monitor_positions()
```

## 应用场景

1. **投资组合管理**: 监控和管理投资组合
2. **风险控制**: 识别大额亏损并及时止损
3. **绩效分析**: 分析投资收益和风险
4. **仓位调整**: 根据持仓情况调整投资策略

## 注意事项

1. 真实账户需要先解锁交易密码
2. 持仓数据可能有延迟，建议刷新缓存
3. 成本价计算可能因分红、拆股等调整
4. 可卖数量可能因T+2交收等规则限制