# 查询持仓

查询账户的股票持仓信息，包括持仓数量、成本价、市值、盈亏等详细数据。

## position_list_query - 查询持仓列表

### 接口原型

```python
position_list_query(code='', pl_ratio_min=None, pl_ratio_max=None, trd_env=TrdEnv.REAL, acc_id=0, refresh_cache=False)
```

### 功能说明

查询指定账户的持仓列表，支持按股票代码、盈亏比例等条件筛选。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 否 | 股票代码，空字符串表示所有 |
| pl_ratio_min | float | 否 | 盈亏比例最小值，例如-10.0表示-10% |
| pl_ratio_max | float | 否 | 盈亏比例最大值，例如10.0表示10% |
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |
| refresh_cache | bool | 否 | 是否刷新缓存，默认False |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 持仓数据DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| qty | int | 持仓数量 |
| can_sell_qty | int | 可卖数量 |
| cost_price | float | 成本价 |
| cost_price_valid | bool | 成本价是否有效 |
| market_val | float | 市值 |
| nominal_price | float | 名义价格 |
| unrealized_pl | float | 未实现盈亏 |
| unrealized_pl_ratio | float | 未实现盈亏比例(%) |
| realized_pl | float | 已实现盈亏 |
| today_buy_val | float | 今日买入价值 |
| today_buy_qty | int | 今日买入数量 |
| today_sell_val | float | 今日卖出价值 |
| today_sell_qty | int | 今日卖出数量 |
| position_side | str | 持仓方向（LONG/SHORT） |

### 代码示例

#### 查询所有持仓

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 查询所有持仓
    ret, positions = trd_ctx.position_list_query(trd_env=ft.TrdEnv.SIMULATE)
    
    if ret == ft.RET_OK:
        if positions.empty:
            print("当前没有持仓")
        else:
            print(f"持仓总数: {len(positions)}")
            print("\n持仓概览:")
            
            # 显示关键信息
            display_columns = ['code', 'stock_name', 'qty', 'cost_price', 
                             'market_val', 'unrealized_pl', 'unrealized_pl_ratio']
            print(positions[display_columns])
            
            # 计算总体盈亏
            total_market_val = positions['market_val'].sum()
            total_unrealized_pl = positions['unrealized_pl'].sum()
            
            print(f"\n持仓汇总:")
            print(f"总市值: {total_market_val:,.2f}")
            print(f"未实现盈亏: {total_unrealized_pl:,.2f}")
            print(f"整体盈亏比例: {(total_unrealized_pl/total_market_val*100):+.2f}%")
    else:
        print('查询持仓失败:', positions)
        
finally:
    trd_ctx.close()
```

#### 持仓详细分析

```python
import futu as ft
import pandas as pd

def analyze_positions(trd_ctx, trd_env=ft.TrdEnv.SIMULATE):
    """详细分析持仓"""
    ret, positions = trd_ctx.position_list_query(trd_env=trd_env)
    
    if ret != ft.RET_OK or positions.empty:
        print("没有持仓数据")
        return
    
    print("=== 持仓详细分析 ===\n")
    
    # 1. 盈亏分析
    profit_positions = positions[positions['unrealized_pl'] > 0]
    loss_positions = positions[positions['unrealized_pl'] < 0]
    
    print("1. 盈亏统计:")
    print(f"   盈利股票: {len(profit_positions)} 只")
    print(f"   亏损股票: {len(loss_positions)} 只")
    print(f"   盈利总额: {profit_positions['unrealized_pl'].sum():+,.2f}")
    print(f"   亏损总额: {loss_positions['unrealized_pl'].sum():+,.2f}")
    
    # 2. 最大盈亏股票
    if not positions.empty:
        max_profit = positions.loc[positions['unrealized_pl'].idxmax()]
        max_loss = positions.loc[positions['unrealized_pl'].idxmin()]
        
        print(f"\n2. 极值分析:")
        print(f"   最大盈利: {max_profit['stock_name']} ({max_profit['code']}) "
              f"{max_profit['unrealized_pl']:+,.2f} ({max_profit['unrealized_pl_ratio']:+.2f}%)")
        print(f"   最大亏损: {max_loss['stock_name']} ({max_loss['code']}) "
              f"{max_loss['unrealized_pl']:+,.2f} ({max_loss['unrealized_pl_ratio']:+.2f}%)")
    
    # 3. 持仓权重分析
    total_market_val = positions['market_val'].sum()
    positions['weight'] = positions['market_val'] / total_market_val * 100
    top_holdings = positions.nlargest(5, 'market_val')
    
    print(f"\n3. 持仓权重 (前5名):")
    for _, holding in top_holdings.iterrows():
        print(f"   {holding['stock_name']} ({holding['code']}): "
              f"{holding['weight']:.1f}% - {holding['market_val']:,.0f}")
    
    # 4. 今日交易活动
    today_active = positions[(positions['today_buy_qty'] > 0) | (positions['today_sell_qty'] > 0)]
    if not today_active.empty:
        print(f"\n4. 今日交易活动:")
        for _, pos in today_active.iterrows():
            if pos['today_buy_qty'] > 0:
                print(f"   买入 {pos['stock_name']}: {pos['today_buy_qty']} 股, "
                      f"金额 {pos['today_buy_val']:,.0f}")
            if pos['today_sell_qty'] > 0:
                print(f"   卖出 {pos['stock_name']}: {pos['today_sell_qty']} 股, "
                      f"金额 {pos['today_sell_val']:,.0f}")

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

analyze_positions(trd_ctx)

trd_ctx.close()
```

#### 按条件筛选持仓

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 1. 查询盈利超过5%的持仓
ret, profit_positions = trd_ctx.position_list_query(
    pl_ratio_min=5.0,
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not profit_positions.empty:
    print("盈利超过5%的持仓:")
    print(profit_positions[['code', 'stock_name', 'unrealized_pl', 'unrealized_pl_ratio']])

# 2. 查询亏损超过10%的持仓
ret, loss_positions = trd_ctx.position_list_query(
    pl_ratio_max=-10.0,
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not loss_positions.empty:
    print("\n亏损超过10%的持仓:")
    print(loss_positions[['code', 'stock_name', 'unrealized_pl', 'unrealized_pl_ratio']])

# 3. 查询特定股票持仓
ret, specific_position = trd_ctx.position_list_query(
    code='HK.00700',
    trd_env=ft.TrdEnv.SIMULATE
)

if ret == ft.RET_OK and not specific_position.empty:
    print("\n腾讯持仓详情:")
    pos = specific_position.iloc[0]
    print(f"持仓数量: {pos['qty']}")
    print(f"可卖数量: {pos['can_sell_qty']}")
    print(f"成本价: {pos['cost_price']:.2f}")
    print(f"市值: {pos['market_val']:,.2f}")
    print(f"盈亏: {pos['unrealized_pl']:+,.2f} ({pos['unrealized_pl_ratio']:+.2f}%)")

trd_ctx.close()
```

#### 持仓监控和报警

```python
import futu as ft
import time
from datetime import datetime

class PositionMonitor:
    def __init__(self, trd_ctx):
        self.trd_ctx = trd_ctx
        self.alert_thresholds = {
            'profit_alert': 10.0,    # 盈利10%报警
            'loss_alert': -15.0,     # 亏损15%报警
            'large_position': 50000   # 单只股票市值超过5万报警
        }
    
    def check_position_alerts(self, trd_env=ft.TrdEnv.SIMULATE):
        """检查持仓报警"""
        ret, positions = self.trd_ctx.position_list_query(trd_env=trd_env)
        
        if ret != ft.RET_OK or positions.empty:
            return []
        
        alerts = []
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        for _, pos in positions.iterrows():
            # 盈利报警
            if pos['unrealized_pl_ratio'] >= self.alert_thresholds['profit_alert']:
                alerts.append({
                    'type': 'PROFIT_ALERT',
                    'code': pos['code'],
                    'name': pos['stock_name'],
                    'message': f"盈利达到 {pos['unrealized_pl_ratio']:.2f}%",
                    'time': current_time
                })
            
            # 亏损报警
            if pos['unrealized_pl_ratio'] <= self.alert_thresholds['loss_alert']:
                alerts.append({
                    'type': 'LOSS_ALERT',
                    'code': pos['code'],
                    'name': pos['stock_name'],
                    'message': f"亏损达到 {pos['unrealized_pl_ratio']:.2f}%",
                    'time': current_time
                })
            
            # 大额持仓报警
            if pos['market_val'] >= self.alert_thresholds['large_position']:
                alerts.append({
                    'type': 'LARGE_POSITION',
                    'code': pos['code'],
                    'name': pos['stock_name'],
                    'message': f"持仓市值 {pos['market_val']:,.0f}",
                    'time': current_time
                })
        
        return alerts
    
    def monitor_positions(self, duration_minutes=60, check_interval=5):
        """监控持仓指定时长"""
        print(f"开始监控持仓 {duration_minutes} 分钟...")
        
        start_time = time.time()
        end_time = start_time + duration_minutes * 60
        
        while time.time() < end_time:
            alerts = self.check_position_alerts()
            
            if alerts:
                print(f"\n{datetime.now().strftime('%H:%M:%S')} - 持仓报警:")
                for alert in alerts:
                    print(f"  [{alert['type']}] {alert['name']} ({alert['code']}): {alert['message']}")
            
            time.sleep(check_interval * 60)  # 转换为秒
        
        print("持仓监控结束")

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

monitor = PositionMonitor(trd_ctx)

# 单次检查
alerts = monitor.check_position_alerts()
if alerts:
    print("当前持仓报警:")
    for alert in alerts:
        print(f"  {alert['name']}: {alert['message']}")
else:
    print("持仓正常，无报警")

# 持续监控（可选）
# monitor.monitor_positions(duration_minutes=30, check_interval=2)

trd_ctx.close()
```

### 持仓数据说明

#### position_side（持仓方向）

| 值 | 说明 |
|----|------|
| LONG | 多头持仓（普通持仓） |
| SHORT | 空头持仓（卖空） |

#### 重要字段说明

- **qty**: 总持仓数量
- **can_sell_qty**: 可卖数量（可能小于总持仓，如T+1限制）
- **cost_price**: 平均成本价格
- **market_val**: 当前市值（数量 × 现价）
- **unrealized_pl**: 未实现盈亏（市值 - 成本）
- **realized_pl**: 已实现盈亏（已卖出部分的盈亏）

### 注意事项

1. **数据时效性**：持仓数据可能有延迟，使用`refresh_cache=True`获取最新数据
2. **可卖数量**：注意区分持仓数量和可卖数量
3. **成本价有效性**：检查`cost_price_valid`字段确认成本价是否可信
4. **汇率影响**：跨币种持仓的盈亏可能受汇率影响
5. **分红除权**：分红除权可能影响成本价和盈亏计算

### 错误处理

```python
import futu as ft

def safe_get_positions(trd_ctx, **query_params):
    """安全获取持仓，包含错误处理"""
    try:
        ret, data = trd_ctx.position_list_query(**query_params)
        
        if ret == ft.RET_OK:
            return True, data
        else:
            # 处理常见错误
            error_msg = str(data)
            if "unlock" in error_msg.lower():
                return False, "需要先解锁交易"
            elif "account" in error_msg.lower():
                return False, "账户ID无效或无权限"
            else:
                return False, f"查询失败: {error_msg}"
                
    except Exception as e:
        return False, f"查询异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, result = safe_get_positions(
    trd_ctx,
    trd_env=ft.TrdEnv.SIMULATE,
    refresh_cache=True
)

if success:
    print(f"查询成功，共 {len(result)} 只持仓")
    if not result.empty:
        print(result[['code', 'stock_name', 'qty', 'unrealized_pl']])
else:
    print("查询失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [查询账户资金](accinfo-query.md) - 账户资金查询
- [查询订单](order-list-query.md) - 订单查询
- [下单](place-order.md) - 买卖股票