# 修改订单

修改已提交但未成交的订单，可以调整价格、数量等参数。

## modify_order - 修改订单

### 接口原型

```python
modify_order(modify_order_op, order_id, qty=0, price=0.0, adjust_limit=0, trd_env=TrdEnv.REAL, acc_id=0)
```

### 功能说明

修改已下单但未成交的订单，支持修改价格、数量、限价幅度等。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| modify_order_op | ModifyOrderOp | 是 | 修改操作类型 |
| order_id | str | 是 | 订单号，来自下单接口返回 |
| qty | int | 否 | 新的订单数量，0表示不修改 |
| price | float | 否 | 新的订单价格，0表示不修改 |
| adjust_limit | int | 否 | 调价次数限制，0表示不限制 |
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 修改结果DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| trd_env | str | 交易环境 |
| order_id | str | 订单号 |
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| qty | int | 修改后的数量 |
| price | float | 修改后的价格 |
| order_type | str | 订单类型 |
| order_status | str | 订单状态 |
| trd_side | str | 交易方向 |

### 代码示例

#### 基本修改订单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 先下单
    ret, order_data = trd_ctx.place_order(
        price=100.0,
        qty=100,
        code="HK.00700",
        trd_side=ft.TrdSide.BUY,
        trd_env=ft.TrdEnv.SIMULATE
    )
    
    if ret == ft.RET_OK:
        order_id = order_data['order_id'].iloc[0]
        print(f'下单成功，订单ID: {order_id}')
        
        # 修改价格
        ret, modify_data = trd_ctx.modify_order(
            modify_order_op=ft.ModifyOrderOp.NORMAL,
            order_id=order_id,
            price=99.0,  # 调整价格到99元
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        if ret == ft.RET_OK:
            print('订单修改成功:')
            print(f"新价格: {modify_data['price'].iloc[0]}")
            print(f"订单状态: {modify_data['order_status'].iloc[0]}")
        else:
            print('订单修改失败:', modify_data)
    else:
        print('下单失败:', order_data)
        
finally:
    trd_ctx.close()
```

#### 批量修改订单

```python
import futu as ft
import time

def batch_modify_orders(trd_ctx, modifications):
    """批量修改订单"""
    results = []
    
    for modify_info in modifications:
        ret, data = trd_ctx.modify_order(
            modify_order_op=modify_info.get('op', ft.ModifyOrderOp.NORMAL),
            order_id=modify_info['order_id'],
            price=modify_info.get('price', 0),
            qty=modify_info.get('qty', 0),
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        result = {
            'order_id': modify_info['order_id'],
            'success': ret == ft.RET_OK,
            'result': data if ret == ft.RET_OK else str(data)
        }
        results.append(result)
        
        if ret == ft.RET_OK:
            print(f"修改成功: 订单{modify_info['order_id']}")
        else:
            print(f"修改失败: 订单{modify_info['order_id']}, 错误: {data}")
        
        time.sleep(0.5)  # 避免频繁调用
    
    return results

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 假设有多个订单需要修改
modifications = [
    {'order_id': '123456789', 'price': 98.0},
    {'order_id': '123456790', 'qty': 200},
    {'order_id': '123456791', 'price': 102.0, 'qty': 150}
]

results = batch_modify_orders(trd_ctx, modifications)
print("\n批量修改结果:", results)

trd_ctx.close()
```

#### 智能调价策略

```python
import futu as ft
import time

class SmartOrderModifier:
    def __init__(self, trd_ctx, quote_ctx):
        self.trd_ctx = trd_ctx
        self.quote_ctx = quote_ctx
    
    def adjust_price_to_market(self, order_id, code, side, tolerance=0.02):
        """根据市场价格调整订单价格"""
        # 获取当前市价
        ret, quote_data = self.quote_ctx.get_stock_quote([code])
        if ret != ft.RET_OK:
            return False, "无法获取市价数据"
        
        current_price = quote_data['last_price'].iloc[0]
        
        # 根据买卖方向设定目标价格
        if side == 'BUY':
            # 买单：当前价下调一定比例
            target_price = current_price * (1 - tolerance)
        else:
            # 卖单：当前价上调一定比例
            target_price = current_price * (1 + tolerance)
        
        # 价格精度处理
        target_price = round(target_price, 2)
        
        # 修改订单
        ret, data = self.trd_ctx.modify_order(
            modify_order_op=ft.ModifyOrderOp.NORMAL,
            order_id=order_id,
            price=target_price,
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        if ret == ft.RET_OK:
            print(f"智能调价成功: {order_id}, 当前价{current_price}, 调整到{target_price}")
            return True, data
        else:
            return False, data
    
    def progressive_price_adjustment(self, order_id, code, side, steps=3, interval=10):
        """渐进式调价：逐步调整价格接近市价"""
        for step in range(steps):
            # 每次调整幅度递减
            tolerance = 0.05 - (step * 0.015)  # 5%, 3.5%, 2%
            
            success, result = self.adjust_price_to_market(order_id, code, side, tolerance)
            if success:
                print(f"第{step+1}次调价完成，等待{interval}秒...")
                time.sleep(interval)
            else:
                print(f"第{step+1}次调价失败: {result}")
                break

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 订阅报价
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
    trd_ctx.unlock_trade("123456")
    
    modifier = SmartOrderModifier(trd_ctx, quote_ctx)
    
    # 假设有一个买单需要智能调价
    order_id = "your_order_id_here"
    modifier.progressive_price_adjustment(order_id, 'HK.00700', 'BUY')
    
finally:
    quote_ctx.close()
    trd_ctx.close()
```

### 修改操作类型说明

#### ModifyOrderOp（修改操作）

| 常量 | 说明 |
|------|------|
| NORMAL | 普通修改 |
| CANCEL | 撤销订单 |
| DISABLE | 失效订单 |
| ENABLE | 启用订单 |
| DELETE | 删除订单 |

### 修改限制说明

#### 可修改的订单状态

| 订单状态 | 是否可修改 | 说明 |
|----------|------------|------|
| 等待提交 | 是 | 订单还未提交到交易所 |
| 已提交 | 是 | 订单已提交但未成交 |
| 部分成交 | 是 | 可修改未成交部分 |
| 全部成交 | 否 | 已完全成交无法修改 |
| 已撤销 | 否 | 已撤销无法修改 |

#### 修改约束

1. **价格限制**：修改后的价格需符合涨跌停限制
2. **数量约束**：新数量不能小于已成交数量
3. **时间限制**：交易时间内才能修改
4. **次数限制**：部分市场对修改次数有限制

### 注意事项

1. **订单状态**：只能修改未完全成交的订单
2. **价格合理性**：修改后的价格应在合理范围内
3. **成交优先**：部分成交的订单，只能修改未成交部分
4. **市场规则**：遵守各市场的修改规则
5. **风险控制**：频繁修改可能影响成交概率

### 错误处理

```python
import futu as ft

def safe_modify_order(trd_ctx, order_id, **modify_params):
    """安全修改订单，包含错误处理"""
    try:
        ret, data = trd_ctx.modify_order(
            modify_order_op=ft.ModifyOrderOp.NORMAL,
            order_id=order_id,
            **modify_params
        )
        
        if ret == ft.RET_OK:
            return True, data
        else:
            # 处理常见错误
            error_msg = str(data)
            if "已成交" in error_msg:
                return False, "订单已成交，无法修改"
            elif "已撤销" in error_msg:
                return False, "订单已撤销，无法修改"
            elif "价格" in error_msg:
                return False, "修改价格不合理"
            elif "非交易时间" in error_msg:
                return False, "当前非交易时间"
            else:
                return False, f"修改失败: {error_msg}"
                
    except Exception as e:
        return False, f"修改异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

success, result = safe_modify_order(
    trd_ctx,
    order_id="123456789",
    price=99.0,
    trd_env=ft.TrdEnv.SIMULATE
)

if success:
    print("修改成功:", result['order_id'].iloc[0])
else:
    print("修改失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [下单](place-order.md) - 提交订单
- [撤销订单](cancel-order.md) - 撤销订单
- [查询订单](order-list-query.md) - 查询订单状态