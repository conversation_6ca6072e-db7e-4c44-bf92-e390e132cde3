# 撤销订单

撤销已提交但未完全成交的订单，包括单个订单撤销和批量撤销功能。

## cancel_order - 撤销单个订单

### 接口原型

```python
cancel_order(order_id, trd_env=TrdEnv.REAL, acc_id=0)
```

### 功能说明

撤销指定的未完全成交订单。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | str | 是 | 订单号，来自下单接口返回 |
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 撤单结果DataFrame，失败时返回错误描述 |

## cancel_all_order - 撤销所有订单

### 接口原型

```python
cancel_all_order(trd_env=TrdEnv.REAL, acc_id=0)
```

### 功能说明

撤销指定账户下的所有未完全成交订单。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| trd_env | TrdEnv | 否 | 交易环境，默认REAL |
| acc_id | int | 否 | 账户ID，默认0 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| order_id | str | 订单号 |
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| trd_side | str | 交易方向 |
| order_type | str | 订单类型 |
| order_status | str | 订单状态 |
| qty | int | 原订单数量 |
| price | float | 原订单价格 |

### 代码示例

#### 撤销单个订单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print('交易解锁失败:', data)
        exit()
    
    # 先下单
    ret, order_data = trd_ctx.place_order(
        price=100.0,
        qty=100,
        code="HK.00700",
        trd_side=ft.TrdSide.BUY,
        trd_env=ft.TrdEnv.SIMULATE
    )
    
    if ret == ft.RET_OK:
        order_id = order_data['order_id'].iloc[0]
        print(f'下单成功，订单ID: {order_id}')
        
        # 撤销订单
        ret, cancel_data = trd_ctx.cancel_order(
            order_id=order_id,
            trd_env=ft.TrdEnv.SIMULATE
        )
        
        if ret == ft.RET_OK:
            print('撤单成功:')
            print(f"订单ID: {cancel_data['order_id'].iloc[0]}")
            print(f"订单状态: {cancel_data['order_status'].iloc[0]}")
        else:
            print('撤单失败:', cancel_data)
    else:
        print('下单失败:', order_data)
        
finally:
    trd_ctx.close()
```

#### 批量撤销订单

```python
import futu as ft
import time

def batch_cancel_orders(trd_ctx, order_ids, trd_env=ft.TrdEnv.SIMULATE):
    """批量撤销订单"""
    results = []
    
    for order_id in order_ids:
        ret, data = trd_ctx.cancel_order(
            order_id=order_id,
            trd_env=trd_env
        )
        
        result = {
            'order_id': order_id,
            'success': ret == ft.RET_OK,
            'result': data if ret == ft.RET_OK else str(data)
        }
        results.append(result)
        
        if ret == ft.RET_OK:
            print(f"撤单成功: {order_id}")
        else:
            print(f"撤单失败: {order_id}, 错误: {data}")
        
        time.sleep(0.2)  # 避免频繁调用
    
    return results

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 假设有多个订单需要撤销
order_ids = ['123456789', '123456790', '123456791']
results = batch_cancel_orders(trd_ctx, order_ids)

print("\n批量撤单结果:")
for result in results:
    status = "成功" if result['success'] else "失败"
    print(f"订单{result['order_id']}: {status}")

trd_ctx.close()
```

#### 撤销所有订单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    trd_ctx.unlock_trade("123456")
    
    # 获取当前未成交订单
    ret, order_list = trd_ctx.order_list_query(
        trd_env=ft.TrdEnv.SIMULATE,
        status_filter_list=[ft.OrderStatus.SUBMITTED, ft.OrderStatus.FILLED_PART]
    )
    
    if ret == ft.RET_OK and not order_list.empty:
        print(f"当前有 {len(order_list)} 个未成交订单")
        print(order_list[['order_id', 'code', 'order_status', 'qty', 'price']])
        
        # 撤销所有订单
        ret, cancel_data = trd_ctx.cancel_all_order(trd_env=ft.TrdEnv.SIMULATE)
        
        if ret == ft.RET_OK:
            print(f"\n撤销所有订单成功，共撤销 {len(cancel_data)} 个订单:")
            for _, row in cancel_data.iterrows():
                print(f"  订单{row['order_id']}: {row['code']} - {row['order_status']}")
        else:
            print('撤销所有订单失败:', cancel_data)
    else:
        print("没有需要撤销的订单")
        
finally:
    trd_ctx.close()
```

#### 智能撤单策略

```python
import futu as ft
import time
from datetime import datetime, timedelta

class SmartCancelManager:
    def __init__(self, trd_ctx, quote_ctx):
        self.trd_ctx = trd_ctx
        self.quote_ctx = quote_ctx
    
    def cancel_orders_by_price_deviation(self, deviation_percent=5.0):
        """撤销价格偏离市价过大的订单"""
        # 获取所有未成交订单
        ret, orders = self.trd_ctx.order_list_query(
            trd_env=ft.TrdEnv.SIMULATE,
            status_filter_list=[ft.OrderStatus.SUBMITTED, ft.OrderStatus.FILLED_PART]
        )
        
        if ret != ft.RET_OK or orders.empty:
            return []
        
        cancelled_orders = []
        
        # 获取各股票的当前价格
        codes = orders['code'].unique().tolist()
        ret, quotes = self.quote_ctx.get_stock_quote(codes)
        
        if ret != ft.RET_OK:
            print("无法获取报价数据")
            return cancelled_orders
        
        # 创建价格字典
        price_dict = {row['code']: row['last_price'] for _, row in quotes.iterrows()}
        
        for _, order in orders.iterrows():
            code = order['code']
            order_price = order['price']
            order_id = order['order_id']
            trd_side = order['trd_side']
            
            if code not in price_dict:
                continue
            
            market_price = price_dict[code]
            
            # 计算价格偏离度
            if trd_side == 'BUY':
                deviation = (order_price - market_price) / market_price * 100
                should_cancel = deviation > deviation_percent  # 买单价格过高
            else:
                deviation = (market_price - order_price) / market_price * 100
                should_cancel = deviation > deviation_percent  # 卖单价格过低
            
            if should_cancel:
                ret, cancel_result = self.trd_ctx.cancel_order(
                    order_id=order_id,
                    trd_env=ft.TrdEnv.SIMULATE
                )
                
                if ret == ft.RET_OK:
                    cancelled_orders.append({
                        'order_id': order_id,
                        'code': code,
                        'reason': f'价格偏离{deviation:.1f}%',
                        'order_price': order_price,
                        'market_price': market_price
                    })
                    print(f"撤销订单 {order_id}: {code} 价格偏离 {deviation:.1f}%")
        
        return cancelled_orders
    
    def cancel_old_orders(self, hours_threshold=2):
        """撤销超过指定时间的订单"""
        ret, orders = self.trd_ctx.order_list_query(
            trd_env=ft.TrdEnv.SIMULATE,
            status_filter_list=[ft.OrderStatus.SUBMITTED, ft.OrderStatus.FILLED_PART]
        )
        
        if ret != ft.RET_OK or orders.empty:
            return []
        
        current_time = datetime.now()
        threshold_time = current_time - timedelta(hours=hours_threshold)
        cancelled_orders = []
        
        for _, order in orders.iterrows():
            # 解析订单时间（假设格式为 'YYYY-MM-DD HH:MM:SS'）
            order_time_str = order['create_time']
            try:
                order_time = datetime.strptime(order_time_str, '%Y-%m-%d %H:%M:%S')
                
                if order_time < threshold_time:
                    ret, cancel_result = self.trd_ctx.cancel_order(
                        order_id=order['order_id'],
                        trd_env=ft.TrdEnv.SIMULATE
                    )
                    
                    if ret == ft.RET_OK:
                        age_hours = (current_time - order_time).total_seconds() / 3600
                        cancelled_orders.append({
                            'order_id': order['order_id'],
                            'code': order['code'],
                            'reason': f'订单存在{age_hours:.1f}小时',
                            'create_time': order_time_str
                        })
                        print(f"撤销过期订单 {order['order_id']}: 存在 {age_hours:.1f} 小时")
            except ValueError:
                print(f"无法解析订单时间: {order_time_str}")
        
        return cancelled_orders

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 订阅相关股票报价
    quote_ctx.subscribe(['HK.00700', 'HK.00001'], [ft.SubType.QUOTE])
    trd_ctx.unlock_trade("123456")
    
    cancel_mgr = SmartCancelManager(trd_ctx, quote_ctx)
    
    # 撤销价格偏离过大的订单
    print("检查价格偏离订单...")
    price_cancelled = cancel_mgr.cancel_orders_by_price_deviation(5.0)
    print(f"因价格偏离撤销 {len(price_cancelled)} 个订单")
    
    # 撤销过期订单
    print("\n检查过期订单...")
    time_cancelled = cancel_mgr.cancel_old_orders(2)
    print(f"因时间过长撤销 {len(time_cancelled)} 个订单")
    
finally:
    quote_ctx.close()
    trd_ctx.close()
```

### 撤单限制说明

#### 可撤销的订单状态

| 订单状态 | 是否可撤销 | 说明 |
|----------|------------|------|
| 等待提交 | 是 | 订单还未提交到交易所 |
| 已提交 | 是 | 订单已提交但未成交 |
| 部分成交 | 是 | 可撤销未成交部分 |
| 全部成交 | 否 | 已完全成交无法撤销 |
| 已撤销 | 否 | 已撤销无需再次撤销 |

#### 撤单约束

1. **时间限制**：交易时间内才能撤销
2. **状态限制**：只能撤销未完全成交的订单
3. **市场规则**：遵守各市场的撤单规则
4. **部分成交**：部分成交的订单，只撤销未成交部分

### 注意事项

1. **订单状态**：撤单前检查订单当前状态
2. **成交风险**：撤单期间订单可能成交
3. **时效性**：撤单指令有时间延迟
4. **批量操作**：批量撤单时注意调用频率
5. **错误处理**：对撤单失败进行适当处理

### 错误处理

```python
import futu as ft

def safe_cancel_order(trd_ctx, order_id, trd_env=ft.TrdEnv.SIMULATE):
    """安全撤单，包含错误处理"""
    try:
        ret, data = trd_ctx.cancel_order(order_id=order_id, trd_env=trd_env)
        
        if ret == ft.RET_OK:
            return True, data
        else:
            # 处理常见错误
            error_msg = str(data)
            if "已成交" in error_msg:
                return False, "订单已成交，无法撤销"
            elif "已撤销" in error_msg:
                return False, "订单已撤销"
            elif "不存在" in error_msg:
                return False, "订单不存在"
            elif "非交易时间" in error_msg:
                return False, "当前非交易时间"
            else:
                return False, f"撤单失败: {error_msg}"
                
    except Exception as e:
        return False, f"撤单异常: {str(e)}"

def safe_cancel_all_orders(trd_ctx, trd_env=ft.TrdEnv.SIMULATE):
    """安全撤销所有订单"""
    try:
        ret, data = trd_ctx.cancel_all_order(trd_env=trd_env)
        
        if ret == ft.RET_OK:
            return True, data
        else:
            error_msg = str(data)
            if "没有可撤销的订单" in error_msg:
                return True, "没有需要撤销的订单"
            else:
                return False, f"撤销所有订单失败: {error_msg}"
                
    except Exception as e:
        return False, f"撤销异常: {str(e)}"

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
trd_ctx.unlock_trade("123456")

# 撤销单个订单
success, result = safe_cancel_order(trd_ctx, "123456789")
if success:
    print("撤单成功")
else:
    print("撤单失败:", result)

# 撤销所有订单
success, result = safe_cancel_all_orders(trd_ctx)
if success:
    print("撤销所有订单成功")
else:
    print("撤销失败:", result)

trd_ctx.close()
```

### 相关接口

- [交易对象](base.md) - 交易上下文基本用法
- [下单](place-order.md) - 提交订单
- [修改订单](modify-order.md) - 修改订单
- [查询订单](order-list-query.md) - 查询订单状态