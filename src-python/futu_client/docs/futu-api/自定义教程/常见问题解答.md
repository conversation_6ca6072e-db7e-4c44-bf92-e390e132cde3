# 富途 OpenAPI 常见问题解答

本文档收集了富途 OpenAPI 使用过程中的常见问题和解决方案，帮助您快速解决遇到的问题。

## 安装和环境问题

### Q1: 如何安装富途 API？

**A:** 使用 uv 安装最新版本：

```bash
# 安装富途API
uv add futu

# 升级到最新版本
uv add futu --upgrade

# 安装特定版本
uv add futu==5.8.0

# 验证安装
uv run python -c "import futu; print('版本:', futu.__version__)"
```

### Q2: Python 版本要求是什么？

**A:**

-   **最低要求**: Python 3.6
-   **推荐版本**: Python 3.8 或更高
-   **不支持**: Python 2.x

### Q3: 在虚拟环境中如何安装？

**A:**

```bash
# 使用 uv 创建项目环境
uv init futu_project
cd futu_project

# 安装富途API
uv add futu
```

### Q4: 安装失败怎么办？

**A:** 常见解决方案：

```bash
# 1. 更新 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 2. 清理缓存
uv cache clean

# 3. 使用国内镜像源
uv add futu --index-url https://pypi.tuna.tsinghua.edu.cn/simple/

# 4. 如果网络问题，可以配置镜像
uv add futu --extra-index-url https://pypi.tuna.tsinghua.edu.cn/simple/
```

## OpenD 相关问题

### Q5: OpenD 无法启动怎么办？

**A:** 按以下步骤排查：

1. **检查权限**：

    - Windows: 右键"以管理员身份运行"
    - macOS: 在安全设置中允许运行

2. **检查端口占用**：

```bash
# Windows
netstat -ano | findstr :11111

# macOS/Linux
lsof -i :11111
```

3. **检查防火墙设置**：

    - 确保端口 11111 未被防火墙阻止
    - 添加 OpenD 到防火墙例外

4. **查看日志文件**：
    - 检查 OpenD 的日志文件获取详细错误信息

### Q6: OpenD 连接失败？

**A:**

```python
import futu as ft
import time

def test_connection_with_retry(max_retries=5):
    """测试连接并重试"""
    for i in range(max_retries):
        try:
            quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
            ret, data = quote_ctx.get_global_state()
            quote_ctx.close()

            if ret == ft.RET_OK:
                print(f"✅ 第{i+1}次尝试连接成功")
                return True
            else:
                print(f"❌ 第{i+1}次尝试失败: {data}")

        except Exception as e:
            print(f"❌ 第{i+1}次尝试异常: {e}")

        if i < max_retries - 1:
            print(f"等待5秒后重试...")
            time.sleep(5)

    return False

# 使用示例
if test_connection_with_retry():
    print("连接成功，可以继续使用API")
else:
    print("连接失败，请检查OpenD是否正常启动")
```

### Q7: 如何配置 OpenD？

**A:** OpenD 配置文件通常在以下位置：

-   Windows: `%APPDATA%/Futu/FutuOpenD/`
-   macOS: `~/Library/Application Support/Futu/FutuOpenD/`
-   Linux: `~/.futu/FutuOpenD/`

常用配置项：

```json
{
    "ip": "127.0.0.1",
    "port": 11111,
    "enable_ssl": false,
    "login_region": "HK",
    "log_level": "info"
}
```

## 账户和权限问题

### Q8: 如何开通 OpenAPI 权限？

**A:**

1. **登录富途牛牛 App**
2. **进入"我的" → "设置" → "开发者"**
3. **申请 OpenAPI 权限**
4. **完成实名认证和风险测评**
5. **等待审核通过**

### Q9: 交易解锁失败？

**A:** 常见原因和解决方案：

```python
def troubleshoot_unlock_trade():
    """交易解锁问题诊断"""

    print("🔍 开始诊断交易解锁问题...")

    # 1. 检查连接
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    try:
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            print("✅ OpenD连接正常")
        else:
            print("❌ OpenD连接失败，请先解决连接问题")
            return
    finally:
        quote_ctx.close()

    # 2. 检查账户列表
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    try:
        ret, acc_data = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("✅ 可以获取账户列表")
            print(f"找到 {len(acc_data)} 个账户")

            for idx, acc in acc_data.iterrows():
                print(f"  账户{idx+1}: {acc['acc_id']} ({acc['trd_env']})")
        else:
            print("❌ 无法获取账户列表:", acc_data)
            return

        # 3. 尝试解锁
        password = input("请输入交易密码: ")
        ret, unlock_data = trd_ctx.unlock_trade(password)

        if ret == ft.RET_OK:
            print("✅ 交易解锁成功")
        else:
            print(f"❌ 交易解锁失败: {unlock_data}")
            print("\n可能的原因:")
            print("1. 交易密码错误")
            print("2. 账户未开通OpenAPI交易权限")
            print("3. 需要在富途App中启用API交易")
            print("4. 账户被冻结或限制")

    finally:
        trd_ctx.close()

# 运行诊断
troubleshoot_unlock_trade()
```

### Q10: 模拟账户和真实账户有什么区别？

**A:**

| 功能     | 模拟账户    | 真实账户    |
| -------- | ----------- | ----------- |
| 下单交易 | ✅ 虚拟交易 | ✅ 真实交易 |
| 资金     | 虚拟资金    | 真实资金    |
| 行情数据 | 相同        | 相同        |
| 风险     | 无风险      | 有风险      |
| 手续费   | 不扣除      | 正常扣除    |
| 交割     | 无实际交割  | 真实交割    |

## 行情数据问题

### Q11: 为什么获取不到实时行情？

**A:** 可能的原因：

1. **数据权限问题**：

```python
def check_market_data_permission():
    """检查行情权限"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 检查用户信息
        ret, user_info = quote_ctx.get_user_info()
        if ret == ft.RET_OK:
            print("👤 用户信息:")
            print(f"昵称: {user_info['nickname']}")
            print(f"用户ID: {user_info['user_id']}")

        # 测试订阅权限
        test_codes = ['HK.00700', 'US.AAPL', 'SZ.000001']

        for code in test_codes:
            ret, err = quote_ctx.subscribe([code], [ft.SubType.QUOTE])
            if ret == ft.RET_OK:
                print(f"✅ {code} 订阅成功")

                # 获取报价
                ret, data = quote_ctx.get_stock_quote([code])
                if ret == ft.RET_OK:
                    quote = data.iloc[0]
                    print(f"   最新价: {quote['last_price']}")
                    print(f"   更新时间: {quote['data_date']} {quote['data_time']}")
                else:
                    print(f"   获取报价失败: {data}")
            else:
                print(f"❌ {code} 订阅失败: {err}")

    finally:
        quote_ctx.close()

check_market_data_permission()
```

2. **数据延迟**：

    - 免费用户行情有 15 分钟延迟
    - 需要购买实时行情权限

3. **市场状态**：

```python
def check_market_status():
    """检查市场状态"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 检查全球市场状态
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            print("🌍 全球市场状态:")
            print(f"服务器时间: {data}")

        # 检查具体市场状态
        markets = [ft.Market.HK, ft.Market.US, ft.Market.SH, ft.Market.SZ]
        market_names = ['港股', '美股', '沪股', '深股']

        for market, name in zip(markets, market_names):
            ret, market_data = quote_ctx.get_market_state([market])
            if ret == ft.RET_OK:
                if not market_data.empty:
                    state = market_data.iloc[0]
                    print(f"{name}: {state['market_state']}")

    finally:
        quote_ctx.close()

check_market_status()
```

### Q12: 订阅数量有限制吗？

**A:** 是的，有以下限制：

-   **免费用户**: 最多订阅 5 只股票
-   **Level1 用户**: 最多订阅 200 只股票
-   **Level2 用户**: 最多订阅 500 只股票

```python
def check_subscription_limit():
    """检查订阅限制"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 获取当前订阅列表
        ret, sub_data = quote_ctx.get_subscription_list()
        if ret == ft.RET_OK:
            print(f"📊 当前订阅数量: {len(sub_data)}")

            if not sub_data.empty:
                print("订阅列表:")
                for idx, row in sub_data.iterrows():
                    print(f"  {row['code']} - {row['sub_type_list']}")

        # 获取用户限制信息
        ret, limit_data = quote_ctx.get_user_info()
        if ret == ft.RET_OK:
            print("💡 建议根据您的账户等级合理安排订阅数量")

    finally:
        quote_ctx.close()

check_subscription_limit()
```

## 交易相关问题

### Q13: 下单失败的常见原因？

**A:**

```python
def analyze_order_failure(error_msg):
    """分析下单失败原因"""

    error_solutions = {
        "insufficient fund": {
            "原因": "资金不足",
            "解决方案": [
                "检查账户可用资金",
                "减少下单数量",
                "充值资金到账户"
            ]
        },
        "market closed": {
            "原因": "市场未开市",
            "解决方案": [
                "检查交易时间",
                "等待市场开盘",
                "确认节假日安排"
            ]
        },
        "invalid price": {
            "原因": "价格无效",
            "解决方案": [
                "检查价格是否在涨跌停范围内",
                "确认价格精度",
                "使用市价单"
            ]
        },
        "position limit": {
            "原因": "持仓限制",
            "解决方案": [
                "检查单只股票持仓限制",
                "减少下单数量",
                "先平仓部分持仓"
            ]
        }
    }

    print(f"❌ 下单失败: {error_msg}")

    for key, info in error_solutions.items():
        if key.lower() in error_msg.lower():
            print(f"\n💡 可能原因: {info['原因']}")
            print("解决方案:")
            for solution in info['解决方案']:
                print(f"  • {solution}")
            break
    else:
        print("\n💡 建议:")
        print("  • 检查账户状态和权限")
        print("  • 确认股票代码正确")
        print("  • 查看详细错误信息")
        print("  • 联系客服获取帮助")

# 使用示例
analyze_order_failure("insufficient fund")
```

### Q14: 如何查看订单状态？

**A:**

```python
def check_order_status():
    """查看订单状态"""
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

    try:
        # 解锁交易
        ret, data = trd_ctx.unlock_trade("您的交易密码")
        if ret != ft.RET_OK:
            print("解锁失败:", data)
            return

        # 获取账户列表
        ret, acc_data = trd_ctx.get_acc_list()
        if ret == ft.RET_OK and not acc_data.empty:
            acc_id = acc_data.iloc[0]['acc_id']
            trd_env = ft.TrdEnv.SIMULATE if acc_data.iloc[0]['trd_env'] == 'SIMULATE' else ft.TrdEnv.REAL

            # 查询今日订单
            ret, order_data = trd_ctx.order_list_query(
                trd_env=trd_env,
                acc_id=acc_id
            )

            if ret == ft.RET_OK:
                print("📋 今日订单列表:")
                if order_data.empty:
                    print("  暂无订单")
                else:
                    for idx, order in order_data.iterrows():
                        print(f"  订单{idx+1}:")
                        print(f"    订单号: {order['order_id']}")
                        print(f"    股票: {order['code']}")
                        print(f"    方向: {'买入' if order['trd_side'] == 'BUY' else '卖出'}")
                        print(f"    数量: {order['qty']}")
                        print(f"    价格: {order['price']}")
                        print(f"    状态: {order['order_status']}")
                        print(f"    时间: {order['create_time']}")
                        print("    ---")
            else:
                print("查询订单失败:", order_data)

    finally:
        trd_ctx.close()

check_order_status()
```

## 数据分析问题

### Q15: 如何获取历史 K 线数据？

**A:**

```python
import futu as ft
import pandas as pd
from datetime import datetime, timedelta

def get_historical_kline(code, days=30, ktype=ft.KLType.K_DAY):
    """获取历史K线数据"""
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        # 计算开始和结束日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # 获取历史K线
        ret, data = quote_ctx.get_history_kline(
            code=code,
            start=start_date.strftime('%Y-%m-%d'),
            end=end_date.strftime('%Y-%m-%d'),
            ktype=ktype,
            fields=[ft.KL_FIELD.ALL]
        )

        if ret == ft.RET_OK:
            print(f"📈 获取 {code} 历史K线成功，共 {len(data)} 条数据")

            # 显示基本统计信息
            print(f"时间范围: {data['time_key'].min()} 到 {data['time_key'].max()}")
            print(f"最高价: {data['high'].max():.2f}")
            print(f"最低价: {data['low'].min():.2f}")
            print(f"平均成交量: {data['volume'].mean():,.0f}")

            return data
        else:
            print(f"获取K线失败: {data}")
            return None

    finally:
        quote_ctx.close()

# 使用示例
df = get_historical_kline('HK.00700', days=60)
if df is not None:
    # 可以进行进一步分析
    df['ma5'] = df['close'].rolling(5).mean()
    df['ma20'] = df['close'].rolling(20).mean()
    print(df.tail())
```

### Q16: 如何计算技术指标？

**A:**

```python
import numpy as np
import pandas as pd

def calculate_technical_indicators(df):
    """计算常用技术指标"""

    # 移动平均线
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA10'] = df['close'].rolling(window=10).mean()
    df['MA20'] = df['close'].rolling(window=20).mean()

    # RSI指标
    def calculate_rsi(prices, window=14):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    df['RSI'] = calculate_rsi(df['close'])

    # MACD指标
    def calculate_macd(prices, fast=12, slow=26, signal=9):
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        signal_line = macd.ewm(span=signal).mean()
        histogram = macd - signal_line
        return macd, signal_line, histogram

    df['MACD'], df['MACD_Signal'], df['MACD_Hist'] = calculate_macd(df['close'])

    # 布林带
    def calculate_bollinger_bands(prices, window=20, std_dev=2):
        ma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = ma + (std * std_dev)
        lower_band = ma - (std * std_dev)
        return upper_band, ma, lower_band

    df['BB_Upper'], df['BB_Middle'], df['BB_Lower'] = calculate_bollinger_bands(df['close'])

    print("✅ 技术指标计算完成")
    print("可用指标: MA5, MA10, MA20, RSI, MACD, MACD_Signal, MACD_Hist, BB_Upper, BB_Middle, BB_Lower")

    return df

# 使用示例
# 假设已经有了K线数据df
# df = calculate_technical_indicators(df)
# print(df[['close', 'MA5', 'MA20', 'RSI']].tail())
```

## 性能优化问题

### Q17: 如何提高 API 调用效率？

**A:**

```python
import futu as ft
import time
from concurrent.futures import ThreadPoolExecutor
import threading

class EfficientFutuAPI:
    def __init__(self):
        self.quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
        self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
        self.lock = threading.Lock()

    def batch_get_quotes(self, code_list, batch_size=50):
        """批量获取报价，提高效率"""
        all_data = []

        # 分批处理
        for i in range(0, len(code_list), batch_size):
            batch = code_list[i:i + batch_size]

            try:
                ret, data = self.quote_ctx.get_stock_quote(batch)
                if ret == ft.RET_OK:
                    all_data.append(data)
                    print(f"✅ 批次 {i//batch_size + 1}: 获取 {len(batch)} 只股票报价")
                else:
                    print(f"❌ 批次 {i//batch_size + 1} 失败: {data}")

                # 避免请求过快
                time.sleep(0.1)

            except Exception as e:
                print(f"❌ 批次 {i//batch_size + 1} 异常: {e}")

        # 合并所有数据
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return pd.DataFrame()

    def parallel_get_klines(self, code_list, days=30):
        """并行获取多只股票的K线数据"""

        def get_single_kline(code):
            try:
                with self.lock:  # 防止并发访问冲突
                    ret, data = self.quote_ctx.get_cur_kline(
                        code=code,
                        num=days,
                        ktype=ft.KLType.K_DAY
                    )

                if ret == ft.RET_OK:
                    return code, data
                else:
                    return code, None
            except Exception as e:
                print(f"获取 {code} K线失败: {e}")
                return code, None

        # 使用线程池并行处理
        results = {}
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(get_single_kline, code): code for code in code_list}

            for future in futures:
                code, data = future.result()
                results[code] = data
                if data is not None:
                    print(f"✅ {code} K线数据获取成功")
                else:
                    print(f"❌ {code} K线数据获取失败")

        return results

    def close(self):
        """关闭连接"""
        self.quote_ctx.close()
        self.trd_ctx.close()

# 使用示例
api = EfficientFutuAPI()

try:
    # 批量获取报价
    codes = ['HK.00700', 'HK.00005', 'HK.00939', 'HK.01299', 'HK.00388']
    quotes = api.batch_get_quotes(codes)
    print(f"获取到 {len(quotes)} 条报价数据")

    # 并行获取K线
    klines = api.parallel_get_klines(codes[:3])  # 限制数量避免过多请求
    print(f"获取到 {len([k for k in klines.values() if k is not None])} 只股票的K线数据")

finally:
    api.close()
```

### Q18: 如何处理 API 限制？

**A:**

```python
import time
import functools
from datetime import datetime, timedelta

class RateLimiter:
    def __init__(self, max_calls, time_window):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []

    def __call__(self, func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            now = datetime.now()

            # 清理过期的调用记录
            self.calls = [call_time for call_time in self.calls
                         if now - call_time < timedelta(seconds=self.time_window)]

            # 检查是否超过限制
            if len(self.calls) >= self.max_calls:
                sleep_time = self.time_window - (now - self.calls[0]).total_seconds()
                if sleep_time > 0:
                    print(f"⏳ API调用频率限制，等待 {sleep_time:.1f} 秒...")
                    time.sleep(sleep_time)

            # 记录此次调用
            self.calls.append(now)

            # 执行函数
            return func(*args, **kwargs)

        return wrapper

# 使用装饰器限制API调用频率
@RateLimiter(max_calls=10, time_window=60)  # 每分钟最多10次调用
def get_stock_quote_with_limit(quote_ctx, code):
    ret, data = quote_ctx.get_stock_quote([code])
    return ret, data

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
try:
    codes = ['HK.00700', 'HK.00005', 'HK.00939']

    for code in codes:
        ret, data = get_stock_quote_with_limit(quote_ctx, code)
        if ret == ft.RET_OK:
            print(f"{code}: {data.iloc[0]['last_price']}")
        time.sleep(1)  # 额外的延迟

finally:
    quote_ctx.close()
```

## 错误处理和调试

### Q19: 如何进行错误处理？

**A:**

```python
import futu as ft
import logging
from functools import wraps

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('futu_api.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def safe_api_call(func):
    """API调用安全装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            ret, data = func(*args, **kwargs)

            if ret == ft.RET_OK:
                logger.info(f"✅ {func.__name__} 调用成功")
                return ret, data
            else:
                logger.error(f"❌ {func.__name__} 调用失败: {data}")
                return ret, data

        except Exception as e:
            logger.error(f"💥 {func.__name__} 调用异常: {str(e)}", exc_info=True)
            return ft.RET_ERROR, str(e)

    return wrapper

class SafeFutuAPI:
    def __init__(self):
        self.quote_ctx = None
        self.trd_ctx = None
        self.connected = False

    def connect(self, max_retries=3):
        """安全连接"""
        for i in range(max_retries):
            try:
                self.quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
                self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

                # 测试连接
                ret, data = self.quote_ctx.get_global_state()
                if ret == ft.RET_OK:
                    self.connected = True
                    logger.info("✅ 连接成功")
                    return True
                else:
                    logger.warning(f"连接测试失败: {data}")

            except Exception as e:
                logger.error(f"连接异常 (尝试 {i+1}/{max_retries}): {e}")
                time.sleep(2)

        logger.error("❌ 连接失败")
        return False

    @safe_api_call
    def get_stock_quote_safe(self, code_list):
        """安全获取股票报价"""
        if not self.connected:
            raise Exception("未连接到OpenD")

        return self.quote_ctx.get_stock_quote(code_list)

    @safe_api_call
    def place_order_safe(self, **order_params):
        """安全下单"""
        if not self.connected:
            raise Exception("未连接到OpenD")

        # 验证必要参数
        required_params = ['price', 'qty', 'code', 'trd_side', 'trd_env', 'acc_id']
        for param in required_params:
            if param not in order_params:
                raise ValueError(f"缺少必要参数: {param}")

        return self.trd_ctx.place_order(**order_params)

    def close(self):
        """安全关闭连接"""
        try:
            if self.quote_ctx:
                self.quote_ctx.close()
            if self.trd_ctx:
                self.trd_ctx.close()
            self.connected = False
            logger.info("✅ 连接已关闭")
        except Exception as e:
            logger.error(f"关闭连接时出错: {e}")

# 使用示例
api = SafeFutuAPI()

try:
    if api.connect():
        # 安全获取报价
        ret, data = api.get_stock_quote_safe(['HK.00700'])
        if ret == ft.RET_OK:
            print(f"腾讯股价: {data.iloc[0]['last_price']}")

finally:
    api.close()
```

### Q20: 如何调试 API 问题？

**A:**

```python
def debug_futu_api():
    """调试富途API问题"""

    print("🔍 开始调试富途API...")

    # 1. 检查环境
    print("\n1️⃣ 检查Python环境:")
    import sys
    print(f"Python版本: {sys.version}")

    try:
        import futu
        print(f"富途API版本: {futu.__version__}")
    except ImportError as e:
        print(f"❌ 富途API导入失败: {e}")
        return

    # 2. 检查网络连接
    print("\n2️⃣ 检查网络连接:")
    import socket
    try:
        sock = socket.create_connection(('127.0.0.1', 11111), timeout=5)
        sock.close()
        print("✅ 端口11111可访问")
    except Exception as e:
        print(f"❌ 端口11111不可访问: {e}")
        print("请检查OpenD是否启动")
        return

    # 3. 测试基本连接
    print("\n3️⃣ 测试基本连接:")
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    try:
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            print("✅ 基本连接正常")
            print(f"服务器时间: {data}")
        else:
            print(f"❌ 基本连接失败: {data}")
    except Exception as e:
        print(f"❌ 连接异常: {e}")
    finally:
        quote_ctx.close()

    # 4. 测试行情功能
    print("\n4️⃣ 测试行情功能:")
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    try:
        # 测试订阅
        ret, err = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
        if ret == ft.RET_OK:
            print("✅ 订阅功能正常")

            # 测试获取报价
            ret, data = quote_ctx.get_stock_quote(['HK.00700'])
            if ret == ft.RET_OK:
                print("✅ 获取报价正常")
                quote = data.iloc[0]
                print(f"腾讯最新价: {quote['last_price']}")
            else:
                print(f"❌ 获取报价失败: {data}")
        else:
            print(f"❌ 订阅失败: {err}")
    except Exception as e:
        print(f"❌ 行情功能异常: {e}")
    finally:
        quote_ctx.close()

    # 5. 测试交易功能
    print("\n5️⃣ 测试交易功能:")
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    try:
        # 测试获取账户列表
        ret, data = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("✅ 获取账户列表正常")
            print(f"找到 {len(data)} 个账户")
        else:
            print(f"❌ 获取账户列表失败: {data}")
    except Exception as e:
        print(f"❌ 交易功能异常: {e}")
    finally:
        trd_ctx.close()

    print("\n🎉 调试完成！")

# 运行调试
debug_futu_api()
```

## 其他常见问题

### Q21: 如何获取技术支持？

**A:**

1. **官方文档**: https://openapi.futunn.com/
2. **客服邮箱**: <EMAIL>
3. **QQ 群**: 富途开放 API 交流群
4. **微信群**: 联系客服加入
5. **GitHub**: https://github.com/FutunnOpen/py-futu-api

### Q22: 如何保持 API 连接稳定？

**A:**

```python
import futu as ft
import time
import threading
from datetime import datetime

class StableConnection:
    def __init__(self):
        self.quote_ctx = None
        self.trd_ctx = None
        self.connected = False
        self.running = True
        self.heartbeat_thread = None

    def connect(self):
        """建立连接"""
        try:
            self.quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
            self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

            # 测试连接
            ret, data = self.quote_ctx.get_global_state()
            if ret == ft.RET_OK:
                self.connected = True
                print("✅ 连接建立成功")

                # 启动心跳检测
                self.start_heartbeat()
                return True
            else:
                print(f"❌ 连接失败: {data}")
                return False

        except Exception as e:
            print(f"❌ 连接异常: {e}")
            return False

    def start_heartbeat(self):
        """启动心跳检测"""
        def heartbeat():
            while self.running and self.connected:
                try:
                    ret, data = self.quote_ctx.get_global_state()
                    if ret != ft.RET_OK:
                        print("💔 心跳检测失败，尝试重连...")
                        self.reconnect()
                    else:
                        print(f"💓 心跳正常 - {datetime.now().strftime('%H:%M:%S')}")

                    time.sleep(30)  # 每30秒检测一次

                except Exception as e:
                    print(f"💔 心跳异常: {e}")
                    self.reconnect()

        self.heartbeat_thread = threading.Thread(target=heartbeat, daemon=True)
        self.heartbeat_thread.start()

    def reconnect(self):
        """重新连接"""
        print("🔄 开始重新连接...")
        self.connected = False

        # 关闭旧连接
        try:
            if self.quote_ctx:
                self.quote_ctx.close()
            if self.trd_ctx:
                self.trd_ctx.close()
        except:
            pass

        # 等待一段时间后重连
        time.sleep(5)

        # 尝试重连
        for i in range(3):
            if self.connect():
                print("✅ 重连成功")
                return True
            print(f"❌ 重连失败，第 {i+1} 次尝试")
            time.sleep(5)

        print("❌ 重连失败，请检查网络和OpenD状态")
        return False

    def close(self):
        """关闭连接"""
        self.running = False
        self.connected = False

        try:
            if self.quote_ctx:
                self.quote_ctx.close()
            if self.trd_ctx:
                self.trd_ctx.close()
            print("✅ 连接已关闭")
        except Exception as e:
            print(f"关闭连接时出错: {e}")

# 使用示例
connection = StableConnection()

try:
    if connection.connect():
        print("开始运行，按 Ctrl+C 停止...")
        while True:
            time.sleep(1)
except KeyboardInterrupt:
    print("\n⏹️ 用户停止程序")
finally:
    connection.close()
```

### Q23: 如何处理时区问题？

**A:**

```python
from datetime import datetime, timezone, timedelta
import pytz

def handle_timezone():
    """处理时区问题"""

    # 富途API使用的时区
    hk_tz = pytz.timezone('Asia/Hong_Kong')  # 港股时区
    us_tz = pytz.timezone('America/New_York')  # 美股时区
    cn_tz = pytz.timezone('Asia/Shanghai')  # A股时区

    # 获取当前时间
    now_utc = datetime.now(timezone.utc)

    print("🌍 各市场当前时间:")
    print(f"香港时间: {now_utc.astimezone(hk_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"美国时间: {now_utc.astimezone(us_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"中国时间: {now_utc.astimezone(cn_tz).strftime('%Y-%m-%d %H:%M:%S %Z')}")

    # 检查市场开市时间
    def is_market_open(market_tz, open_time, close_time):
        local_time = now_utc.astimezone(market_tz)
        current_time = local_time.time()

        # 检查是否在交易时间内
        if open_time <= current_time <= close_time:
            return True, "开市中"
        elif current_time < open_time:
            return False, f"未开市，将在 {open_time} 开市"
        else:
            return False, f"已收市，明日 {open_time} 开市"

    from datetime import time

    # 各市场交易时间（简化版本）
    markets = {
        "港股": (hk_tz, time(9, 30), time(16, 0)),
        "美股": (us_tz, time(9, 30), time(16, 0)),
        "A股": (cn_tz, time(9, 30), time(15, 0))
    }

    print("\n📈 市场状态:")
    for market_name, (tz, open_t, close_t) in markets.items():
        is_open, status = is_market_open(tz, open_t, close_t)
        status_icon = "🟢" if is_open else "🔴"
        print(f"{status_icon} {market_name}: {status}")

handle_timezone()
```

这个 FAQ 文档涵盖了富途 OpenAPI 使用中的大部分常见问题。如果您遇到的问题不在其中，建议：

1. 查看官方文档
2. 检查错误日志
3. 联系富途客服
4. 在社区寻求帮助

记住，良好的错误处理和日志记录是解决问题的关键！
