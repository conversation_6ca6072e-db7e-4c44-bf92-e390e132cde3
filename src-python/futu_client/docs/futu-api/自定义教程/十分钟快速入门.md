# 十分钟快速入门指南

本指南将帮助您在 10 分钟内快速上手富途 OpenAPI，体验基本的行情获取和交易功能。

## 第一步：环境准备（3 分钟）

### 1.1 检查 Python 环境

```bash
# 检查Python版本（需要3.6+）
python --version

# 如果没有Python，请先安装：
# Windows: 从python.org下载安装
# macOS: brew install python
# Ubuntu: sudo apt install python3
```

### 1.2 安装富途 API

```bash
# 安装富途API
uv add futu

# 验证安装
uv run python -c "import futu; print('安装成功')"
```

## 第二步：启动 OpenD（2 分钟）

### 2.1 下载并安装 OpenD

1. 访问：https://openapi.futunn.com/
2. 下载对应系统的 OpenD
3. 安装并启动 OpenD
4. 登录您的富途账号

### 2.2 验证 OpenD 连接

```python
import futu as ft

# 测试连接
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
ret, data = quote_ctx.get_global_state()

if ret == ft.RET_OK:
    print("✅ OpenD连接成功！")
else:
    print("❌ 连接失败，请检查OpenD是否启动")

quote_ctx.close()
```

## 第三步：获取第一个行情（2 分钟）

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅腾讯股票
    code = 'HK.00700'  # 腾讯控股
    ret, err = quote_ctx.subscribe([code], [ft.SubType.QUOTE])

    if ret == ft.RET_OK:
        # 获取实时报价
        ret, data = quote_ctx.get_stock_quote([code])

        if ret == ft.RET_OK:
            stock = data.iloc[0]
            print(f"🎉 获取报价成功！")
            print(f"股票代码: {stock['code']}")
            print(f"最新价格: {stock['last_price']:.2f}")
            print(f"涨跌幅: {((stock['last_price'] - stock['prev_close_price']) / stock['prev_close_price'] * 100):+.2f}%")
        else:
            print("获取报价失败:", data)
    else:
        print("订阅失败:", err)

finally:
    quote_ctx.close()
```

## 第四步：尝试模拟交易（3 分钟）

```python
import futu as ft

# 创建交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易（请替换为您的交易密码）
    password = "123456"  # 请修改为您的实际交易密码
    ret, data = trd_ctx.unlock_trade(password)

    if ret == ft.RET_OK:
        print("✅ 交易解锁成功")

        # 查看账户信息
        ret, accounts = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print(f"📋 找到 {len(accounts)} 个账户")

            # 使用模拟账户
            sim_accounts = accounts[accounts['trd_env'] == 'SIMULATE']
            if not sim_accounts.empty:
                acc_id = sim_accounts.iloc[0]['acc_id']
                print(f"使用模拟账户: {acc_id}")

                # 查看资金
                ret, funds = trd_ctx.accinfo_query(
                    trd_env=ft.TrdEnv.SIMULATE,
                    acc_id=acc_id
                )

                if ret == ft.RET_OK:
                    cash = funds.iloc[0]['cash']
                    print(f"💰 可用资金: {cash:,.2f}")

                    # 模拟下单
                    print(f"🛒 尝试买入腾讯...")
                    ret, order = trd_ctx.place_order(
                        price=300.0,           # 价格
                        qty=100,               # 数量（1手）
                        code="HK.00700",       # 腾讯
                        trd_side=ft.TrdSide.BUY,
                        trd_env=ft.TrdEnv.SIMULATE,
                        acc_id=acc_id
                    )

                    if ret == ft.RET_OK:
                        print("🎉 模拟下单成功！")
                        print(f"订单号: {order['order_id'].iloc[0]}")
                    else:
                        print("下单失败:", order)
            else:
                print("❌ 未找到模拟账户")
    else:
        print("❌ 交易解锁失败:", data)
        print("💡 请检查交易密码是否正确")

finally:
    trd_ctx.close()
```

## 常见问题快速解决

### Q: OpenD 无法启动？

**A:**

-   Windows: 右键"以管理员身份运行"
-   macOS: 检查安全设置，允许运行
-   检查端口 11111 是否被占用

### Q: 订阅失败？

**A:**

-   确保已登录富途账号
-   检查是否有行情权限
-   股票代码格式要正确（如 HK.00700）

### Q: 交易解锁失败？

**A:**

-   确认交易密码正确
-   确保账户开通了 OpenAPI 权限
-   首次使用需要在牛牛 App 中开通

### Q: 获取不到实时数据？

**A:**

-   数据可能有 15 分钟延迟（需购买实时行情）
-   检查市场是否开市
-   确认已正确订阅

## 下一步学习

现在您已经成功入门！接下来可以：

1. **深入学习**: 阅读[完整入门教程](完整入门教程.md)
2. **查看示例**: 浏览各 API 接口文档
3. **解决问题**: 查看[常见问题解答](常见问题解答.md)
4. **实盘交易**: 谨慎从模拟转到实盘

## 重要提醒

⚠️ **安全第一**

-   始终先在模拟环境测试
-   妥善保管账号密码
-   设置合理的风险控制

⚠️ **数据权限**

-   免费用户有 15 分钟延迟
-   实时行情需要额外购买
-   注意 API 调用频率限制

⚠️ **交易风险**

-   量化交易存在风险
-   充分测试策略后再投入实盘
-   遵守相关法规和交易规则

恭喜您完成快速入门！🎉
