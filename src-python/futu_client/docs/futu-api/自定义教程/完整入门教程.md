# 富途 OpenAPI 完整入门教程

本教程将带您从零开始，完整掌握富途 OpenAPI 的使用，包括环境搭建、基础操作、高级功能和生产部署。

## 第一章：环境准备

### 1.1 系统要求

**支持的操作系统：**

-   Windows 10/11
-   macOS 10.14+
-   Ubuntu 18.04+
-   CentOS 7+

**Python 要求：**

-   Python 3.6 或更高版本
-   推荐使用 Python 3.8+

### 1.2 安装 Python 环境

```bash
# 检查Python版本
python --version
python3 --version

# 如果没有Python，请先安装：
# Windows: 从python.org下载安装包
# macOS: brew install python
# Ubuntu: sudo apt install python3 python3-pip
# CentOS: sudo yum install python3 python3-pip
```

### 1.3 安装富途 API

```bash
# 安装富途API包
uv add futu

# 验证安装是否成功
uv run python -c "import futu; print('富途API安装成功，版本:', futu.__version__)"
```

## 第二章：OpenD 网关设置

### 2.1 下载和安装 OpenD

1. 访问富途 OpenAPI 官网：https://openapi.futunn.com/
2. 下载对应操作系统的 OpenD 程序
3. 安装 OpenD 到本地
4. 启动 OpenD 程序

### 2.2 配置 OpenD

**Windows 用户：**

```bash
# 以管理员身份运行OpenD
# 默认监听端口：11111
```

**macOS/Linux 用户：**

```bash
# 启动OpenD
./OpenD

# 或者指定端口
./OpenD --port=11111
```

### 2.3 登录富途账号

1. 在 OpenD 界面中输入您的富途账号和密码
2. 完成二次验证（如果需要）
3. 确认登录成功，状态显示为"已连接"

## 第三章：第一个程序

### 3.1 连接测试

```python
import futu as ft
import time

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 测试连接
    ret, data = quote_ctx.get_global_state()
    if ret == ft.RET_OK:
        print("✅ 连接OpenD成功！")
        print(f"服务器时间: {data}")
    else:
        print("❌ 连接失败:", data)

finally:
    quote_ctx.close()
```

### 3.2 获取股票报价

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅腾讯控股的报价
    code_list = ['HK.00700']  # 腾讯控股
    ret, err = quote_ctx.subscribe(code_list, [ft.SubType.QUOTE])

    if ret == ft.RET_OK:
        print("✅ 订阅成功")

        # 获取实时报价
        ret, data = quote_ctx.get_stock_quote(code_list)
        if ret == ft.RET_OK:
            print("📊 股票报价信息：")
            for index, row in data.iterrows():
                print(f"股票代码: {row['code']}")
                print(f"股票名称: {row['stock_name']}")
                print(f"最新价格: {row['last_price']:.2f}")
                print(f"涨跌额: {row['change_val']:+.2f}")
                print(f"涨跌幅: {row['change_rate']:+.2f}%")
                print(f"成交量: {row['volume']:,}")
                print(f"成交额: {row['turnover']:,.2f}")
        else:
            print("获取报价失败:", data)
    else:
        print("订阅失败:", err)

finally:
    quote_ctx.close()
```

## 第四章：交易功能入门

### 4.1 交易解锁

```python
import futu as ft

# 创建港股交易上下文
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易（请替换为您的实际交易密码）
    password = "您的交易密码"
    ret, data = trd_ctx.unlock_trade(password)

    if ret == ft.RET_OK:
        print("✅ 交易解锁成功")
    else:
        print("❌ 交易解锁失败:", data)

finally:
    trd_ctx.close()
```

### 4.2 查看账户信息

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 先解锁交易
    ret, data = trd_ctx.unlock_trade("您的交易密码")
    if ret != ft.RET_OK:
        print("解锁失败:", data)
        exit()

    # 获取账户列表
    ret, data = trd_ctx.get_acc_list()
    if ret == ft.RET_OK:
        print("📋 账户列表：")
        for index, row in data.iterrows():
            print(f"账户ID: {row['acc_id']}")
            print(f"交易环境: {row['trd_env']}")
            print(f"账户类型: {row['acc_type']}")
            print("---")

        # 查看第一个账户的资金信息
        if not data.empty:
            acc_id = data.iloc[0]['acc_id']
            trd_env = ft.TrdEnv.SIMULATE if data.iloc[0]['trd_env'] == 'SIMULATE' else ft.TrdEnv.REAL

            ret, fund_data = trd_ctx.accinfo_query(trd_env=trd_env, acc_id=acc_id)
            if ret == ft.RET_OK:
                fund = fund_data.iloc[0]
                print("💰 资金信息：")
                print(f"总资产: {fund['total_assets']:,.2f}")
                print(f"现金: {fund['cash']:,.2f}")
                print(f"可用资金: {fund['avl_withdrawal_cash']:,.2f}")
                print(f"市值: {fund['market_val']:,.2f}")
    else:
        print("获取账户列表失败:", data)

finally:
    trd_ctx.close()
```

### 4.3 模拟下单

```python
import futu as ft

trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("您的交易密码")
    if ret != ft.RET_OK:
        print("解锁失败:", data)
        exit()

    # 获取模拟账户
    ret, acc_data = trd_ctx.get_acc_list()
    if ret == ft.RET_OK:
        sim_accounts = acc_data[acc_data['trd_env'] == 'SIMULATE']
        if not sim_accounts.empty:
            acc_id = sim_accounts.iloc[0]['acc_id']

            # 模拟买入腾讯
            ret, order_data = trd_ctx.place_order(
                price=300.0,                    # 限价300港币
                qty=100,                        # 买入100股（1手）
                code="HK.00700",                # 腾讯控股
                trd_side=ft.TrdSide.BUY,       # 买入
                order_type=ft.OrderType.NORMAL, # 普通订单
                trd_env=ft.TrdEnv.SIMULATE,     # 模拟环境
                acc_id=acc_id
            )

            if ret == ft.RET_OK:
                order = order_data.iloc[0]
                print("🛒 下单成功！")
                print(f"订单号: {order['order_id']}")
                print(f"股票代码: {order['code']}")
                print(f"订单状态: {order['order_status']}")
                print(f"价格: {order['price']}")
                print(f"数量: {order['qty']}")
            else:
                print("下单失败:", order_data)
        else:
            print("未找到模拟账户")
    else:
        print("获取账户失败:", acc_data)

finally:
    trd_ctx.close()
```

## 第五章：高级功能

### 5.1 K 线数据获取

```python
import futu as ft
import pandas as pd
from datetime import datetime, timedelta

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取腾讯最近30天的日K线
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    ret, data = quote_ctx.get_cur_kline(
        code='HK.00700',
        num=30,
        ktype=ft.KLType.K_DAY
    )

    if ret == ft.RET_OK:
        print("📈 K线数据获取成功：")
        print(data.head())

        # 计算简单移动平均线
        data['MA5'] = data['close'].rolling(window=5).mean()
        data['MA10'] = data['close'].rolling(window=10).mean()

        print("\n📊 技术指标：")
        latest = data.iloc[-1]
        print(f"最新收盘价: {latest['close']:.2f}")
        print(f"5日均线: {latest['MA5']:.2f}")
        print(f"10日均线: {latest['MA10']:.2f}")
    else:
        print("获取K线失败:", data)

finally:
    quote_ctx.close()
```

### 5.2 实时数据推送

```python
import futu as ft
import time

class StockQuoteTest(ft.StockQuoteHandlerBase):
    """实时报价回调类"""
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super().on_recv_rsp(rsp_pb)
        if ret_code != ft.RET_OK:
            print("报价推送错误:", data)
            return ft.RET_ERROR, data

        print("📡 实时报价推送:")
        for index, row in data.iterrows():
            print(f"时间: {row['data_date']} {row['data_time']}")
            print(f"股票: {row['code']} - {row['stock_name']}")
            print(f"价格: {row['last_price']:.2f} ({row['change_rate']:+.2f}%)")
            print("---")

        return ft.RET_OK, data

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 设置回调
    quote_handler = StockQuoteTest()
    quote_ctx.set_handler(quote_handler)

    # 订阅实时报价
    ret, err = quote_ctx.subscribe(['HK.00700', 'HK.00005'], [ft.SubType.QUOTE])
    if ret == ft.RET_OK:
        print("✅ 订阅成功，开始接收实时数据...")
        print("按 Ctrl+C 停止...")

        # 持续运行接收数据
        while True:
            time.sleep(1)
    else:
        print("订阅失败:", err)

except KeyboardInterrupt:
    print("\n⏹️ 停止接收数据")
finally:
    quote_ctx.close()
```

## 第六章：策略开发示例

### 6.1 简单均线策略

```python
import futu as ft
import pandas as pd
import time
from datetime import datetime

class MovingAverageStrategy:
    def __init__(self, code, short_window=5, long_window=20):
        self.code = code
        self.short_window = short_window
        self.long_window = long_window
        self.quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
        self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
        self.position = 0  # 持仓数量

    def get_kline_data(self, num=50):
        """获取K线数据"""
        ret, data = self.quote_ctx.get_cur_kline(
            code=self.code,
            num=num,
            ktype=ft.KLType.K_DAY
        )

        if ret == ft.RET_OK:
            return data
        else:
            print("获取K线数据失败:", data)
            return None

    def calculate_signals(self, data):
        """计算交易信号"""
        # 计算移动平均线
        data['MA_short'] = data['close'].rolling(window=self.short_window).mean()
        data['MA_long'] = data['close'].rolling(window=self.long_window).mean()

        # 计算信号
        data['signal'] = 0
        data['signal'][self.short_window:] = np.where(
            data['MA_short'][self.short_window:] > data['MA_long'][self.short_window:], 1, 0
        )
        data['positions'] = data['signal'].diff()

        return data

    def execute_trade(self, signal):
        """执行交易"""
        try:
            # 解锁交易
            ret, data = self.trd_ctx.unlock_trade("您的交易密码")
            if ret != ft.RET_OK:
                print("解锁失败:", data)
                return

            # 获取模拟账户
            ret, acc_data = self.trd_ctx.get_acc_list()
            if ret == ft.RET_OK:
                sim_accounts = acc_data[acc_data['trd_env'] == 'SIMULATE']
                if not sim_accounts.empty:
                    acc_id = sim_accounts.iloc[0]['acc_id']

                    if signal == 1 and self.position == 0:
                        # 买入信号
                        ret, order_data = self.trd_ctx.place_order(
                            price=0,  # 市价单
                            qty=100,
                            code=self.code,
                            trd_side=ft.TrdSide.BUY,
                            order_type=ft.OrderType.MARKET,
                            trd_env=ft.TrdEnv.SIMULATE,
                            acc_id=acc_id
                        )

                        if ret == ft.RET_OK:
                            print(f"🛒 买入订单已发送: {self.code}")
                            self.position = 100
                        else:
                            print("买入失败:", order_data)

                    elif signal == -1 and self.position > 0:
                        # 卖出信号
                        ret, order_data = self.trd_ctx.place_order(
                            price=0,  # 市价单
                            qty=self.position,
                            code=self.code,
                            trd_side=ft.TrdSide.SELL,
                            order_type=ft.OrderType.MARKET,
                            trd_env=ft.TrdEnv.SIMULATE,
                            acc_id=acc_id
                        )

                        if ret == ft.RET_OK:
                            print(f"💰 卖出订单已发送: {self.code}")
                            self.position = 0
                        else:
                            print("卖出失败:", order_data)

        except Exception as e:
            print(f"执行交易出错: {e}")

    def run(self):
        """运行策略"""
        print(f"🚀 启动均线策略: {self.code}")
        print(f"短期均线: {self.short_window}日, 长期均线: {self.long_window}日")

        try:
            while True:
                # 获取数据
                data = self.get_kline_data()
                if data is not None and len(data) >= self.long_window:
                    # 计算信号
                    data = self.calculate_signals(data)

                    # 获取最新信号
                    latest_signal = data['positions'].iloc[-1]

                    if latest_signal != 0:
                        print(f"📊 检测到交易信号: {latest_signal}")
                        self.execute_trade(latest_signal)

                    # 显示当前状态
                    latest = data.iloc[-1]
                    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"收盘价: {latest['close']:.2f}")
                    print(f"短期均线: {latest['MA_short']:.2f}")
                    print(f"长期均线: {latest['MA_long']:.2f}")
                    print(f"当前持仓: {self.position}")
                    print("---")

                # 等待下次检查
                time.sleep(60)  # 每分钟检查一次

        except KeyboardInterrupt:
            print("\n⏹️ 策略停止")
        finally:
            self.quote_ctx.close()
            self.trd_ctx.close()

# 使用示例
if __name__ == "__main__":
    import numpy as np

    # 创建策略实例
    strategy = MovingAverageStrategy('HK.00700', short_window=5, long_window=20)

    # 运行策略
    strategy.run()
```

## 第七章：风险管理

### 7.1 资金管理

```python
import futu as ft

class RiskManager:
    def __init__(self, max_position_ratio=0.1, stop_loss_ratio=0.05):
        self.max_position_ratio = max_position_ratio  # 最大单只股票仓位比例
        self.stop_loss_ratio = stop_loss_ratio        # 止损比例
        self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

    def calculate_position_size(self, total_cash, stock_price):
        """计算合理的持仓数量"""
        max_investment = total_cash * self.max_position_ratio
        shares = int(max_investment / stock_price / 100) * 100  # 整手购买
        return shares

    def check_stop_loss(self, code, entry_price, current_price):
        """检查是否需要止损"""
        loss_ratio = (entry_price - current_price) / entry_price

        if loss_ratio >= self.stop_loss_ratio:
            print(f"⚠️ 触发止损: {code}, 亏损比例: {loss_ratio:.2%}")
            return True
        return False

    def get_account_summary(self):
        """获取账户摘要"""
        try:
            ret, acc_data = self.trd_ctx.get_acc_list()
            if ret == ft.RET_OK:
                for index, row in acc_data.iterrows():
                    acc_id = row['acc_id']
                    trd_env = ft.TrdEnv.SIMULATE if row['trd_env'] == 'SIMULATE' else ft.TrdEnv.REAL

                    # 获取资金信息
                    ret, fund_data = self.trd_ctx.accinfo_query(trd_env=trd_env, acc_id=acc_id)
                    if ret == ft.RET_OK:
                        fund = fund_data.iloc[0]
                        print(f"📊 账户摘要 ({row['trd_env']}):")
                        print(f"总资产: {fund['total_assets']:,.2f}")
                        print(f"现金: {fund['cash']:,.2f}")
                        print(f"市值: {fund['market_val']:,.2f}")
                        print(f"浮动盈亏: {fund['unrealized_pl']:,.2f}")
                        print(f"已实现盈亏: {fund['realized_pl']:,.2f}")
                        print("---")
        except Exception as e:
            print(f"获取账户信息失败: {e}")
```

### 7.2 监控和报警

```python
import futu as ft
import smtplib
from email.mime.text import MIMEText
from datetime import datetime

class AlertManager:
    def __init__(self, email_config=None):
        self.email_config = email_config
        self.alert_history = []

    def send_email_alert(self, subject, message):
        """发送邮件警报"""
        if not self.email_config:
            print(f"📧 邮件警报: {subject}")
            print(message)
            return

        try:
            msg = MIMEText(message, 'plain', 'utf-8')
            msg['Subject'] = subject
            msg['From'] = self.email_config['from']
            msg['To'] = self.email_config['to']

            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['port'])
            server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()

            print(f"✅ 邮件警报已发送: {subject}")
        except Exception as e:
            print(f"❌ 邮件发送失败: {e}")

    def check_price_alert(self, code, current_price, target_price, alert_type="价格警报"):
        """检查价格警报"""
        alert_key = f"{code}_{target_price}_{alert_type}"

        if alert_key not in self.alert_history:
            if current_price >= target_price:
                message = f"""
股票代码: {code}
当前价格: {current_price:.2f}
目标价格: {target_price:.2f}
触发时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
                self.send_email_alert(f"价格警报 - {code}", message)
                self.alert_history.append(alert_key)
```

## 第八章：部署和运维

### 8.1 服务器部署

```python
# 配置文件示例 - config.py
import os

class Config:
    # OpenD连接配置
    OPEND_HOST = os.getenv('OPEND_HOST', '127.0.0.1')
    OPEND_PORT = int(os.getenv('OPEND_PORT', 11111))

    # 交易配置
    TRADE_PASSWORD = os.getenv('TRADE_PASSWORD', '')
    TRADE_ENV = os.getenv('TRADE_ENV', 'SIMULATE')  # SIMULATE 或 REAL

    # 风险管理
    MAX_POSITION_RATIO = float(os.getenv('MAX_POSITION_RATIO', 0.1))
    STOP_LOSS_RATIO = float(os.getenv('STOP_LOSS_RATIO', 0.05))

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'trading.log')

    # 邮件配置
    EMAIL_SMTP_SERVER = os.getenv('EMAIL_SMTP_SERVER', 'smtp.gmail.com')
    EMAIL_PORT = int(os.getenv('EMAIL_PORT', 587))
    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME', '')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD', '')
    EMAIL_TO = os.getenv('EMAIL_TO', '')
```

### 8.2 日志记录

```python
import logging
import sys
from datetime import datetime

def setup_logger(name, log_file, level=logging.INFO):
    """设置日志记录器"""
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# 使用示例
logger = setup_logger('TradingBot', 'trading.log')

class TradingBot:
    def __init__(self):
        self.logger = logger

    def execute_trade(self, code, action, qty, price):
        """执行交易并记录日志"""
        self.logger.info(f"开始执行交易: {action} {code} {qty}股 @{price}")

        try:
            # 执行交易逻辑
            result = self.place_order(code, action, qty, price)

            if result['success']:
                self.logger.info(f"交易成功: 订单号 {result['order_id']}")
            else:
                self.logger.error(f"交易失败: {result['error']}")

        except Exception as e:
            self.logger.error(f"交易异常: {str(e)}", exc_info=True)
```

### 8.3 监控脚本

```bash
#!/bin/bash
# monitor.sh - 监控脚本

SCRIPT_DIR="/path/to/your/trading/bot"
LOG_FILE="$SCRIPT_DIR/trading.log"
PID_FILE="$SCRIPT_DIR/trading.pid"

check_process() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "交易机器人正在运行 (PID: $PID)"
            return 0
        else
            echo "PID文件存在但进程不在运行"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        echo "交易机器人未运行"
        return 1
    fi
}

start_bot() {
    cd "$SCRIPT_DIR"
    python main.py &
    echo $! > "$PID_FILE"
    echo "交易机器人已启动 (PID: $!)"
}

stop_bot() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        kill "$PID"
        rm -f "$PID_FILE"
        echo "交易机器人已停止"
    else
        echo "交易机器人未运行"
    fi
}

case "$1" in
    start)
        if check_process; then
            echo "交易机器人已在运行"
        else
            start_bot
        fi
        ;;
    stop)
        stop_bot
        ;;
    restart)
        stop_bot
        sleep 2
        start_bot
        ;;
    status)
        check_process
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
```

## 第九章：常见问题解决

### 9.1 连接问题

**问题 1: 无法连接到 OpenD**

```python
# 解决方案
import futu as ft
import time

def test_connection():
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            print("✅ 连接成功")
            return True
        else:
            print(f"❌ 连接失败: {data}")
            return False
    except Exception as e:
        print(f"❌ 连接异常: {e}")
        return False
    finally:
        quote_ctx.close()

# 重试连接
for i in range(5):
    if test_connection():
        break
    print(f"第 {i+1} 次重试...")
    time.sleep(5)
```

**问题 2: 订阅失败**

```python
# 检查订阅限制
def check_subscription_limit():
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

    try:
        ret, data = quote_ctx.get_subscription_list()
        if ret == ft.RET_OK:
            print(f"当前订阅数量: {len(data)}")
            print("订阅列表:")
            for index, row in data.iterrows():
                print(f"  {row['code']} - {row['sub_type']}")
        else:
            print("获取订阅列表失败:", data)
    finally:
        quote_ctx.close()
```

### 9.2 交易问题

**问题 3: 交易解锁失败**

```python
def troubleshoot_unlock():
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

    try:
        # 检查是否已经解锁
        ret, data = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("✅ 可以获取账户列表，连接正常")

            # 尝试解锁
            password = input("请输入交易密码: ")
            ret, unlock_data = trd_ctx.unlock_trade(password)

            if ret == ft.RET_OK:
                print("✅ 交易解锁成功")
            else:
                print(f"❌ 交易解锁失败: {unlock_data}")
                print("请检查:")
                print("1. 交易密码是否正确")
                print("2. 账户是否开通OpenAPI权限")
                print("3. 是否在富途App中启用了API交易")
        else:
            print(f"❌ 获取账户列表失败: {data}")
    finally:
        trd_ctx.close()
```

## 第十章：进阶策略

### 10.1 网格交易策略

```python
import futu as ft
import numpy as np
from datetime import datetime, timedelta

class GridTradingStrategy:
    def __init__(self, code, center_price, grid_spacing=0.01, grid_levels=10, lot_size=100):
        self.code = code
        self.center_price = center_price
        self.grid_spacing = grid_spacing  # 网格间距(比例)
        self.grid_levels = grid_levels    # 网格层数
        self.lot_size = lot_size         # 每格交易手数

        self.quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
        self.trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

        # 计算网格价位
        self.buy_grids = []
        self.sell_grids = []

        for i in range(1, grid_levels + 1):
            buy_price = center_price * (1 - grid_spacing * i)
            sell_price = center_price * (1 + grid_spacing * i)

            self.buy_grids.append({
                'price': buy_price,
                'filled': False,
                'order_id': None
            })

            self.sell_grids.append({
                'price': sell_price,
                'filled': False,
                'order_id': None
            })

    def place_grid_orders(self):
        """放置网格订单"""
        try:
            # 解锁交易
            ret, data = self.trd_ctx.unlock_trade("您的交易密码")
            if ret != ft.RET_OK:
                print("解锁失败:", data)
                return

            # 获取模拟账户
            ret, acc_data = self.trd_ctx.get_acc_list()
            if ret == ft.RET_OK:
                sim_accounts = acc_data[acc_data['trd_env'] == 'SIMULATE']
                if not sim_accounts.empty:
                    acc_id = sim_accounts.iloc[0]['acc_id']

                    # 放置买入网格订单
                    for grid in self.buy_grids:
                        if not grid['filled']:
                            ret, order_data = self.trd_ctx.place_order(
                                price=grid['price'],
                                qty=self.lot_size,
                                code=self.code,
                                trd_side=ft.TrdSide.BUY,
                                order_type=ft.OrderType.NORMAL,
                                trd_env=ft.TrdEnv.SIMULATE,
                                acc_id=acc_id
                            )

                            if ret == ft.RET_OK:
                                grid['order_id'] = order_data.iloc[0]['order_id']
                                print(f"💚 买入网格订单已放置: {grid['price']:.2f}")

                    # 放置卖出网格订单（如果有持仓）
                    for grid in self.sell_grids:
                        if not grid['filled']:
                            ret, order_data = self.trd_ctx.place_order(
                                price=grid['price'],
                                qty=self.lot_size,
                                code=self.code,
                                trd_side=ft.TrdSide.SELL,
                                order_type=ft.OrderType.NORMAL,
                                trd_env=ft.TrdEnv.SIMULATE,
                                acc_id=acc_id
                            )

                            if ret == ft.RET_OK:
                                grid['order_id'] = order_data.iloc[0]['order_id']
                                print(f"❤️ 卖出网格订单已放置: {grid['price']:.2f}")

        except Exception as e:
            print(f"放置网格订单失败: {e}")

    def monitor_grids(self):
        """监控网格执行情况"""
        print("🔍 开始监控网格交易...")

        try:
            while True:
                # 检查订单状态
                self.check_order_status()

                # 获取当前价格
                ret, quote_data = self.quote_ctx.get_stock_quote([self.code])
                if ret == ft.RET_OK:
                    current_price = quote_data.iloc[0]['last_price']
                    print(f"当前价格: {current_price:.2f}")

                time.sleep(10)  # 每10秒检查一次

        except KeyboardInterrupt:
            print("\n⏹️ 停止监控")
        finally:
            self.quote_ctx.close()
            self.trd_ctx.close()

    def check_order_status(self):
        """检查订单状态"""
        # 这里可以添加检查订单执行状态的逻辑
        # 当网格订单成交时，自动在相应位置放置新的反向订单
        pass
```

## 总结

本教程涵盖了富途 OpenAPI 的完整使用流程，从基础环境搭建到高级策略开发。主要内容包括：

1. **环境搭建** - Python 安装、富途 API 安装、OpenD 配置
2. **基础操作** - 连接测试、行情获取、账户查询
3. **交易功能** - 解锁交易、下单、持仓管理
4. **高级功能** - K 线分析、实时数据推送、技术指标
5. **策略开发** - 均线策略、网格交易等
6. **风险管理** - 资金管理、止损止盈、监控报警
7. **部署运维** - 服务器部署、日志记录、监控脚本

**重要提醒：**

-   请先在模拟环境充分测试
-   注意风险控制和资金管理
-   遵守相关法规和交易规则
-   保护好账号密码等敏感信息

**下一步学习建议：**

-   深入学习量化交易理论
-   研究更多技术指标和策略
-   关注市场动态和政策变化
-   加入量化交易社区交流学习

祝您在量化交易的道路上取得成功！🎉
