# 命令行 OpenD

### 第一步 下载

命令行 OpenD 支持 Windows、MacOS、CentOS、Ubuntu 四种系统（点击完成下载）。

- OpenD - [Windows](https://www.futunn.com/download/fetch-lasted-link?name=opend-windows)、[MacOS](https://www.futunn.com/download/fetch-lasted-link?name=opend-macos)、[CentOS](https://www.futunn.com/download/fetch-lasted-link?name=opend-centos)、[Ubuntu](https://www.futunn.com/download/fetch-lasted-link?name=opend-ubuntu)

### 第二步 解压

- 解压上一步下载的文件，在文件夹中找到 OpenD 配置文件 FutuOpenD.xml 和程序打包数据文件 Appdata.dat。
  - FutuOpenD.xml 用于配置 OpenD 程序启动参数，若不存在则程序无法正常启动。
  - Appdata.dat 是程序需要用到的一些数据量较大的信息，打包数据减少启动下载该数据的耗时，若不存在则程序无法正常启动。

- 命令行 OpenD 支持用户自定义文件路径，详见[命令行启动参数](#第四步-命令行启动)。

### 第三步 参数配置

- 打开并编辑配置文件 FutuOpenD.xml，如下图所示。普通使用仅需修改账号和登录密码，其他高阶选项可以根据下表的提示进行修改。

![XML配置](https://openapi.futunn.com/futu-api-doc/assets/xml-config-7b644830.png)

**配置项列表**：

| 配置项 | 说明 |
|--------|------|
| ip | 监听地址 |
| api_port | API 协议接收端口 |
| login_account | 登录帐号 |
| login_pwd | 登录密码明文 |
| login_pwd_md5 | 登录密码密文（32 位 MD5 加密 16 进制） |
| lang | 中英语言 |
| log_level | OpenD 日志级别 |
| push_proto_type | 推送协议类型 |
| qot_push_frequency | API 订阅数据推送频率控制 |
| telnet_ip | 远程操作命令监听地址 |
| telnet_port | 远程操作命令监听端口 |
| rsa_private_key | API 协议 [RSA](../qa/other.md#4601) 加密私钥（PKCS#1）文件绝对路径 |
| price_reminder_push | 是否接收到价提醒推送 |
| auto_hold_quote_right | 被踢后是否自动抢权限 |
| future_trade_api_time_zone | 期货交易 API 时区 |
| websocket_ip | WebSocket 服务监听地址 |
| websocket_port | WebSocket 服务监听端口 |
| websocket_key_md5 | 密钥密文（32 位 MD5 加密 16 进制） |
| websocket_private_key | WebSocket 证书私钥文件路径 |
| websocket_cert | WebSocket 证书文件路径 |
| pdt_protection | 是否开启 防止被标记为日内交易者 的功能 |
| dtcall_confirmation | 是否开启 日内交易保证金追缴预警 的功能 |

> **提示**
> 
> - 为保证您的证券业务账户安全，如果监听地址不是本地，您必须配置私钥才能使用交易接口。行情接口不受此限制。
> - 当 WebSocket 监听地址不是本地，需配置 SSL 才可以启动，且证书私钥生成不可设置密码。
> - 密文是明文经过 32 位 MD5 加密后用 16 进制表示的数据，搜索在线 MD5 加密（注意，通过第三方网站计算可能有记录撞库的风险）或下载 MD5 计算工具可计算得到。32 位 MD5 密文如下图红框区域（e10adc3949ba59abbe56e057f20f883e）：![MD5示例](https://openapi.futunn.com/futu-api-doc/assets/md5-7b644830.png)
> - OpenD 默认读取同目录下的 FutuOpenD.xml。在 MacOS 上，由于系统保护机制，OpenD.app 在运行时会被分配一个随机路径，导致无法找到原本的路径。此时有以下方法：
>   - 执行 tar 包下的 fixrun.sh
>   - 用命令行参数 `-cfg_file` 指定配置文件路径，见下面说明
> - 日志级别默认 info 级别，在系统开发阶段，不建议关闭日志或者将日志修改到 warning，error，fatal 级别，防止出现问题时无法定位。

### 第四步 命令行启动

- 在命令行中切到前面解压文件夹 OpenD 文件所在的目录，使用如下命令启动，即可以 FutuOpenD.xml 配置文件中的参数启动。
  - Windows：`FutuOpenD`
  - Linux：`./FutuOpenD`  
  - MacOS：`./FutuOpenD.app/Contents/MacOS/FutuOpenD`