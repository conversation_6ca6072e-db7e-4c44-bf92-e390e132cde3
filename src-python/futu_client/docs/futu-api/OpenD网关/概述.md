# OpenD安装配置

OpenD是富途OpenAPI的桌面网关程序，作为API客户端和富途服务器之间的桥梁，负责身份验证、数据转发和协议转换。

## OpenD简介

### 什么是OpenD

OpenD（Open Desktop）是富途推出的桌面应用程序，具有以下功能：

1. **身份认证**：通过富途账号登录，获取API访问权限
2. **数据网关**：在API客户端和富途服务器间转发数据
3. **协议转换**：将HTTP/TCP协议转换为富途内部协议
4. **权限控制**：管理不同API的访问权限
5. **本地缓存**：缓存行情数据，提高访问速度

### 架构图

```
API客户端 ←→ OpenD ←→ 富途服务器
   (您的代码)    (本地网关)    (行情/交易服务)
```

## 下载安装

### 官方下载

访问富途OpenAPI官网下载OpenD：
- 官网地址：https://openapi.futunn.com/
- 直接下载：https://openapi.futunn.com/download

### 系统要求

| 操作系统 | 最低版本 | 推荐配置 |
|----------|----------|----------|
| Windows | Windows 7 SP1 | Windows 10/11 |
| macOS | macOS 10.12 | macOS 11.0+ |
| 内存 | 2GB | 4GB+ |
| 磁盘空间 | 500MB | 1GB+ |

### 安装步骤

#### Windows安装

1. 下载`FutuOpenD_x.x.x_Setup.exe`
2. 右键选择"以管理员身份运行"
3. 按照安装向导完成安装
4. 安装完成后从开始菜单启动OpenD

#### macOS安装

1. 下载`FutuOpenD_x.x.x.dmg`
2. 双击dmg文件挂载磁盘镜像
3. 将FutuOpenD拖拽到Applications文件夹
4. 从Launchpad或Applications文件夹启动OpenD

## 初次配置

### 1. 启动OpenD

首次启动OpenD会显示配置界面：

```
┌─────────────────────────────────────┐
│           富途OpenD                  │
├─────────────────────────────────────┤
│ 登录方式：                          │
│ ○ 富途账号密码登录                   │
│ ○ 手机验证码登录                     │
│                                     │
│ 账号：[___________________]         │
│ 密码：[___________________]         │
│                                     │
│ 服务器：○ 正式环境  ○ 仿真环境       │
│                                     │
│        [登录]    [设置]             │
└─────────────────────────────────────┘
```

### 2. 登录富途账号

#### 使用富途账号登录

```python
# 示例：检测OpenD登录状态
import futu as ft

# 创建行情上下文测试连接
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
ret, data = quote_ctx.get_global_state()

if ret == ft.RET_OK:
    print("OpenD连接成功")
    print(f"登录状态: {data}")
else:
    print("OpenD连接失败，请检查：")
    print("1. OpenD是否已启动")
    print("2. 是否已登录富途账号")
    print("3. 端口11111是否被占用")

quote_ctx.close()
```

### 3. 端口配置

OpenD默认使用以下端口：

| 服务类型 | 默认端口 | 说明 |
|----------|----------|------|
| 行情服务 | 11111 | 获取股票行情数据 |
| 交易服务 | 11111 | 执行交易操作 |
| 历史数据 | 11111 | 获取历史行情数据 |

#### 修改端口配置

1. 点击OpenD界面的"设置"按钮
2. 在"高级设置"中修改端口
3. 重启OpenD使配置生效

```python
# 使用自定义端口连接
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=12345)
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=12345)
```

## 高级配置

### 1. 网络设置

#### 代理配置

如果您的网络环境需要代理：

1. 打开OpenD设置
2. 选择"网络设置"
3. 配置代理服务器信息

```
代理设置：
- HTTP代理：proxy.company.com:8080
- 用户名：username
- 密码：password
```

#### 防火墙设置

确保以下端口允许OpenD访问：

```
出站端口：
- 443 (HTTPS)
- 80 (HTTP)  
- 5000-5100 (富途服务器端口范围)

入站端口：
- 11111 (OpenD API端口)
```

### 2. 权限设置

#### 行情权限

```python
import futu as ft

# 检查行情权限
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 检查实时行情权限
ret, data = quote_ctx.get_user_security('HK')
if ret == ft.RET_OK:
    print("港股行情权限:", data)
else:
    print("权限查询失败:", data)

# 检查订阅额度
ret, data = quote_ctx.get_subscription_quota()
if ret == ft.RET_OK:
    print("订阅额度:", data)

quote_ctx.close()
```

#### 交易权限

```python
import futu as ft

# 检查交易权限
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 尝试解锁交易
    ret, data = trd_ctx.unlock_trade("your_password")
    if ret == ft.RET_OK:
        print("交易权限正常")
        
        # 检查账户权限
        ret, acc_list = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("可用账户:", len(acc_list))
            for _, acc in acc_list.iterrows():
                print(f"  账户{acc['acc_id']}: {acc['trd_market_auth']}")
    else:
        print("交易解锁失败:", data)
        
finally:
    trd_ctx.close()
```

### 3. 日志配置

#### 启用调试日志

```python
import futu as ft

# 启用调试模式
ft.SysConfig.set_log_level(ft.LogLevel.DEBUG)

# 设置日志文件路径
ft.SysConfig.set_log_file_path('/path/to/logs')

# 启用网络调试
ft.SysConfig.enable_proto_encrypt(False)  # 仅调试时使用
```

#### 查看OpenD日志

OpenD日志文件位置：

**Windows:**
```
C:\Users\<USER>\AppData\Local\Futu\FutuOpenD\logs\
```

**macOS:**
```
~/Library/Application Support/Futu/FutuOpenD/logs/
```

## 环境切换

### 1. 正式环境

用于实盘交易，连接真实的富途服务器：

```python
import futu as ft

# 正式环境配置
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

# 在正式环境下单
ret, data = trd_ctx.place_order(
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    trd_env=ft.TrdEnv.REAL  # 真实环境
)
```

### 2. 仿真环境

用于测试和学习，使用虚拟资金：

```python
import futu as ft

# 仿真环境下单
ret, data = trd_ctx.place_order(
    price=100.0,
    qty=100,
    code="HK.00700",
    trd_side=ft.TrdSide.BUY,
    trd_env=ft.TrdEnv.SIMULATE  # 仿真环境
)
```

## 连接测试

### 完整连接测试脚本

```python
import futu as ft
import time

def test_opend_connection():
    """测试OpenD连接状态"""
    print("=== OpenD连接测试 ===\n")
    
    # 1. 测试行情连接
    print("1. 测试行情连接...")
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        ret, data = quote_ctx.get_global_state()
        if ret == ft.RET_OK:
            print("   ✓ 行情连接成功")
            print(f"   服务器时间: {data}")
        else:
            print("   ✗ 行情连接失败:", data)
            return False
    except Exception as e:
        print("   ✗ 行情连接异常:", e)
        return False
    finally:
        quote_ctx.close()
    
    # 2. 测试市场状态
    print("\n2. 测试市场状态...")
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        ret, data = quote_ctx.get_market_state(['HK', 'US'])
        if ret == ft.RET_OK:
            print("   ✓ 市场状态获取成功")
            for _, market in data.iterrows():
                print(f"   {market['market']}: {market['market_state']}")
        else:
            print("   ✗ 市场状态获取失败:", data)
    except Exception as e:
        print("   ✗ 市场状态获取异常:", e)
    finally:
        quote_ctx.close()
    
    # 3. 测试交易连接
    print("\n3. 测试交易连接...")
    trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)
    
    try:
        ret, data = trd_ctx.get_acc_list()
        if ret == ft.RET_OK:
            print("   ✓ 交易连接成功")
            print(f"   可用账户数: {len(data)}")
        else:
            print("   ✗ 交易连接失败:", data)
    except Exception as e:
        print("   ✗ 交易连接异常:", e)
    finally:
        trd_ctx.close()
    
    print("\n=== 测试完成 ===")
    return True

# 运行测试
if __name__ == "__main__":
    test_opend_connection()
```

## 常见问题

### 1. OpenD无法启动

**问题**：双击OpenD图标没有反应

**解决方案**：
1. 检查是否有OpenD进程残留：
   ```bash
   # Windows
   tasklist | findstr FutuOpenD
   taskkill /F /IM FutuOpenD.exe
   
   # macOS
   ps aux | grep FutuOpenD
   sudo killall FutuOpenD
   ```

2. 以管理员权限启动
3. 检查防病毒软件是否拦截
4. 重新安装OpenD

### 2. 登录失败

**问题**：输入正确账号密码仍无法登录

**解决方案**：
1. 检查网络连接
2. 确认富途账号状态正常
3. 尝试使用手机验证码登录
4. 检查是否需要在富途牛牛App中开启OpenAPI权限

### 3. API连接超时

**问题**：Python代码连接OpenD超时

**解决方案**：
```python
import futu as ft

# 增加连接超时时间
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_sync_query_connect_timeout(10)  # 10秒超时

# 检查OpenD状态
ret, data = quote_ctx.get_global_state()
if ret != ft.RET_OK:
    print("OpenD未正常运行")
```

### 4. 端口被占用

**问题**：11111端口被其他程序占用

**解决方案**：
```bash
# 查看端口占用情况
# Windows
netstat -ano | findstr 11111

# macOS/Linux  
lsof -i :11111

# 修改OpenD端口或终止占用程序
```

## 性能优化

### 1. 连接池管理

```python
import futu as ft
from threading import Lock

class OpenDConnectionPool:
    def __init__(self, host='127.0.0.1', port=11111, max_connections=5):
        self.host = host
        self.port = port
        self.max_connections = max_connections
        self.quote_pool = []
        self.trade_pool = []
        self.lock = Lock()
    
    def get_quote_context(self):
        with self.lock:
            if self.quote_pool:
                return self.quote_pool.pop()
            else:
                return ft.OpenQuoteContext(host=self.host, port=self.port)
    
    def return_quote_context(self, ctx):
        with self.lock:
            if len(self.quote_pool) < self.max_connections:
                self.quote_pool.append(ctx)
            else:
                ctx.close()
    
    def cleanup(self):
        with self.lock:
            for ctx in self.quote_pool + self.trade_pool:
                ctx.close()
            self.quote_pool.clear()
            self.trade_pool.clear()

# 使用连接池
pool = OpenDConnectionPool()
quote_ctx = pool.get_quote_context()

try:
    # 使用连接进行操作
    ret, data = quote_ctx.get_stock_quote(['HK.00700'])
finally:
    pool.return_quote_context(quote_ctx)
```

### 2. 缓存配置

```python
import futu as ft

# 启用本地缓存
ft.SysConfig.enable_cache(True)
ft.SysConfig.set_cache_size(1000)  # 设置缓存大小

# 配置缓存过期时间
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_cache_timeout(60)  # 缓存60秒
```

## 安全建议

1. **账号安全**：
   - 定期修改富途账号密码
   - 启用两步验证
   - 避免在公共网络使用

2. **API安全**：
   - 不要在代码中硬编码交易密码
   - 使用环境变量或配置文件
   - 定期检查API调用日志

3. **系统安全**：
   - 保持OpenD版本更新
   - 配置防火墙规则
   - 监控异常连接

通过正确安装和配置OpenD，您就可以开始使用富途OpenAPI进行程序化交易和行情获取了。