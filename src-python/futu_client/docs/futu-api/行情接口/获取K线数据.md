# 获取K线数据

获取股票的K线数据，支持多种时间周期，包括分钟线、日线、周线、月线等。

## get_cur_kline - 获取实时K线

### 接口原型

```python
get_cur_kline(code, num, ktype=KLType.K_DAY, autype=AuType.QFQ)
```

### 功能说明

实时获取指定股票最近num个K线数据。**注意：使用此接口前需要先订阅相应的K线数据。**

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 是 | 股票代码，例如'HK.00700' |
| num | int | 是 | 获取K线的数量，最大1000 |
| ktype | KLType | 否 | K线类型，默认K_DAY |
| autype | AuType | 否 | 复权类型，默认前复权 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | K线数据DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| time_key | str | K线时间 |
| open | float | 开盘价 |
| close | float | 收盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| pe_ratio | float | 市盈率 |
| turnover_rate | float | 换手率 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| change_rate | float | 涨跌幅 |
| last_close | float | 昨收价 |

## request_history_kline - 获取历史K线

### 接口原型

```python
request_history_kline(code, start=None, end=None, ktype=KLType.K_DAY, autype=AuType.QFQ, fields=[KL_FIELD.ALL], max_count=1000, page_req_key=None, extended_time=False)
```

### 功能说明

获取历史K线数据，不需要事先订阅，支持分页获取大量历史数据。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 是 | 股票代码 |
| start | str | 否 | 开始时间，例如'2020-01-01' |
| end | str | 否 | 结束时间，例如'2020-12-31' |
| ktype | KLType | 否 | K线类型，默认日线 |
| autype | AuType | 否 | 复权类型，默认前复权 |
| fields | list | 否 | 需要的字段列表 |
| max_count | int | 否 | 最大数据量，默认1000 |
| page_req_key | str | 否 | 分页请求key |
| extended_time | bool | 否 | 是否包含盘前盘后数据 |

### 代码示例

#### 获取实时K线

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅K线数据
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.K_DAY])
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
    quote_ctx.close()
    exit()

# 获取最近10个交易日的K线
ret, data = quote_ctx.get_cur_kline('HK.00700', 10, ft.KLType.K_DAY)
if ret == ft.RET_OK:
    print("腾讯最近10日K线:")
    print(data[['time_key', 'open', 'close', 'high', 'low', 'volume']])
else:
    print('获取K线失败:', data)

quote_ctx.close()
```

#### 获取不同周期的K线

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅不同周期的K线
kline_types = [ft.SubType.K_1M, ft.SubType.K_5M, ft.SubType.K_DAY]
quote_ctx.subscribe(['HK.00700'], kline_types)

# 获取不同周期的数据
kline_configs = [
    (ft.KLType.K_1M, "1分钟", 30),
    (ft.KLType.K_5M, "5分钟", 20),
    (ft.KLType.K_DAY, "日线", 5)
]

for ktype, name, num in kline_configs:
    ret, data = quote_ctx.get_cur_kline('HK.00700', num, ktype)
    if ret == ft.RET_OK:
        print(f"\n{name}K线（最近{num}个）:")
        print(data[['time_key', 'close', 'volume']].tail())
    else:
        print(f'获取{name}K线失败:', data)

quote_ctx.close()
```

#### 获取历史K线数据

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯2023年的历史日线数据
ret, data = quote_ctx.request_history_kline(
    code='HK.00700',
    start='2023-01-01',
    end='2023-12-31',
    ktype=ft.KLType.K_DAY,
    autype=ft.AuType.QFQ,
    max_count=1000
)

if ret == ft.RET_OK:
    print(f"获取到 {len(data)} 条历史K线数据")
    print("\n数据概览:")
    print(data[['time_key', 'open', 'close', 'high', 'low', 'volume']].head())
    
    # 计算年度统计
    annual_high = data['high'].max()
    annual_low = data['low'].min()
    start_price = data['open'].iloc[0]
    end_price = data['close'].iloc[-1]
    annual_return = (end_price - start_price) / start_price * 100
    
    print(f"\n2023年统计:")
    print(f"年度最高价: {annual_high:.2f}")
    print(f"年度最低价: {annual_low:.2f}")
    print(f"年初价格: {start_price:.2f}")
    print(f"年末价格: {end_price:.2f}")
    print(f"年度收益率: {annual_return:.2f}%")
else:
    print('获取历史K线失败:', data)

quote_ctx.close()
```

#### K线技术分析

```python
import futu as ft
import pandas as pd

def calculate_ma(data, periods=[5, 10, 20]):
    """计算移动平均线"""
    for period in periods:
        data[f'MA{period}'] = data['close'].rolling(window=period).mean()
    return data

def calculate_rsi(data, period=14):
    """计算RSI指标"""
    delta = data['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    data['RSI'] = rsi
    return data

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取历史数据进行技术分析
ret, data = quote_ctx.request_history_kline(
    code='HK.00700',
    start='2023-01-01',
    end='2023-12-31',
    ktype=ft.KLType.K_DAY
)

if ret == ft.RET_OK:
    # 计算技术指标
    data = calculate_ma(data)
    data = calculate_rsi(data)
    
    # 显示最近的数据和指标
    print("腾讯技术分析指标:")
    columns = ['time_key', 'close', 'MA5', 'MA10', 'MA20', 'RSI']
    print(data[columns].tail(10))
    
    # 分析当前趋势
    latest = data.iloc[-1]
    if latest['close'] > latest['MA20']:
        trend = "上升趋势"
    elif latest['close'] < latest['MA20']:
        trend = "下降趋势"
    else:
        trend = "横盘整理"
    
    rsi_signal = "超买" if latest['RSI'] > 70 else "超卖" if latest['RSI'] < 30 else "正常"
    
    print(f"\n当前趋势分析:")
    print(f"价格趋势: {trend}")
    print(f"RSI信号: {rsi_signal} ({latest['RSI']:.2f})")

quote_ctx.close()
```

### K线类型说明

#### KLType（K线周期）

| 常量 | 说明 |
|------|------|
| K_1M | 1分钟 |
| K_3M | 3分钟 |
| K_5M | 5分钟 |
| K_15M | 15分钟 |
| K_30M | 30分钟 |
| K_60M | 60分钟 |
| K_DAY | 日K线 |
| K_WEEK | 周K线 |
| K_MONTH | 月K线 |
| K_QUARTER | 季K线 |
| K_YEAR | 年K线 |

#### AuType（复权类型）

| 常量 | 说明 |
|------|------|
| QFQ | 前复权 |
| HFQ | 后复权 |
| NONE | 不复权 |

### 数据获取限制

#### 实时K线限制

| K线类型 | 最大数量 |
|----------|----------|
| 分钟线 | 1000 |
| 日线 | 1000 |
| 周线 | 1000 |
| 月线 | 1000 |

#### 历史K线限制

| 用户类型 | 日限额 |
|----------|--------|
| 免费用户 | 100只股票 |
| 付费用户 | 根据套餐 |

### 注意事项

1. **订阅要求**：`get_cur_kline`需要先订阅相应的K线数据
2. **历史数据**：`request_history_kline`无需订阅，但有配额限制
3. **复权处理**：建议使用前复权数据进行技术分析
4. **时间格式**：时间参数格式为'YYYY-MM-DD'或'YYYY-MM-DD HH:MM:SS'
5. **数据完整性**：停牌期间可能缺少数据

### 错误处理

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 尝试获取K线数据
    ret, data = quote_ctx.get_cur_kline('HK.00700', 10)
    if ret != ft.RET_OK:
        if "need subscribe" in str(data).lower():
            print("需要先订阅K线数据")
            # 订阅后重试
            quote_ctx.subscribe(['HK.00700'], [ft.SubType.K_DAY])
            ret, data = quote_ctx.get_cur_kline('HK.00700', 10)
        else:
            print(f"获取K线失败: {data}")
    
    if ret == ft.RET_OK:
        print("K线数据获取成功")
        print(data.head())
        
except Exception as e:
    print(f"获取K线时发生异常: {e}")
finally:
    quote_ctx.close()
```

### 相关接口

- [订阅反订阅](sub.md) - 订阅K线数据
- [K线推送](update-kl.md) - K线推送回调
- [获取复权因子](get-rehab.md) - 复权数据处理