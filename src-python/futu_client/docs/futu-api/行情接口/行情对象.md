# 行情对象

行情对象是获取市场数据的核心接口，通过创建行情上下文来订阅和获取各种行情数据。

## OpenQuoteContext

### 基本用法

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 使用行情接口
ret, data = quote_ctx.get_market_snapshot(['HK.00700'])

# 关闭连接
quote_ctx.close()
```

### 构造参数

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| host | str | OpenD的IP地址 | '127.0.0.1' |
| port | int | OpenD的端口号 | 11111 |
| is_encrypt | bool | 是否启用加密 | None |
| is_async_connect | bool | 是否异步连接 | False |

### 连接管理

#### start()
启动异步接收推送数据的线程，用于异步接收服务器的推送数据。

```python
quote_ctx.start()
```

#### stop()
停止异步接收推送数据的线程。

```python
quote_ctx.stop()
```

#### close()
关闭上下文连接，释放资源。

```python
quote_ctx.close()
```

### 订阅管理

#### subscribe(code_list, subtype_list, subscribe_push=True, is_first_push=True, subscribe_type=SubType.QUOTE, is_req_after_sub=True)

订阅获取实时数据，指定股票代码和订阅的数据类型即可。

**参数：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code_list | list | 股票代码列表，例如['HK.00700', 'HK.00001'] |
| subtype_list | list | 订阅数据类型列表，见SubType |
| subscribe_push | bool | 是否订阅该数据类型的推送，默认True |
| is_first_push | bool | 订阅成功后是否立即推送一次数据，默认True |
| subscribe_type | SubType | 订阅类型，默认SubType.QUOTE |
| is_req_after_sub | bool | 订阅之后是否立即拉取一次数据，默认True |

**返回：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | str | 如果失败，返回错误描述 |

**示例：**

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅港股腾讯的报价和K线
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE, ft.SubType.K_1M])
if ret != ft.RET_OK:
    print('订阅失败: ', err_message)
else:
    print('订阅成功')

quote_ctx.close()
```

#### unsubscribe(code_list, subtype_list, unsubscribe_all=False)

取消订阅。

**参数：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code_list | list | 股票代码列表 |
| subtype_list | list | 取消订阅的数据类型列表 |
| unsubscribe_all | bool | 是否取消所有订阅，默认False |

**示例：**

```python
# 取消订阅
ret, err_message = quote_ctx.unsubscribe(['HK.00700'], [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('取消订阅失败: ', err_message)
```

#### unsubscribe_all()

取消所有订阅。

```python
ret, err_message = quote_ctx.unsubscribe_all()
if ret != ft.RET_OK:
    print('取消全部订阅失败: ', err_message)
```

#### query_subscription(is_all_conn=True)

查询已订阅的实时数据。

**参数：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| is_all_conn | bool | 是否返回所有连接的订阅状态，默认True |

**返回：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | DataFrame | 订阅信息，失败时返回错误描述 |

**DataFrame数据结构：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| sub_type | str | 订阅类型 |
| is_push | bool | 是否推送 |

### 回调设置

#### set_handler(handler)

设置实时数据回调处理对象。

**参数：**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| handler | HandlerBase | 回调处理对象 |

**示例：**

```python
import futu as ft

class MyQuoteHandler(ft.StockQuoteHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super(MyQuoteHandler, self).on_recv_rsp(rsp_pb)
        if ret_code != ft.RET_OK:
            print("错误: ", data)
            return ft.RET_ERROR, data
        
        print("收到报价推送: ", data)
        return ft.RET_OK, data

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
handler = MyQuoteHandler()
quote_ctx.set_handler(handler)

# 订阅数据后会自动推送到handler
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
```

### 数据类型定义

#### SubType（订阅类型）

| 值 | 说明 |
|----|------|
| QUOTE | 报价 |
| ORDER_BOOK | 摆盘 |
| TICKER | 逐笔 |
| K_DAY | 日K线 |
| K_1M | 1分钟K线 |
| K_3M | 3分钟K线 |
| K_5M | 5分钟K线 |
| K_15M | 15分钟K线 |
| K_30M | 30分钟K线 |
| K_60M | 60分钟K线 |
| K_WEEK | 周K线 |
| K_MONTH | 月K线 |
| RT_DATA | 分时数据 |
| BROKER | 经纪队列 |

### 注意事项

1. **连接管理**：使用完毕后务必调用`close()`关闭连接
2. **订阅限制**：免费用户有订阅数量限制
3. **推送延迟**：部分数据可能有延迟，实时性取决于权限
4. **错误处理**：建议对所有API调用进行错误检查

### 常见问题

**Q: 订阅失败怎么办？**
A: 检查股票代码格式是否正确，确认是否有相应的行情权限。

**Q: 推送数据收不到？**
A: 确保已设置回调处理器，并且OpenD正常运行。

**Q: 如何提高订阅效率？**
A: 一次性订阅多只股票，避免频繁的订阅/取消订阅操作。