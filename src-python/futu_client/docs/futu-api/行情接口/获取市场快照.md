# 获取市场快照

获取指定股票的详细市场快照数据，包括买卖盘、最新成交等信息，无需订阅即可获取。

## get_market_snapshot - 获取市场快照

### 接口原型

```python
get_market_snapshot(code_list)
```

### 功能说明

获取指定股票的市场快照数据，包括基本报价、买卖盘、最新成交信息。此接口无需订阅，但有调用频率限制。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code_list | list | 是 | 股票代码列表，例如['HK.00700', 'US.AAPL'] |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 快照数据DataFrame，失败时返回错误描述 |

### DataFrame字段说明

#### 基础信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| update_time | str | 更新时间 |
| last_price | float | 最新价 |
| open_price | float | 开盘价 |
| high_price | float | 最高价 |
| low_price | float | 最低价 |
| prev_close_price | float | 昨收价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| turnover_rate | float | 换手率 |
| suspension | bool | 是否停牌 |

#### 买卖盘信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| bid_price_1 | float | 买一价 |
| bid_volume_1 | int | 买一量 |
| ask_price_1 | float | 卖一价 |
| ask_volume_1 | int | 卖一量 |
| ... | ... | 最多支持10档买卖盘 |

#### 最新成交
| 字段名 | 类型 | 说明 |
|--------|------|------|
| last_deal_price | float | 最新成交价 |
| last_deal_volume | int | 最新成交量 |
| last_deal_time | str | 最新成交时间 |
| last_deal_direction | str | 最新成交方向 |

### 代码示例

#### 基本用法

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取单只股票快照
    ret, data = quote_ctx.get_market_snapshot(['HK.00700'])
    
    if ret == ft.RET_OK:
        snapshot = data.iloc[0]
        print("腾讯(00700)市场快照:")
        print(f"最新价: {snapshot['last_price']}")
        print(f"涨跌: {snapshot['last_price'] - snapshot['prev_close_price']:+.2f}")
        print(f"涨跌幅: {(snapshot['last_price'] - snapshot['prev_close_price']) / snapshot['prev_close_price'] * 100:+.2f}%")
        print(f"成交量: {snapshot['volume']:,}")
        print(f"成交额: {snapshot['turnover']:,.0f}")
        print(f"更新时间: {snapshot['update_time']}")
    else:
        print('获取快照失败:', data)
        
finally:
    quote_ctx.close()
```

#### 批量获取多只股票

```python
import futu as ft
import pandas as pd

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 港股主要股票列表
    hk_stocks = ['HK.00700', 'HK.00001', 'HK.00005', 'HK.00941', 'HK.03690']
    
    ret, data = quote_ctx.get_market_snapshot(hk_stocks)
    
    if ret == ft.RET_OK:
        print("港股主要股票快照:")
        print("=" * 80)
        
        # 计算涨跌幅
        data['change'] = data['last_price'] - data['prev_close_price']
        data['change_rate'] = data['change'] / data['prev_close_price'] * 100
        
        # 显示关键信息
        display_data = data[['code', 'last_price', 'change', 'change_rate', 'volume', 'turnover']]
        
        for _, row in display_data.iterrows():
            print(f"{row['code']:<12} {row['last_price']:>8.2f} "
                  f"{row['change']:>+6.2f} ({row['change_rate']:>+5.1f}%) "
                  f"量:{row['volume']:>10,} 额:{row['turnover']:>12,.0f}")
    else:
        print('获取快照失败:', data)
        
finally:
    quote_ctx.close()
```

#### 买卖盘分析

```python
import futu as ft

def analyze_order_book(quote_ctx, code):
    """分析买卖盘数据"""
    ret, data = quote_ctx.get_market_snapshot([code])
    
    if ret != ft.RET_OK:
        print(f"获取{code}快照失败:", data)
        return
    
    snapshot = data.iloc[0]
    
    print(f"\n{code} 买卖盘分析:")
    print("=" * 50)
    
    # 显示买卖盘
    print("档位    买盘                 卖盘")
    print("-" * 50)
    
    for i in range(1, 6):  # 显示前5档
        bid_price_col = f'bid_price_{i}'
        bid_volume_col = f'bid_volume_{i}'
        ask_price_col = f'ask_price_{i}'
        ask_volume_col = f'ask_volume_{i}'
        
        if bid_price_col in snapshot and ask_price_col in snapshot:
            bid_price = snapshot[bid_price_col] if pd.notna(snapshot[bid_price_col]) else 0
            bid_volume = snapshot[bid_volume_col] if pd.notna(snapshot[bid_volume_col]) else 0
            ask_price = snapshot[ask_price_col] if pd.notna(snapshot[ask_price_col]) else 0
            ask_volume = snapshot[ask_volume_col] if pd.notna(snapshot[ask_volume_col]) else 0
            
            bid_info = f"{bid_price:.2f}({bid_volume:,})" if bid_price > 0 else "-"
            ask_info = f"{ask_price:.2f}({ask_volume:,})" if ask_price > 0 else "-"
            
            print(f"{i:>2}     {bid_info:<20} {ask_info}")
    
    # 计算买卖盘比率
    total_bid_volume = sum([snapshot.get(f'bid_volume_{i}', 0) or 0 for i in range(1, 11)])
    total_ask_volume = sum([snapshot.get(f'ask_volume_{i}', 0) or 0 for i in range(1, 11)])
    
    if total_bid_volume + total_ask_volume > 0:
        bid_ratio = total_bid_volume / (total_bid_volume + total_ask_volume) * 100
        print(f"\n买卖盘比率: 买盘{bid_ratio:.1f}% vs 卖盘{100-bid_ratio:.1f}%")
    
    # 买卖价差
    bid1 = snapshot.get('bid_price_1', 0) or 0
    ask1 = snapshot.get('ask_price_1', 0) or 0
    if bid1 > 0 and ask1 > 0:
        spread = ask1 - bid1
        spread_pct = spread / ((bid1 + ask1) / 2) * 100
        print(f"买卖价差: {spread:.2f} ({spread_pct:.2f}%)")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    analyze_order_book(quote_ctx, 'HK.00700')
    analyze_order_book(quote_ctx, 'US.AAPL')
finally:
    quote_ctx.close()
```

#### 市场概览分析

```python
import futu as ft
import pandas as pd

def market_overview_analysis(quote_ctx, stock_list):
    """市场概览分析"""
    ret, data = quote_ctx.get_market_snapshot(stock_list)
    
    if ret != ft.RET_OK:
        print("获取市场快照失败:", data)
        return
    
    # 计算涨跌幅
    data['change_rate'] = (data['last_price'] - data['prev_close_price']) / data['prev_close_price'] * 100
    
    print("=== 市场概览分析 ===\n")
    
    # 1. 涨跌统计
    rising_stocks = data[data['change_rate'] > 0]
    falling_stocks = data[data['change_rate'] < 0]
    flat_stocks = data[data['change_rate'] == 0]
    
    print("1. 涨跌统计:")
    print(f"   上涨: {len(rising_stocks)} 只 ({len(rising_stocks)/len(data)*100:.1f}%)")
    print(f"   下跌: {len(falling_stocks)} 只 ({len(falling_stocks)/len(data)*100:.1f}%)")
    print(f"   平盘: {len(flat_stocks)} 只 ({len(flat_stocks)/len(data)*100:.1f}%)")
    
    # 2. 涨跌幅分布
    print(f"\n2. 涨跌幅分布:")
    print(f"   涨幅超过5%: {len(data[data['change_rate'] > 5])} 只")
    print(f"   涨幅2%-5%: {len(data[(data['change_rate'] > 2) & (data['change_rate'] <= 5)])} 只")
    print(f"   变动±2%内: {len(data[abs(data['change_rate']) <= 2])} 只")
    print(f"   跌幅2%-5%: {len(data[(data['change_rate'] < -2) & (data['change_rate'] >= -5)])} 只")
    print(f"   跌幅超过5%: {len(data[data['change_rate'] < -5])} 只")
    
    # 3. 成交活跃度
    print(f"\n3. 成交活跃度:")
    avg_turnover = data['turnover'].mean()
    high_volume_stocks = data[data['turnover'] > avg_turnover * 2]
    print(f"   平均成交额: {avg_turnover:,.0f}")
    print(f"   高成交额股票(>2倍均值): {len(high_volume_stocks)} 只")
    
    # 4. 表现最佳/最差
    if not data.empty:
        best_performer = data.loc[data['change_rate'].idxmax()]
        worst_performer = data.loc[data['change_rate'].idxmin()]
        
        print(f"\n4. 表现极值:")
        print(f"   最佳表现: {best_performer['code']} {best_performer['change_rate']:+.2f}%")
        print(f"   最差表现: {worst_performer['code']} {worst_performer['change_rate']:+.2f}%")
    
    # 5. 停牌统计
    suspended_stocks = data[data['suspension'] == True]
    if not suspended_stocks.empty:
        print(f"\n5. 停牌股票: {len(suspended_stocks)} 只")
        for _, stock in suspended_stocks.iterrows():
            print(f"   {stock['code']}")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 港股恒生指数成分股（部分）
    hsi_stocks = [
        'HK.00700', 'HK.00001', 'HK.00005', 'HK.00011', 'HK.00012',
        'HK.00016', 'HK.00017', 'HK.00019', 'HK.00027', 'HK.00066',
        'HK.00083', 'HK.00101', 'HK.00151', 'HK.00175', 'HK.00267',
        'HK.00288', 'HK.00388', 'HK.00669', 'HK.00688', 'HK.00941',
        'HK.01038', 'HK.01299', 'HK.01398', 'HK.01810', 'HK.02318'
    ]
    
    market_overview_analysis(quote_ctx, hsi_stocks)
    
finally:
    quote_ctx.close()
```

#### 实时监控多只股票

```python
import futu as ft
import time
from datetime import datetime

class MarketMonitor:
    def __init__(self, quote_ctx, watch_list):
        self.quote_ctx = quote_ctx
        self.watch_list = watch_list
        self.previous_data = {}
        self.alerts = []
    
    def add_alert(self, code, condition, threshold):
        """添加监控预警"""
        self.alerts.append({
            'code': code,
            'condition': condition,  # 'price_up', 'price_down', 'volume_surge'
            'threshold': threshold
        })
    
    def check_alerts(self, current_data):
        """检查预警条件"""
        triggered_alerts = []
        
        for alert in self.alerts:
            code = alert['code']
            condition = alert['condition']
            threshold = alert['threshold']
            
            # 找到对应股票数据
            stock_data = current_data[current_data['code'] == code]
            if stock_data.empty:
                continue
            
            stock = stock_data.iloc[0]
            
            if condition == 'price_up':
                change_rate = (stock['last_price'] - stock['prev_close_price']) / stock['prev_close_price'] * 100
                if change_rate >= threshold:
                    triggered_alerts.append(f"{code} 涨幅达到 {change_rate:.2f}% (≥{threshold}%)")
            
            elif condition == 'price_down':
                change_rate = (stock['last_price'] - stock['prev_close_price']) / stock['prev_close_price'] * 100
                if change_rate <= -threshold:
                    triggered_alerts.append(f"{code} 跌幅达到 {change_rate:.2f}% (≤-{threshold}%)")
            
            elif condition == 'volume_surge':
                if code in self.previous_data:
                    prev_volume = self.previous_data[code].get('volume', 0)
                    if prev_volume > 0:
                        volume_change = (stock['volume'] - prev_volume) / prev_volume * 100
                        if volume_change >= threshold:
                            triggered_alerts.append(f"{code} 成交量异动 {volume_change:.1f}% (≥{threshold}%)")
        
        return triggered_alerts
    
    def monitor(self, duration_minutes=60, check_interval=30):
        """开始监控"""
        print(f"开始监控 {len(self.watch_list)} 只股票，时长 {duration_minutes} 分钟")
        print("监控股票:", ', '.join(self.watch_list))
        print("预警条件:", len(self.alerts), "个")
        print("-" * 60)
        
        start_time = time.time()
        end_time = start_time + duration_minutes * 60
        
        while time.time() < end_time:
            try:
                ret, data = self.quote_ctx.get_market_snapshot(self.watch_list)
                
                if ret == ft.RET_OK:
                    current_time = datetime.now().strftime('%H:%M:%S')
                    
                    # 检查预警
                    alerts = self.check_alerts(data)
                    if alerts:
                        print(f"\n[{current_time}] 🚨 预警触发:")
                        for alert in alerts:
                            print(f"  {alert}")
                    
                    # 显示监控状态
                    avg_change = ((data['last_price'] - data['prev_close_price']) / data['prev_close_price'] * 100).mean()
                    print(f"[{current_time}] 平均涨跌幅: {avg_change:+.2f}%")
                    
                    # 保存当前数据用于下次比较
                    for _, row in data.iterrows():
                        self.previous_data[row['code']] = row.to_dict()
                
                else:
                    print(f"获取快照失败: {data}")
                
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\n监控已手动停止")
                break
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(check_interval)
        
        print("\n监控结束")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 创建监控器
    monitor = MarketMonitor(quote_ctx, ['HK.00700', 'HK.00001', 'HK.00005'])
    
    # 添加预警条件
    monitor.add_alert('HK.00700', 'price_up', 3.0)    # 腾讯涨幅≥3%
    monitor.add_alert('HK.00700', 'price_down', 3.0)  # 腾讯跌幅≥3%
    monitor.add_alert('HK.00001', 'volume_surge', 50.0)  # 长和成交量异动≥50%
    
    # 开始监控（监控10分钟，每30秒检查一次）
    monitor.monitor(duration_minutes=10, check_interval=30)
    
finally:
    quote_ctx.close()
```

### 使用限制

#### 调用频率限制
- **最大频率**：30次/秒
- **单次最大股票数**：200只
- **建议间隔**：建议每次调用间隔≥1秒

#### 数据时效性
- **实时性**：数据延迟取决于行情权限
- **免费用户**：15分钟延迟
- **付费用户**：实时数据

### 注意事项

1. **无需订阅**：此接口无需事先订阅股票
2. **频率控制**：注意调用频率限制，避免被限流
3. **数据完整性**：停牌股票部分字段可能为空
4. **时区处理**：注意不同市场的时区差异
5. **错误处理**：对网络异常和数据异常进行适当处理

### 错误处理

```python
import futu as ft
import time

def robust_get_snapshot(quote_ctx, code_list, max_retries=3):
    """带重试的快照获取"""
    for attempt in range(max_retries):
        try:
            ret, data = quote_ctx.get_market_snapshot(code_list)
            
            if ret == ft.RET_OK:
                return True, data
            else:
                print(f"第{attempt + 1}次尝试失败: {data}")
                if attempt < max_retries - 1:
                    time.sleep(1)  # 等待1秒后重试
        
        except Exception as e:
            print(f"第{attempt + 1}次尝试异常: {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
    
    return False, "获取快照失败，已达到最大重试次数"

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    success, result = robust_get_snapshot(quote_ctx, ['HK.00700'])
    
    if success:
        print("获取快照成功")
        print(result[['code', 'last_price', 'volume']])
    else:
        print("获取失败:", result)

finally:
    quote_ctx.close()
```

### 相关接口

- [获取股票报价](get-stock-quote.md) - 获取实时报价（需订阅）
- [订阅反订阅](sub.md) - 订阅数据推送
- [获取摆盘数据](get-order-book.md) - 获取详细买卖盘