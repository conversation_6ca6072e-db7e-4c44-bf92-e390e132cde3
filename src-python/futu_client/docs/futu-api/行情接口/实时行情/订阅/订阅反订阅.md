# 订阅反订阅

> 对应官方文档: `/futu-api-doc/quote/sub.html`

## 接口介绍

用于订阅或反订阅指定股票的行情数据，支持多种数据类型的订阅。

## 函数原型

```python
subscribe(code_list, subtype_list, subscribe_push=True, is_first_push=True, is_subscribe=True)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code_list | list | 股票代码列表 |
| subtype_list | list | 订阅类型列表 |
| subscribe_push | bool | 是否订阅推送 |
| is_first_push | bool | 是否首次推送 |
| is_subscribe | bool | True订阅，False反订阅 |

## 订阅类型

| 类型 | 说明 |
|------|------|
| SubType.QUOTE | 基本报价 |
| SubType.ORDER_BOOK | 买卖盘 |
| SubType.TICKER | 逐笔成交 |
| SubType.RT_DATA | 分时数据 |
| SubType.K_1M | 1分钟K线 |
| SubType.K_5M | 5分钟K线 |
| SubType.K_15M | 15分钟K线 |
| SubType.K_30M | 30分钟K线 |
| SubType.K_60M | 60分钟K线 |
| SubType.K_DAY | 日K线 |
| SubType.K_WEEK | 周K线 |
| SubType.K_MON | 月K线 |
| SubType.BROKER | 经纪队列 |

## 订阅示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅腾讯的基本报价和买卖盘
code_list = ['HK.00700']
subtype_list = [SubType.QUOTE, SubType.ORDER_BOOK]

ret, data = quote_ctx.subscribe(code_list, subtype_list)

if ret == RET_OK:
    print("订阅成功")
    print(data)
else:
    print("订阅失败:", data)

quote_ctx.close()
```

## 反订阅示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 反订阅腾讯的买卖盘数据
code_list = ['HK.00700']
subtype_list = [SubType.ORDER_BOOK]

ret, data = quote_ctx.subscribe(code_list, subtype_list, is_subscribe=False)

if ret == RET_OK:
    print("反订阅成功")
    print(data)
else:
    print("反订阅失败:", data)

quote_ctx.close()
```

## 批量订阅示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 批量订阅多只股票的多种数据类型
code_list = ['HK.00700', 'HK.00981', 'HK.00005']
subtype_list = [SubType.QUOTE, SubType.K_1M, SubType.RT_DATA]

ret, data = quote_ctx.subscribe(code_list, subtype_list)

if ret == RET_OK:
    print(f"成功订阅 {len(code_list)} 只股票的 {len(subtype_list)} 种数据类型")
    
    # 查看订阅详情
    for _, row in data.iterrows():
        print(f"股票: {row['code']}")
        print(f"数据类型: {row['subtype']}")
        print(f"状态: {row['subscribe_status']}")
        print("-" * 30)
else:
    print("订阅失败:", data)

quote_ctx.close()
```

## 注意事项

1. 订阅数量有限制，需要合理管理
2. 推送数据需要设置相应的回调函数
3. 长时间不使用的订阅应及时清理
4. 不同数据类型的推送频率不同
5. 订阅失败可能是权限或网络问题

## 应用场景

1. **实时监控**: 订阅关注股票的实时数据
2. **策略开发**: 为量化策略提供数据源
3. **风险管理**: 监控持仓股票的价格变化
4. **研究分析**: 收集历史和实时数据进行分析