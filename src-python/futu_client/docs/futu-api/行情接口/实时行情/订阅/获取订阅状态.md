# 获取订阅状态

> 对应官方文档: `/futu-api-doc/quote/query-subscription.html`

## 接口介绍

查询当前所有的订阅状态，包括已订阅的股票代码和数据类型。

## 函数原型

```python
query_subscription(is_all_conn=True)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| is_all_conn | bool | 是否返回所有连接的订阅状态 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 查询当前所有订阅
ret, data = quote_ctx.query_subscription()

if ret == RET_OK:
    print("当前订阅状态:")
    print(data)
    
    if len(data) > 0:
        # 按股票分组显示
        grouped = data.groupby('code')
        for code, group in grouped:
            print(f"\n股票: {code}")
            for _, row in group.iterrows():
                print(f"  {row['subtype']}: {row['subscribe_status']}")
    
    print(f"\n总订阅数: {len(data)}")
else:
    print("查询失败:", data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| subtype | str | 订阅类型 |
| subscribe_status | str | 订阅状态 |

## 订阅状态统计示例

```python
from futu import *
import pandas as pd

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

def analyze_subscriptions():
    ret, data = quote_ctx.query_subscription()
    
    if ret == RET_OK:
        print("订阅状态分析:")
        print("=" * 40)
        
        if len(data) == 0:
            print("当前无任何订阅")
            return
        
        # 统计不同类型的订阅数量
        type_counts = data['subtype'].value_counts()
        
        print("订阅类型统计:")
        for subtype, count in type_counts.items():
            print(f"  {subtype}: {count}")
        
        print(f"\n总订阅数: {len(data)}")
        
        # 按股票统计
        stock_counts = data['code'].value_counts()
        print(f"已订阅股票数: {len(stock_counts)}")
        
        # 显示订阅最多的股票
        if len(stock_counts) > 0:
            top_stocks = stock_counts.head(5)
            print("\n订阅最多的股票:")
            for code, count in top_stocks.items():
                print(f"  {code}: {count}种数据类型")
        
        # 检查订阅限制
        if len(data) > 80:  # 假设限制为100
            print("\n⚠️ 订阅数量接近限制，建议清理不必要的订阅")

analyze_subscriptions()
quote_ctx.close()
```

## 订阅管理示例

```python
from futu import *

class SubscriptionManager:
    def __init__(self, host='127.0.0.1', port=11111):
        self.quote_ctx = OpenQuoteContext(host=host, port=port)
    
    def list_subscriptions(self):
        """显示所有订阅"""
        ret, data = self.quote_ctx.query_subscription()
        
        if ret == RET_OK:
            print("当前订阅列表:")
            print("-" * 50)
            
            if len(data) == 0:
                print("暂无订阅")
                return
            
            # 按股票分组显示
            grouped = data.groupby('code')
            for code, group in grouped:
                subtypes = list(group['subtype'])
                print(f"{code}: {', '.join(subtypes)}")
        else:
            print("查询订阅失败:", data)
    
    def check_subscription(self, code, subtype):
        """检查特定订阅是否存在"""
        ret, data = self.quote_ctx.query_subscription()
        
        if ret == RET_OK:
            matches = data[(data['code'] == code) & (data['subtype'] == subtype)]
            return len(matches) > 0
        return False
    
    def cleanup_subscriptions(self, keep_codes=None):
        """清理订阅，保留指定股票"""
        if keep_codes is None:
            keep_codes = []
        
        ret, data = self.quote_ctx.query_subscription()
        
        if ret == RET_OK:
            to_remove = data[~data['code'].isin(keep_codes)]
            
            if len(to_remove) > 0:
                print(f"准备清理 {len(to_remove)} 个订阅...")
                
                # 按股票分组进行反订阅
                grouped = to_remove.groupby('code')
                for code, group in grouped:
                    subtypes = [getattr(SubType, st) for st in group['subtype'] if hasattr(SubType, st)]
                    
                    if subtypes:
                        ret, _ = self.quote_ctx.subscribe([code], subtypes, is_subscribe=False)
                        if ret == RET_OK:
                            print(f"✅ 已清理 {code} 的订阅")
                        else:
                            print(f"❌ 清理 {code} 失败")
            else:
                print("无需清理订阅")
    
    def close(self):
        """关闭连接"""
        self.quote_ctx.close()

# 使用示例
manager = SubscriptionManager()

# 显示当前订阅
manager.list_subscriptions()

# 检查特定订阅
if manager.check_subscription('HK.00700', 'QUOTE'):
    print("腾讯的基本报价已订阅")

# 清理订阅，只保留指定股票
manager.cleanup_subscriptions(['HK.00700', 'HK.00981'])

manager.close()
```

## 应用场景

1. **订阅管理**: 定期检查和清理订阅状态
2. **系统监控**: 监控订阅数量避免超限
3. **策略调试**: 确认策略所需数据已正确订阅
4. **资源优化**: 清理不必要的订阅释放资源

## 注意事项

1. 订阅状态实时更新，建议定期查询
2. 订阅数量有限制，需要合理规划
3. 查询结果包含所有连接的订阅信息
4. 清理订阅时要确保不影响正在运行的策略