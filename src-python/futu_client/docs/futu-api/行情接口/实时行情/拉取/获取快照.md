# 获取快照

> 对应官方文档: `/futu-api-doc/quote/get-market-snapshot.html`

## 接口介绍

获取市场快照数据，包括股票的实时价格、成交量、市值等关键指标。

## 函数原型

```python
get_market_snapshot(code_list)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code_list | list | 股票代码列表，最多200个 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取多只股票的市场快照
code_list = ['HK.00700', 'HK.00981', 'HK.00005']
ret, data = quote_ctx.get_market_snapshot(code_list)

if ret == RET_OK:
    print(data)
    for index, row in data.iterrows():
        print(f"股票: {row['code']} - {row['stock_name']}")
        print(f"最新价: {row['last_price']}")
        print(f"涨跌幅: {row['change_rate']:.2f}%")
        print(f"成交量: {row['volume']:,}")
        print(f"市值: {row['market_val']:,}")
        print(f"PE: {row['pe_ratio']:.2f}")
        print("-" * 40)
else:
    print('获取市场快照失败:', data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| last_price | float | 最新价 |
| open_price | float | 开盘价 |
| high_price | float | 最高价 |
| low_price | float | 最低价 |
| prev_close_price | float | 昨收价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| change_rate | float | 涨跌幅 |
| change_val | float | 涨跌额 |
| market_val | float | 市值 |
| pe_ratio | float | 市盈率 |
| pb_ratio | float | 市净率 |
| turnover_rate | float | 换手率 |

## 批量分析示例

```python
from futu import *
import pandas as pd

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取恒生指数成分股快照
hsi_codes = ['HK.00700', 'HK.00981', 'HK.00005', 'HK.00388', 'HK.00175']
ret, data = quote_ctx.get_market_snapshot(hsi_codes)

if ret == RET_OK:
    # 排序分析
    print("按涨跌幅排序:")
    sorted_data = data.sort_values('change_rate', ascending=False)
    for _, row in sorted_data.iterrows():
        print(f"{row['stock_name']}: {row['change_rate']:.2f}%")
    
    # 统计分析
    avg_pe = data['pe_ratio'].mean()
    avg_turnover = data['turnover_rate'].mean()
    
    print(f"\n平均PE: {avg_pe:.2f}")
    print(f"平均换手率: {avg_turnover:.2f}%")
    
    # 筛选活跃股票
    active_stocks = data[data['turnover_rate'] > 1.0]
    print(f"\n活跃股票({len(active_stocks)}只):")
    for _, row in active_stocks.iterrows():
        print(f"{row['stock_name']}: 换手率{row['turnover_rate']:.2f}%")

quote_ctx.close()
```

## 实时监控示例

```python
from futu import *
import time

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

watch_list = ['HK.00700', 'HK.00981', 'HK.00005']

def monitor_stocks():
    ret, data = quote_ctx.get_market_snapshot(watch_list)
    if ret == RET_OK:
        print(f"\n{time.strftime('%H:%M:%S')} 市场快照:")
        for _, row in data.iterrows():
            color = "🔴" if row['change_rate'] > 0 else "🟢" if row['change_rate'] < 0 else "⚪"
            print(f"{color} {row['stock_name']}: {row['last_price']:.2f} ({row['change_rate']:+.2f}%)")

# 每30秒更新一次
try:
    while True:
        monitor_stocks()
        time.sleep(30)
except KeyboardInterrupt:
    print("\n监控已停止")
    quote_ctx.close()
```

## 应用场景

1. **实时监控**: 监控多只股票的实时表现
2. **快速筛选**: 基于价格、估值等指标筛选股票
3. **市场分析**: 分析市场整体表现和热点
4. **投资决策**: 快速了解股票基本面指标

## 注意事项

1. 单次最多获取200只股票数据
2. 数据为实时快照，非历史数据
3. 需要相应的行情权限
4. 建议合理控制请求频率