# 获取实时经纪队列

> 对应官方文档: `/futu-api-doc/quote/get-broker.html`

## 接口介绍

获取股票买卖盘经纪队列数据，显示各经纪商的委托情况。

## 函数原型

```python
get_broker(code)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯经纪队列
ret, ask_data, bid_data = quote_ctx.get_broker('HK.00700')

if ret == RET_OK:
    print("卖盘经纪队列:")
    print(ask_data)
    
    print("\n买盘经纪队列:")
    print(bid_data)
    
    # 分析经纪分布
    if not ask_data.empty:
        print("\n卖盘经纪分析:")
        for index, row in ask_data.iterrows():
            print(f"档位{row['order_level']}: {row['broker_name']} - {row['broker_id']}")
    
    if not bid_data.empty:
        print("\n买盘经纪分析:")
        for index, row in bid_data.iterrows():
            print(f"档位{row['order_level']}: {row['broker_name']} - {row['broker_id']}")
            
else:
    print('获取经纪队列失败:', ask_data)

quote_ctx.close()
```

## 应用场景

1. **机构行为分析**: 跟踪大型机构的交易行为
2. **市场结构研究**: 了解市场参与者构成
3. **交易策略**: 基于经纪队列制定交易策略
4. **风险管理**: 识别大额委托和潜在风险

## 注意事项

1. 经纪队列数据仅适用于支持的市场
2. 数据更新频率较高，需要合理处理
3. 经纪商信息可能因市场而异
4. 需要相应的行情权限和经纪队列权限