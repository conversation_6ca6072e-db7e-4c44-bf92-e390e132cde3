# 获取实时逐笔

> 对应官方文档: `/futu-api-doc/quote/get-ticker.html`

## 接口介绍

获取股票实时逐笔成交数据，包括每笔交易的详细信息。

## 函数原型

```python
get_deal(code, num)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码 |
| num | int | 获取数量，最大200 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯最新100笔成交
ret, data = quote_ctx.get_deal('HK.00700', 100)

if ret == RET_OK:
    print(data)
    for index, row in data.iterrows():
        print(f"时间: {row['time']}")
        print(f"价格: {row['price']}")
        print(f"数量: {row['volume']}")
        print(f"金额: {row['turnover']}")
        
        # 交易方向
        direction_map = {1: "买盘", -1: "卖盘", 0: "中性"}
        direction = direction_map.get(row['change_type'], "未知")
        print(f"方向: {direction}")
        print("-" * 30)
else:
    print('获取逐笔成交失败:', data)

quote_ctx.close()
```

## 应用场景

1. **市场微观结构分析**: 研究市场交易行为
2. **资金流向分析**: 计算买卖盘资金流动
3. **交易策略**: 基于逐笔数据制定交易策略
4. **市场情绪判断**: 通过买卖盘比例判断市场情绪

## 注意事项

1. 单次最多获取200笔成交数据
2. 数据按时间倒序排列（最新在前）
3. 需要相应的行情权限
4. 交易方向判断基于价格变化