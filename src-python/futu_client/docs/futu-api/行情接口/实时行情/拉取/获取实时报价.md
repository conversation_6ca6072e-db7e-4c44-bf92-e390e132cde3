# 获取实时报价

> 对应官方文档: `/futu-api-doc/quote/get-stock-quote.html`

## 接口介绍

获取股票实时报价数据，包括最新价格、涨跌幅、成交量等实时信息。

## 函数原型

```python
get_stock_quote(code_list)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code_list | list | 股票代码列表，最多200个 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取实时报价
code_list = ['HK.00700', 'HK.00981', 'HK.00005']
ret, data = quote_ctx.get_stock_quote(code_list)

if ret == RET_OK:
    print("实时报价:")
    print(data)
    
    for index, row in data.iterrows():
        print(f"股票: {row['code']} - {row['stock_name']}")
        print(f"最新价: {row['last_price']}")
        print(f"涨跌幅: {row['change_rate']:.2f}%")
        print(f"涨跌额: {row['change_val']:+.3f}")
        print(f"成交量: {row['volume']:,}")
        print(f"成交额: {row['turnover']:,.0f}")
        print("-" * 40)
else:
    print('获取实时报价失败:', data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| last_price | float | 最新价 |
| open_price | float | 开盘价 |
| high_price | float | 最高价 |
| low_price | float | 最低价 |
| prev_close_price | float | 昨收价 |
| change_val | float | 涨跌额 |
| change_rate | float | 涨跌幅 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| lot_size | int | 每手股数 |
| stock_type | str | 股票类型 |
| list_time | str | 上市时间 |
| delisting | bool | 是否退市 |

## 批量监控示例

```python
from futu import *
import time

def monitor_stocks():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 监控的股票列表
    watch_list = ['HK.00700', 'HK.00981', 'HK.00005', 'HK.00388']
    
    try:
        while True:
            ret, data = quote_ctx.get_stock_quote(watch_list)
            
            if ret == RET_OK:
                print(f"\n{time.strftime('%H:%M:%S')} 实时行情:")
                print("-" * 60)
                
                for _, row in data.iterrows():
                    # 涨跌颜色显示
                    if row['change_rate'] > 0:
                        color = "🔴"
                    elif row['change_rate'] < 0:
                        color = "🟢"
                    else:
                        color = "⚪"
                    
                    print(f"{color} {row['stock_name']:<8} "
                          f"价格: {row['last_price']:>8.2f} "
                          f"涨跌: {row['change_rate']:>+6.2f}% "
                          f"成交: {row['volume']:>10,}")
                
                # 找出异动股票
                significant_moves = data[abs(data['change_rate']) > 3]
                if len(significant_moves) > 0:
                    print(f"\n⚠️ 异动提醒:")
                    for _, row in significant_moves.iterrows():
                        print(f"   {row['stock_name']}: {row['change_rate']:+.2f}%")
            
            time.sleep(10)  # 10秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    finally:
        quote_ctx.close()

# 运行监控
monitor_stocks()
```

## 价格预警示例

```python
from futu import *

class PriceAlert:
    def __init__(self, code, name, high_threshold, low_threshold):
        self.code = code
        self.name = name
        self.high_threshold = high_threshold
        self.low_threshold = low_threshold
        self.last_price = None
        
    def check_alert(self, current_price):
        alerts = []
        
        if current_price >= self.high_threshold:
            alerts.append(f"🔴 {self.name} 价格突破上限 {self.high_threshold}")
        
        if current_price <= self.low_threshold:
            alerts.append(f"🟢 {self.name} 价格跌破下限 {self.low_threshold}")
        
        # 检查急速变化
        if self.last_price:
            change_rate = (current_price - self.last_price) / self.last_price * 100
            if abs(change_rate) > 2:  # 2%以上变化
                direction = "急涨" if change_rate > 0 else "急跌"
                alerts.append(f"⚡ {self.name} {direction} {change_rate:+.2f}%")
        
        self.last_price = current_price
        return alerts

# 设置价格预警
alerts = [
    PriceAlert('HK.00700', '腾讯', 400, 350),
    PriceAlert('HK.00981', '中芯国际', 25, 20),
]

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    while True:
        code_list = [alert.code for alert in alerts]
        ret, data = quote_ctx.get_stock_quote(code_list)
        
        if ret == RET_OK:
            for _, row in data.iterrows():
                # 找到对应的预警设置
                alert = next((a for a in alerts if a.code == row['code']), None)
                if alert:
                    alert_msgs = alert.check_alert(row['last_price'])
                    for msg in alert_msgs:
                        print(f"{time.strftime('%H:%M:%S')} {msg}")
        
        time.sleep(5)
        
except KeyboardInterrupt:
    print("\n价格预警已停止")
finally:
    quote_ctx.close()
```

## 应用场景

1. **实时监控**: 监控关注股票的实时价格变化
2. **交易决策**: 基于实时数据做交易决策
3. **风险管理**: 实时监控持仓股票的价格波动
4. **量化交易**: 为量化策略提供实时数据源

## 注意事项

1. 单次最多获取200只股票数据
2. 实时数据需要相应的行情权限
3. 建议合理控制请求频率
4. 数据更新频率取决于市场活跃度