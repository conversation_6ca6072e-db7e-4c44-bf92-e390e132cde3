# 获取实时K线

> 对应官方文档: `/futu-api-doc/quote/get-kl.html`

## 接口介绍

获取股票实时K线数据，包括最新的未完成K线。

## 函数原型

```python
get_cur_kline(code, num, ktype=KLType.K_DAY, autype=AuType.QFQ, extended_time=False)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码 |
| num | int | 获取数量 |
| ktype | KLType | K线类型 |
| autype | AuType | 复权类型 |
| extended_time | bool | 是否包含盘前盘后 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯最新100根日K线
ret, data = quote_ctx.get_cur_kline('HK.00700', 100, KLType.K_DAY, AuType.QFQ)

if ret == RET_OK:
    print(data)
    # 最新的K线数据
    latest = data.iloc[-1]
    print(f"最新日期: {latest['time_key']}")
    print(f"收盘价: {latest['close']}")
    print(f"成交量: {latest['volume']}")
else:
    print('获取实时K线失败:', data)

quote_ctx.close()
```

## 实时K线特点

1. **最新数据**: 包含当前正在形成的K线
2. **实时更新**: 数据会随市场实时变化
3. **完整性**: 未完成的K线会持续更新直到周期结束
4. **权限要求**: 需要相应市场的实时行情权限

## 应用场景

1. **实时监控**: 监控股票最新价格变化
2. **技术分析**: 基于最新K线进行技术指标计算
3. **交易决策**: 结合实时K线判断买卖时机
4. **图表显示**: 实时更新K线图表

## 注意事项

1. 实时K线数据需要相应的行情权限
2. 最新的K线可能是未完成状态
3. 建议结合实时推送获取最新变化
4. 数据更新频率取决于市场活跃度