# 获取实时摆盘

> 对应官方文档: `/futu-api-doc/quote/get-order-book.html`

## 接口介绍

获取股票实时买卖盘数据，包括买卖档位的价格、数量和笔数信息。

## 函数原型

```python
get_order_book(code, num=10)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码 |
| num | int | 返回档位数量，默认10档 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯的买卖盘数据
ret, data = quote_ctx.get_order_book('HK.00700')

if ret == RET_OK:
    print("买卖盘数据:")
    print(data)
    
    # 分析买卖盘
    for index, row in data.iterrows():
        print(f"股票: {row['code']}")
        
        # 买盘分析
        if 'Bid' in row and row['Bid']:
            print("\n买盘前5档:")
            for i, bid in enumerate(row['Bid'][:5]):
                print(f"  {i+1}档: 价格={bid[0]:.3f}, 数量={bid[1]:,}, 笔数={bid[2]}")
        
        # 卖盘分析
        if 'Ask' in row and row['Ask']:
            print("\n卖盘前5档:")
            for i, ask in enumerate(row['Ask'][:5]):
                print(f"  {i+1}档: 价格={ask[0]:.3f}, 数量={ask[1]:,}, 笔数={ask[2]}")
        
        # 计算价差
        if 'Bid' in row and 'Ask' in row and row['Bid'] and row['Ask']:
            best_bid = row['Bid'][0][0]
            best_ask = row['Ask'][0][0]
            spread = best_ask - best_bid
            spread_rate = (spread / best_bid) * 100
            print(f"\n价差: {spread:.3f} ({spread_rate:.2f}%)")
        
        print("-" * 50)
else:
    print('获取买卖盘失败:', data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| Bid | list | 买盘数据列表 |
| Ask | list | 卖盘数据列表 |

### 买卖盘数据格式

每个档位包含三个元素的列表：
- `[价格, 数量, 笔数]`

## 市场深度分析示例

```python
from futu import *

def analyze_market_depth(code):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_order_book(code, num=10)
    
    if ret == RET_OK:
        row = data.iloc[0]
        print(f"市场深度分析 - {row['stock_name']} ({code})")
        print("=" * 50)
        
        bids = row['Bid']
        asks = row['Ask']
        
        if bids and asks:
            # 计算总买卖量
            total_bid_volume = sum(bid[1] for bid in bids)
            total_ask_volume = sum(ask[1] for ask in asks)
            
            print(f"总买盘量: {total_bid_volume:,}")
            print(f"总卖盘量: {total_ask_volume:,}")
            print(f"买卖比: {total_bid_volume/total_ask_volume:.2f}")
            
            # 分析价格分布
            best_bid = bids[0][0]
            best_ask = asks[0][0]
            
            print(f"\n最佳买价: {best_bid:.3f}")
            print(f"最佳卖价: {best_ask:.3f}")
            print(f"价差: {best_ask - best_bid:.3f}")
            
            # 计算各档位的累计量
            print(f"\n买盘累计量:")
            cumulative_bid = 0
            for i, bid in enumerate(bids[:5]):
                cumulative_bid += bid[1]
                print(f"  {i+1}档: {bid[0]:.3f} - 累计{cumulative_bid:,}")
            
            print(f"\n卖盘累计量:")
            cumulative_ask = 0
            for i, ask in enumerate(asks[:5]):
                cumulative_ask += ask[1]
                print(f"  {i+1}档: {ask[0]:.3f} - 累计{cumulative_ask:,}")
            
            # 分析市场偏向
            if total_bid_volume > total_ask_volume * 1.2:
                print(f"\n市场偏向: 买盘较强 📈")
            elif total_ask_volume > total_bid_volume * 1.2:
                print(f"\n市场偏向: 卖盘较强 📉")
            else:
                print(f"\n市场偏向: 买卖平衡 ⚖️")
    
    quote_ctx.close()

# 分析腾讯的市场深度
analyze_market_depth('HK.00700')
```

## 多股票对比示例

```python
from futu import *

def compare_order_books(code_list):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    print("买卖盘对比分析:")
    print("=" * 80)
    
    for code in code_list:
        ret, data = quote_ctx.get_order_book(code, num=5)
        
        if ret == RET_OK:
            row = data.iloc[0]
            bids = row['Bid']
            asks = row['Ask']
            
            if bids and asks:
                best_bid = bids[0][0]
                best_ask = asks[0][0]
                spread = best_ask - best_bid
                spread_rate = (spread / best_bid) * 100
                
                total_bid_volume = sum(bid[1] for bid in bids)
                total_ask_volume = sum(ask[1] for ask in asks)
                
                print(f"{row['stock_name']} ({code}):")
                print(f"  最佳买价: {best_bid:.3f}")
                print(f"  最佳卖价: {best_ask:.3f}")
                print(f"  价差: {spread:.3f} ({spread_rate:.2f}%)")
                print(f"  买盘量: {total_bid_volume:,}")
                print(f"  卖盘量: {total_ask_volume:,}")
                print(f"  买卖比: {total_bid_volume/total_ask_volume:.2f}")
                print("-" * 40)
    
    quote_ctx.close()

# 对比分析
compare_order_books(['HK.00700', 'HK.00981', 'HK.00005'])
```

## 实时监控示例

```python
from futu import *
import time

def monitor_order_book(code):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        while True:
            ret, data = quote_ctx.get_order_book(code, num=3)
            
            if ret == RET_OK:
                row = data.iloc[0]
                bids = row['Bid']
                asks = row['Ask']
                
                if bids and asks:
                    print(f"\n{time.strftime('%H:%M:%S')} - {row['stock_name']}")
                    print(f"{'卖盘':<15} {'价格':<10} {'买盘':<15}")
                    print("-" * 45)
                    
                    # 显示前3档
                    for i in range(min(3, len(asks), len(bids))):
                        ask_info = f"{asks[i][1]:,}@{asks[i][0]:.3f}"
                        bid_info = f"{bids[i][1]:,}@{bids[i][0]:.3f}"
                        print(f"{ask_info:<15} {'|':<10} {bid_info:<15}")
                    
                    # 计算价差
                    spread = asks[0][0] - bids[0][0]
                    print(f"\n价差: {spread:.3f}")
            
            time.sleep(3)  # 3秒更新一次
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    finally:
        quote_ctx.close()

# 监控腾讯买卖盘
monitor_order_book('HK.00700')
```

## 应用场景

1. **市场深度分析**: 分析市场流动性和价格支撑
2. **交易时机判断**: 基于买卖盘强度判断入场时机
3. **价差套利**: 寻找价差较大的套利机会
4. **风险管理**: 评估大额交易的市场冲击

## 注意事项

1. 数据为实时快照，变化较快
2. 需要相应的行情权限
3. 档位数量可能因股票而异
4. 建议合理控制请求频率