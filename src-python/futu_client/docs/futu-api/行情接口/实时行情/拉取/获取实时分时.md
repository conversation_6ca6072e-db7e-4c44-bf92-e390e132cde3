# 获取实时分时

> 对应官方文档: `/futu-api-doc/quote/get-rt.html`

## 接口介绍

获取股票实时分时数据，包括价格、成交量等分时信息。

## 函数原型

```python
get_rt_data(code)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| code | str | 股票代码 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取腾讯分时数据
ret, data = quote_ctx.get_rt_data('HK.00700')

if ret == RET_OK:
    print(data)
    for index, row in data.iterrows():
        if not row['is_blank']:  # 过滤空白点
            print(f"时间: {row['time']}")
            print(f"价格: {row['cur_price']}")
            print(f"均价: {row['avg_price']}")
            print(f"成交量: {row['volume']}")
            print(f"成交额: {row['turnover']}")
            print("-" * 30)
else:
    print('获取分时数据失败:', data)

quote_ctx.close()
```

## 应用场景

1. **分时图表**: 绘制股票当日价格走势
2. **实时监控**: 跟踪股票价格变化
3. **交易分析**: 分析当日交易模式
4. **量价关系**: 研究价格与成交量关系

## 注意事项

1. 分时数据仅包含当日数据
2. 空白点表示非交易时间
3. 数据更新频率为分钟级别
4. 需要相应的行情权限