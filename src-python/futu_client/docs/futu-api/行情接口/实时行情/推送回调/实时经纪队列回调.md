# 实时经纪队列回调

> 对应官方文档: `/futu-api-doc/quote/update-broker.html`

## 接口介绍

实时经纪队列回调用于接收股票买卖盘经纪队列数据的实时推送。当订阅的股票经纪队列发生变化时，会自动触发此回调函数。

## 回调机制

```python
from futu import *

class BrokerTest(BrokerHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, ask_content, bid_content = super(BrokerTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("BrokerTest: error, msg: %s" % ask_content)
            return RET_ERROR, ask_content, bid_content
        
        print("BrokerTest ask: ", ask_content)  # 卖盘经纪队列
        print("BrokerTest bid: ", bid_content)  # 买盘经纪队列
        return RET_OK, ask_content, bid_content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = BrokerTest()
quote_ctx.set_handler(handler)  # 设置实时经纪队列回调
```

## 数据结构

实时经纪队列推送包含买盘和卖盘两部分数据：

### 买盘/卖盘经纪数据

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| order_level | int | 档位 |
| broker_id | int | 经纪商ID |
| broker_name | str | 经纪商名称 |
| broker_pos | int | 经纪商在该档位的位置 |

## 使用示例

```python
from futu import *

class BrokerHandler(BrokerHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, ask_content, bid_content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            # 处理卖盘经纪队列
            if not ask_content.empty:
                print("卖盘经纪队列更新:")
                for _, row in ask_content.iterrows():
                    print(f"  档位{row['order_level']}: {row['broker_name']} (ID: {row['broker_id']})")
            
            # 处理买盘经纪队列
            if not bid_content.empty:
                print("买盘经纪队列更新:")
                for _, row in bid_content.iterrows():
                    print(f"  档位{row['order_level']}: {row['broker_name']} (ID: {row['broker_id']})")
            
            print("-" * 50)
        
        return ret_code, ask_content, bid_content

# 设置回调并订阅经纪队列
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(BrokerHandler())
quote_ctx.subscribe(['HK.00700'], [SubType.BROKER])
```

## 经纪队列分析示例

```python
from futu import *
import pandas as pd

class BrokerAnalyzer(BrokerHandlerBase):
    def __init__(self):
        super().__init__()
        self.broker_activity = {}  # 记录经纪商活跃度
    
    def on_recv_rsp(self, rsp_pb):
        ret_code, ask_content, bid_content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            # 分析经纪队列变化
            self.analyze_broker_activity(ask_content, bid_content)
        
        return ret_code, ask_content, bid_content
    
    def analyze_broker_activity(self, ask_data, bid_data):
        # 合并买卖盘数据
        all_data = pd.concat([ask_data, bid_data], ignore_index=True)
        
        if not all_data.empty:
            code = all_data.iloc[0]['code']
            print(f"\n{code} 经纪队列分析:")
            
            # 统计经纪商参与度
            broker_count = all_data['broker_name'].value_counts()
            
            # 更新活跃度记录
            if code not in self.broker_activity:
                self.broker_activity[code] = {}
            
            for broker, count in broker_count.items():
                if broker not in self.broker_activity[code]:
                    self.broker_activity[code][broker] = 0
                self.broker_activity[code][broker] += count
            
            # 显示最活跃的经纪商
            print("最活跃经纪商:")
            for broker, count in broker_count.head(3).items():
                print(f"  {broker}: {count}档")
            
            # 分析买卖盘分布
            ask_brokers = len(ask_data['broker_name'].unique()) if not ask_data.empty else 0
            bid_brokers = len(bid_data['broker_name'].unique()) if not bid_data.empty else 0
            
            print(f"卖盘经纪数: {ask_brokers}, 买盘经纪数: {bid_brokers}")
            
            # 寻找大型机构
            institutional_brokers = all_data[all_data['broker_name'].str.contains('高盛|摩根|中金|瑞银', na=False)]
            if not institutional_brokers.empty:
                print("机构参与:")
                for _, row in institutional_brokers.iterrows():
                    side = "卖盘" if row['code'] in ask_data['code'].values else "买盘"
                    print(f"  {row['broker_name']} ({side}档位{row['order_level']})")

# 使用分析器
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
analyzer = BrokerAnalyzer()
quote_ctx.set_handler(analyzer)
quote_ctx.subscribe(['HK.00700'], [SubType.BROKER])
```

## 机构跟踪示例

```python
from futu import *

class InstitutionTracker(BrokerHandlerBase):
    def __init__(self):
        super().__init__()
        self.target_institutions = ['高盛', '摩根', '中金', '瑞银', '美林']
        self.institution_positions = {}
    
    def on_recv_rsp(self, rsp_pb):
        ret_code, ask_content, bid_content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            self.track_institutions(ask_content, bid_content)
        
        return ret_code, ask_content, bid_content
    
    def track_institutions(self, ask_data, bid_data):
        # 检查卖盘中的机构
        for _, row in ask_data.iterrows():
            for institution in self.target_institutions:
                if institution in row['broker_name']:
                    print(f"🔴 机构卖盘: {row['broker_name']} 档位{row['order_level']}")
                    self.record_position(row['code'], institution, 'ASK', row['order_level'])
        
        # 检查买盘中的机构
        for _, row in bid_data.iterrows():
            for institution in self.target_institutions:
                if institution in row['broker_name']:
                    print(f"🟢 机构买盘: {row['broker_name']} 档位{row['order_level']}")
                    self.record_position(row['code'], institution, 'BID', row['order_level'])
    
    def record_position(self, code, institution, side, level):
        if code not in self.institution_positions:
            self.institution_positions[code] = {}
        
        if institution not in self.institution_positions[code]:
            self.institution_positions[code][institution] = {'ASK': [], 'BID': []}
        
        self.institution_positions[code][institution][side].append(level)
    
    def get_institution_summary(self):
        print("\n机构持仓总结:")
        for code, institutions in self.institution_positions.items():
            print(f"\n{code}:")
            for institution, positions in institutions.items():
                ask_levels = positions['ASK']
                bid_levels = positions['BID']
                print(f"  {institution}: 卖盘{len(ask_levels)}档, 买盘{len(bid_levels)}档")

# 使用机构跟踪器
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
tracker = InstitutionTracker()
quote_ctx.set_handler(tracker)
quote_ctx.subscribe(['HK.00700'], [SubType.BROKER])
```

## 应用场景

1. **机构行为分析**: 跟踪大型机构的交易行为
2. **市场结构研究**: 了解市场参与者构成
3. **交易策略**: 基于经纪队列制定交易策略
4. **风险管理**: 识别大额委托和潜在风险

## 注意事项

1. 必须先订阅BROKER类型才能收到推送
2. 经纪队列数据仅适用于支持的市场
3. 数据更新频率较高，需要高效处理
4. 经纪商信息可能因市场而异
5. 需要相应的行情权限和经纪队列权限