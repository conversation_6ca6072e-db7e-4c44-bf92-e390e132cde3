# 实时逐笔回调

> 对应官方文档: `/futu-api-doc/quote/update-ticker.html`

## 接口介绍

实时逐笔回调用于接收股票逐笔交易数据的实时推送。当订阅的股票发生新的交易时，会自动触发此回调函数。

## 回调机制

```python
from futu import *

class TickerTest(TickerHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super(TickerTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("TickerTest: error, msg: %s" % content)
            return RET_ERROR, content
        
        print("TickerTest ", content)  # 实时逐笔数据
        return RET_OK, content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = TickerTest()
quote_ctx.set_handler(handler)  # 设置实时逐笔回调
```

## 数据结构

实时逐笔推送包含以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| time | str | 交易时间 |
| sequence | int | 交易序号 |
| dir | int | 交易方向 |
| price | float | 交易价格 |
| volume | int | 交易数量 |
| turnover | float | 交易金额 |
| recvtime | str | 接收时间 |
| type | int | 交易类型 |

### 交易方向说明

| 值 | 说明 |
|----|------|
| 1 | 买盘(外盘) |
| -1 | 卖盘(内盘) |
| 0 | 中性盘 |

### 交易类型说明

| 值 | 说明 |
|----|------|
| 0 | 自动撮合 |
| 1 | 开盘 |
| 2 | 收盘 |
| 3 | 外盘 |
| 4 | 内盘 |
| 5 | 碎股交易 |

## 使用示例

```python
from futu import *

class TickerHandler(TickerHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            for row in content:
                print(f"股票: {row['code']} {row['stock_name']}")
                print(f"时间: {row['time']}")
                print(f"价格: {row['price']}")
                print(f"数量: {row['volume']}")
                print(f"金额: {row['turnover']}")
                
                # 解析交易方向
                direction_map = {1: "买盘", -1: "卖盘", 0: "中性"}
                direction = direction_map.get(row['dir'], "未知")
                print(f"方向: {direction}")
                
                # 解析交易类型
                type_map = {0: "自动撮合", 1: "开盘", 2: "收盘", 3: "外盘", 4: "内盘", 5: "碎股"}
                trade_type = type_map.get(row['type'], "未知")
                print(f"类型: {trade_type}")
                print("-" * 40)
        
        return ret_code, content

# 设置回调并订阅逐笔数据
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(TickerHandler())
quote_ctx.subscribe(['HK.00700'], [SubType.TICKER])
```

## 应用场景

1. **交易监控**: 实时监控每笔交易的详细信息
2. **市场分析**: 分析买卖盘力量对比
3. **量价分析**: 结合价格和成交量分析市场趋势
4. **算法交易**: 基于逐笔数据制定交易策略

## 注意事项

1. 必须先订阅TICKER类型才能收到推送
2. 推送频率极高，需要高效处理避免积压
3. 交易方向可帮助判断市场情绪
4. 建议结合其他数据类型进行综合分析