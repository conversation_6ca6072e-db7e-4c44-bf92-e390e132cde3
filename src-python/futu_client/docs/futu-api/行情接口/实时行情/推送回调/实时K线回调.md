# 实时K线回调

> 对应官方文档: `/futu-api-doc/quote/update-kl.html`

## 接口介绍

实时K线回调用于接收股票K线数据的实时推送。当订阅的股票K线发生变化时（新增或更新），会自动触发此回调函数。

## 回调机制

```python
from futu import *

class CurKlineTest(CurKlineHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super(CurKlineTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("CurKlineTest: error, msg: %s" % content)
            return RET_ERROR, content
        
        print("CurKlineTest ", content)  # 实时K线数据
        return RET_OK, content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = CurKlineTest()
quote_ctx.set_handler(handler)  # 设置实时K线回调
```

## 数据结构

实时K线推送包含以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| time_key | str | K线时间 |
| open | float | 开盘价 |
| close | float | 收盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| pe_ratio | float | 市盈率 |
| turnover_rate | float | 换手率 |
| last_close | float | 昨收价 |

## 使用示例

```python
from futu import *

class KlineHandler(CurKlineHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            for row in content:
                print(f"股票: {row['code']} {row['stock_name']}")
                print(f"时间: {row['time_key']}")
                print(f"开盘: {row['open']}")
                print(f"收盘: {row['close']}")
                print(f"最高: {row['high']}")
                print(f"最低: {row['low']}")
                print(f"成交量: {row['volume']}")
                print(f"成交额: {row['turnover']}")
                print("-" * 40)
        
        return ret_code, content

# 设置回调并订阅K线
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(KlineHandler())
quote_ctx.subscribe(['HK.00700'], [SubType.K_1M])  # 订阅1分钟K线
```

## K线类型

支持订阅的K线类型：

| 类型 | 说明 |
|------|------|
| SubType.K_1M | 1分钟K线 |
| SubType.K_3M | 3分钟K线 |
| SubType.K_5M | 5分钟K线 |
| SubType.K_15M | 15分钟K线 |
| SubType.K_30M | 30分钟K线 |
| SubType.K_60M | 60分钟K线 |
| SubType.K_DAY | 日K线 |
| SubType.K_WEEK | 周K线 |
| SubType.K_MON | 月K线 |

## 注意事项

1. 必须先订阅相应的K线类型才能收到推送
2. 不同K线类型的推送频率不同
3. 推送的K线数据可能是新增或更新现有K线
4. 建议根据time_key判断是否为新的K线数据