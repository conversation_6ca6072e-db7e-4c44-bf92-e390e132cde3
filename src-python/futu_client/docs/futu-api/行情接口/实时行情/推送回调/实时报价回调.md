# 实时报价回调

> 对应官方文档: `/futu-api-doc/quote/update-stock-quote.html`

## 接口介绍

实时报价回调用于接收股票实时价格变化的推送通知。当订阅的股票价格发生变化时，会自动触发此回调函数。

## 回调机制

```python
from futu import *

class StockQuoteTest(StockQuoteHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super(StockQuoteTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("StockQuoteTest: error, msg: %s" % content)
            return RET_ERROR, content
        
        print("StockQuoteTest ", content)  # 实时报价数据
        return RET_OK, content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = StockQuoteTest()
quote_ctx.set_handler(handler)  # 设置实时报价回调
```

## 数据结构

实时报价推送包含以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| last_price | float | 最新价 |
| open_price | float | 开盘价 |
| high_price | float | 最高价 |
| low_price | float | 最低价 |
| prev_close_price | float | 昨收价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| change_rate | float | 涨跌幅 |
| change_val | float | 涨跌额 |

## 使用示例

```python
from futu import *

def stock_quote_callback(rsp_pb):
    ret_code, content = rsp_pb
    if ret_code == RET_OK:
        for row in content:
            print(f"股票: {row['code']} {row['stock_name']}")
            print(f"最新价: {row['last_price']}")
            print(f"涨跌幅: {row['change_rate']}%")
            print("-" * 30)

# 设置回调并订阅
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(StockQuoteTest())
quote_ctx.subscribe(['HK.00700'], [SubType.QUOTE])
```

## 注意事项

1. 必须先订阅相关股票才能收到推送
2. 推送频率取决于市场活跃度
3. 回调函数执行不应阻塞主线程
4. 建议在回调中进行数据缓存而非直接处理复杂逻辑