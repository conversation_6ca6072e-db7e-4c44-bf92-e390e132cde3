# 实时摆盘回调

> 对应官方文档: `/futu-api-doc/quote/update-order-book.html`

## 接口介绍

实时摆盘回调用于接收股票买卖盘数据的实时推送。当订阅的股票买卖盘发生变化时，会自动触发此回调函数。

## 回调机制

```python
from futu import *

class OrderBookTest(OrderBookHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super(OrderBookTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("OrderBookTest: error, msg: %s" % content)
            return RET_ERROR, content
        
        print("OrderBookTest ", content)  # 实时摆盘数据
        return RET_OK, content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = OrderBookTest()
quote_ctx.set_handler(handler)  # 设置实时摆盘回调
```

## 数据结构

实时摆盘推送包含以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| Bid | list | 买盘数据 |
| Ask | list | 卖盘数据 |

### 买卖盘数据结构

每个买卖盘档位包含：
- 价格 (float)
- 数量 (int)
- 笔数 (int)

## 使用示例

```python
from futu import *

class OrderBookHandler(OrderBookHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            for row in content:
                print(f"股票: {row['code']}")
                
                # 显示买盘前5档
                if 'Bid' in row:
                    print("买盘:")
                    for i, bid in enumerate(row['Bid'][:5]):
                        print(f"  档位{i+1}: 价格={bid[0]}, 数量={bid[1]}, 笔数={bid[2]}")
                
                # 显示卖盘前5档
                if 'Ask' in row:
                    print("卖盘:")
                    for i, ask in enumerate(row['Ask'][:5]):
                        print(f"  档位{i+1}: 价格={ask[0]}, 数量={ask[1]}, 笔数={ask[2]}")
                
                print("-" * 50)
        
        return ret_code, content

# 设置回调并订阅
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(OrderBookHandler())
quote_ctx.subscribe(['HK.00700'], [SubType.ORDER_BOOK])
```

## 注意事项

1. 必须先订阅ORDER_BOOK类型才能收到推送
2. 买卖盘数据按价格优先级排序
3. 推送频率较高，建议合理处理避免性能问题
4. 可通过档位数据计算价差和市场深度