# 实时分时回调

> 对应官方文档: `/futu-api-doc/quote/update-rt.html`

## 接口介绍

实时分时回调用于接收股票分时数据的实时推送。当订阅的股票分时数据发生变化时，会自动触发此回调函数。

## 回调机制

```python
from futu import *

class RTDataTest(RTDataHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super(RTDataTest, self).on_recv_rsp(rsp_pb)
        if ret_code != RET_OK:
            print("RTDataTest: error, msg: %s" % content)
            return RET_ERROR, content
        
        print("RTDataTest ", content)  # 实时分时数据
        return RET_OK, content

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
handler = RTDataTest()
quote_ctx.set_handler(handler)  # 设置实时分时回调
```

## 数据结构

实时分时推送包含以下字段：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| time | str | 分时时间点 |
| is_blank | bool | 是否为空白点 |
| opened_mins | int | 开盘分钟数 |
| cur_price | float | 当前价格 |
| last_close | float | 昨收价 |
| avg_price | float | 均价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |

## 使用示例

```python
from futu import *

class RTDataHandler(RTDataHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, content = super().on_recv_rsp(rsp_pb)
        if ret_code == RET_OK:
            for row in content:
                if not row['is_blank']:  # 跳过空白点
                    print(f"股票: {row['code']} {row['stock_name']}")
                    print(f"时间: {row['time']}")
                    print(f"当前价: {row['cur_price']}")
                    print(f"均价: {row['avg_price']}")
                    print(f"成交量: {row['volume']}")
                    print(f"成交额: {row['turnover']}")
                    
                    # 计算涨跌幅
                    change_rate = (row['cur_price'] - row['last_close']) / row['last_close'] * 100
                    print(f"涨跌幅: {change_rate:.2f}%")
                    print("-" * 40)
        
        return ret_code, content

# 设置回调并订阅分时数据
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
quote_ctx.set_handler(RTDataHandler())
quote_ctx.subscribe(['HK.00700'], [SubType.RT_DATA])
```

## 分时数据特点

1. **时间间隔**: 通常为1分钟间隔
2. **空白点**: 非交易时间的数据点标记为空白
3. **累计数据**: 成交量和成交额为当日累计值
4. **均价计算**: 均价为当日平均成交价格

## 应用场景

1. **实时监控**: 监控股票价格实时变化
2. **分时图表**: 绘制股票分时走势图
3. **交易信号**: 基于分时数据判断买卖时机
4. **风险控制**: 实时监控持仓股票价格波动

## 注意事项

1. 必须先订阅RT_DATA类型才能收到推送
2. 推送频率较高，建议合理处理避免性能问题
3. 空白点数据可用于标识非交易时间
4. 分时数据仅在交易时间内有效