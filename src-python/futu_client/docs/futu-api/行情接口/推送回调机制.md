# 数据推送回调

富途OpenAPI支持实时数据推送功能，当订阅的股票数据发生变化时，会自动推送到客户端。通过设置回调函数，可以实时接收和处理这些数据。

## 推送机制概述

### 推送流程

```
1. 订阅数据 → 2. 设置回调 → 3. 接收推送 → 4. 处理数据
```

### 支持的推送类型

| 推送类型 | 数据类型 | 说明 |
|----------|----------|------|
| 报价推送 | QUOTE | 实时报价变化 |
| K线推送 | K_LINE | K线数据更新 |
| 摆盘推送 | ORDER_BOOK | 买卖盘变化 |
| 逐笔推送 | DEAL | 逐笔成交数据 |
| 经纪推送 | BROKER | 经纪队列变化 |

## 回调函数基类

### StockQuoteHandlerBase - 报价推送

```python
import futu as ft

class StockQuoteHandler(ft.StockQuoteHandlerBase):
    """报价推送回调处理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收推送响应"""
        ret_code, data = super(StockQuoteHandler, self).on_recv_rsp(rsp_pb)
        
        if ret_code != ft.RET_OK:
            print("报价推送错误:", data)
            return ft.RET_ERROR, data
        
        # 处理报价数据
        self.process_quote_data(data)
        return ft.RET_OK, data
    
    def process_quote_data(self, data):
        """处理报价数据"""
        for index, row in data.iterrows():
            code = row['code']
            last_price = row['last_price']
            change_rate = (row['last_price'] - row['prev_close_price']) / row['prev_close_price'] * 100
            
            print(f"[报价推送] {code}: {last_price:.2f} ({change_rate:+.2f}%)")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 设置回调处理器
    quote_handler = StockQuoteHandler()
    quote_ctx.set_handler(quote_handler)
    
    # 订阅报价推送
    ret, err_message = quote_ctx.subscribe(['HK.00700', 'HK.00001'], [ft.SubType.QUOTE])
    if ret == ft.RET_OK:
        print("订阅成功，等待推送...")
        
        # 保持连接以接收推送
        import time
        time.sleep(60)  # 等待1分钟
    else:
        print('订阅失败:', err_message)
        
finally:
    quote_ctx.close()
```

### CurKlineHandlerBase - K线推送

```python
import futu as ft
from datetime import datetime

class KLineHandler(ft.CurKlineHandlerBase):
    """K线推送回调处理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收K线推送响应"""
        ret_code, data = super(KLineHandler, self).on_recv_rsp(rsp_pb)
        
        if ret_code != ft.RET_OK:
            print("K线推送错误:", data)
            return ft.RET_ERROR, data
        
        # 处理K线数据
        self.process_kline_data(data)
        return ft.RET_OK, data
    
    def process_kline_data(self, data):
        """处理K线数据"""
        for index, row in data.iterrows():
            code = row['code']
            time_key = row['time_key']
            close_price = row['close']
            volume = row['volume']
            
            print(f"[K线推送] {code} {time_key}: 收盘{close_price:.2f} 量{volume:,}")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 设置K线推送处理器
    kline_handler = KLineHandler()
    quote_ctx.set_handler(kline_handler)
    
    # 订阅1分钟K线推送
    ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.K_1M])
    if ret == ft.RET_OK:
        print("K线订阅成功，等待推送...")
        
        # 保持连接
        import time
        time.sleep(300)  # 等待5分钟
    else:
        print('K线订阅失败:', err_message)
        
finally:
    quote_ctx.close()
```

### OrderBookHandlerBase - 摆盘推送

```python
import futu as ft

class OrderBookHandler(ft.OrderBookHandlerBase):
    """摆盘推送回调处理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收摆盘推送响应"""
        ret_code, bid_data, ask_data = super(OrderBookHandler, self).on_recv_rsp(rsp_pb)
        
        if ret_code != ft.RET_OK:
            print("摆盘推送错误:", bid_data)
            return ft.RET_ERROR, bid_data, ask_data
        
        # 处理摆盘数据
        self.process_order_book_data(bid_data, ask_data)
        return ft.RET_OK, bid_data, ask_data
    
    def process_order_book_data(self, bid_data, ask_data):
        """处理摆盘数据"""
        if not bid_data.empty and not ask_data.empty:
            code = bid_data.iloc[0]['code'] if not bid_data.empty else ask_data.iloc[0]['code']
            
            best_bid = bid_data.iloc[0]['price'] if not bid_data.empty else 0
            best_ask = ask_data.iloc[0]['price'] if not ask_data.empty else 0
            spread = best_ask - best_bid if best_bid > 0 and best_ask > 0 else 0
            
            print(f"[摆盘推送] {code}: 买一{best_bid:.2f} 卖一{best_ask:.2f} 价差{spread:.2f}")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 设置摆盘推送处理器
    orderbook_handler = OrderBookHandler()
    quote_ctx.set_handler(orderbook_handler)
    
    # 订阅摆盘推送
    ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.ORDER_BOOK])
    if ret == ft.RET_OK:
        print("摆盘订阅成功，等待推送...")
        
        import time
        time.sleep(60)
    else:
        print('摆盘订阅失败:', err_message)
        
finally:
    quote_ctx.close()
```

### DealHandlerBase - 逐笔推送

```python
import futu as ft

class DealHandler(ft.DealHandlerBase):
    """逐笔推送回调处理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收逐笔推送响应"""
        ret_code, data = super(DealHandler, self).on_recv_rsp(rsp_pb)
        
        if ret_code != ft.RET_OK:
            print("逐笔推送错误:", data)
            return ft.RET_ERROR, data
        
        # 处理逐笔数据
        self.process_deal_data(data)
        return ft.RET_OK, data
    
    def process_deal_data(self, data):
        """处理逐笔数据"""
        for index, row in data.iterrows():
            code = row['code']
            time = row['time']
            price = row['price']
            volume = row['volume']
            direction = row['direction']
            
            direction_symbol = {'BUY': '↑', 'SELL': '↓', 'NEUTRAL': '→'}.get(direction, direction)
            
            print(f"[逐笔推送] {code} {time}: {direction_symbol}{price:.2f} x {volume:,}")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 设置逐笔推送处理器
    deal_handler = DealHandler()
    quote_ctx.set_handler(deal_handler)
    
    # 订阅逐笔推送
    ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.DEAL])
    if ret == ft.RET_OK:
        print("逐笔订阅成功，等待推送...")
        
        import time
        time.sleep(60)
    else:
        print('逐笔订阅失败:', err_message)
        
finally:
    quote_ctx.close()
```

## 综合推送处理器

### 多类型数据推送处理

```python
import futu as ft
import pandas as pd
from datetime import datetime
import threading
from collections import defaultdict

class ComprehensiveDataHandler:
    """综合数据推送处理器"""
    
    def __init__(self):
        self.quote_data = {}
        self.kline_data = defaultdict(list)
        self.deal_data = defaultdict(list)
        self.orderbook_data = {}
        self.lock = threading.Lock()
        self.callbacks = {
            'quote': [],
            'kline': [],
            'deal': [],
            'orderbook': []
        }
    
    def add_callback(self, data_type, callback_func):
        """添加回调函数"""
        if data_type in self.callbacks:
            self.callbacks[data_type].append(callback_func)
    
    def create_quote_handler(self):
        """创建报价推送处理器"""
        handler = self
        
        class QuoteHandler(ft.StockQuoteHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    handler.process_quote_push(data)
                return ret_code, data
        
        return QuoteHandler()
    
    def create_kline_handler(self):
        """创建K线推送处理器"""
        handler = self
        
        class KLineHandler(ft.CurKlineHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    handler.process_kline_push(data)
                return ret_code, data
        
        return KLineHandler()
    
    def create_deal_handler(self):
        """创建逐笔推送处理器"""
        handler = self
        
        class DealHandler(ft.DealHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    handler.process_deal_push(data)
                return ret_code, data
        
        return DealHandler()
    
    def create_orderbook_handler(self):
        """创建摆盘推送处理器"""
        handler = self
        
        class OrderBookHandler(ft.OrderBookHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, bid_data, ask_data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    handler.process_orderbook_push(bid_data, ask_data)
                return ret_code, bid_data, ask_data
        
        return OrderBookHandler()
    
    def process_quote_push(self, data):
        """处理报价推送"""
        with self.lock:
            for _, row in data.iterrows():
                code = row['code']
                self.quote_data[code] = {
                    'last_price': row['last_price'],
                    'change_rate': (row['last_price'] - row['prev_close_price']) / row['prev_close_price'] * 100,
                    'volume': row['volume'],
                    'turnover': row['turnover'],
                    'update_time': datetime.now()
                }
                
                # 执行回调
                for callback in self.callbacks['quote']:
                    try:
                        callback(code, self.quote_data[code])
                    except Exception as e:
                        print(f"报价回调执行错误: {e}")
    
    def process_kline_push(self, data):
        """处理K线推送"""
        with self.lock:
            for _, row in data.iterrows():
                code = row['code']
                kline_info = {
                    'time_key': row['time_key'],
                    'open': row['open'],
                    'close': row['close'],
                    'high': row['high'],
                    'low': row['low'],
                    'volume': row['volume'],
                    'update_time': datetime.now()
                }
                
                self.kline_data[code].append(kline_info)
                
                # 保留最近100根K线
                if len(self.kline_data[code]) > 100:
                    self.kline_data[code] = self.kline_data[code][-100:]
                
                # 执行回调
                for callback in self.callbacks['kline']:
                    try:
                        callback(code, kline_info)
                    except Exception as e:
                        print(f"K线回调执行错误: {e}")
    
    def process_deal_push(self, data):
        """处理逐笔推送"""
        with self.lock:
            for _, row in data.iterrows():
                code = row['code']
                deal_info = {
                    'time': row['time'],
                    'price': row['price'],
                    'volume': row['volume'],
                    'direction': row['direction'],
                    'turnover': row['turnover'],
                    'update_time': datetime.now()
                }
                
                self.deal_data[code].append(deal_info)
                
                # 保留最近1000笔交易
                if len(self.deal_data[code]) > 1000:
                    self.deal_data[code] = self.deal_data[code][-1000:]
                
                # 执行回调
                for callback in self.callbacks['deal']:
                    try:
                        callback(code, deal_info)
                    except Exception as e:
                        print(f"逐笔回调执行错误: {e}")
    
    def process_orderbook_push(self, bid_data, ask_data):
        """处理摆盘推送"""
        with self.lock:
            if not bid_data.empty or not ask_data.empty:
                code = bid_data.iloc[0]['code'] if not bid_data.empty else ask_data.iloc[0]['code']
                
                self.orderbook_data[code] = {
                    'bid_data': bid_data,
                    'ask_data': ask_data,
                    'best_bid': bid_data.iloc[0]['price'] if not bid_data.empty else 0,
                    'best_ask': ask_data.iloc[0]['price'] if not ask_data.empty else 0,
                    'update_time': datetime.now()
                }
                
                # 执行回调
                for callback in self.callbacks['orderbook']:
                    try:
                        callback(code, self.orderbook_data[code])
                    except Exception as e:
                        print(f"摆盘回调执行错误: {e}")
    
    def get_latest_data(self, code):
        """获取指定股票的最新数据"""
        with self.lock:
            result = {}
            
            if code in self.quote_data:
                result['quote'] = self.quote_data[code]
            
            if code in self.kline_data and self.kline_data[code]:
                result['latest_kline'] = self.kline_data[code][-1]
            
            if code in self.deal_data and self.deal_data[code]:
                result['latest_deal'] = self.deal_data[code][-1]
            
            if code in self.orderbook_data:
                result['orderbook'] = self.orderbook_data[code]
            
            return result
    
    def get_summary(self):
        """获取数据摘要"""
        with self.lock:
            summary = {
                'quote_count': len(self.quote_data),
                'kline_codes': len(self.kline_data),
                'deal_codes': len(self.deal_data),
                'orderbook_count': len(self.orderbook_data),
                'total_deals': sum(len(deals) for deals in self.deal_data.values()),
                'total_klines': sum(len(klines) for klines in self.kline_data.values())
            }
            return summary

# 使用示例
def main():
    # 创建综合处理器
    data_handler = ComprehensiveDataHandler()
    
    # 定义回调函数
    def on_quote_update(code, quote_data):
        print(f"报价更新: {code} = {quote_data['last_price']:.2f} ({quote_data['change_rate']:+.2f}%)")
    
    def on_kline_update(code, kline_data):
        print(f"K线更新: {code} {kline_data['time_key']} 收盘{kline_data['close']:.2f}")
    
    def on_deal_update(code, deal_data):
        direction_symbol = {'BUY': '↑', 'SELL': '↓', 'NEUTRAL': '→'}.get(deal_data['direction'], '')
        print(f"成交推送: {code} {direction_symbol}{deal_data['price']:.2f} x {deal_data['volume']:,}")
    
    def on_orderbook_update(code, orderbook_data):
        spread = orderbook_data['best_ask'] - orderbook_data['best_bid']
        print(f"摆盘更新: {code} 价差{spread:.2f}")
    
    # 注册回调
    data_handler.add_callback('quote', on_quote_update)
    data_handler.add_callback('kline', on_kline_update)
    data_handler.add_callback('deal', on_deal_update)
    data_handler.add_callback('orderbook', on_orderbook_update)
    
    # 创建行情上下文
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        # 设置各种推送处理器
        quote_ctx.set_handler(data_handler.create_quote_handler())
        quote_ctx.set_handler(data_handler.create_kline_handler())
        quote_ctx.set_handler(data_handler.create_deal_handler())
        quote_ctx.set_handler(data_handler.create_orderbook_handler())
        
        # 订阅多种数据
        codes = ['HK.00700', 'HK.00001']
        sub_types = [ft.SubType.QUOTE, ft.SubType.K_1M, ft.SubType.DEAL, ft.SubType.ORDER_BOOK]
        
        ret, err_message = quote_ctx.subscribe(codes, sub_types)
        if ret == ft.RET_OK:
            print(f"订阅成功: {codes}")
            print("等待数据推送...")
            
            # 保持连接并定期显示摘要
            import time
            for i in range(12):  # 运行2分钟（12 x 10秒）
                time.sleep(10)
                summary = data_handler.get_summary()
                print(f"\n[摘要] 报价:{summary['quote_count']} K线:{summary['total_klines']} "
                      f"成交:{summary['total_deals']} 摆盘:{summary['orderbook_count']}")
        else:
            print('订阅失败:', err_message)
    
    finally:
        quote_ctx.close()

if __name__ == "__main__":
    main()
```

## 交易推送回调

### TradeOrderHandlerBase - 订单推送

```python
import futu as ft

class TradeOrderHandler(ft.TradeOrderHandlerBase):
    """交易订单推送处理器"""
    
    def on_recv_rsp(self, rsp_pb):
        """接收订单推送响应"""
        ret_code, data = super(TradeOrderHandler, self).on_recv_rsp(rsp_pb)
        
        if ret_code != ft.RET_OK:
            print("订单推送错误:", data)
            return ft.RET_ERROR, data
        
        # 处理订单数据
        self.process_order_update(data)
        return ft.RET_OK, data
    
    def process_order_update(self, data):
        """处理订单更新"""
        for index, row in data.iterrows():
            order_id = row['order_id']
            order_status = row['order_status']
            code = row['code']
            trd_side = row['trd_side']
            qty = row['qty']
            dealt_qty = row['dealt_qty']
            price = row['price']
            
            print(f"[订单推送] {order_id}: {code} {trd_side} {dealt_qty}/{qty} @ {price:.2f} [{order_status}]")
            
            # 根据订单状态进行不同处理
            if order_status == 'FILLED_ALL':
                self.on_order_filled(row)
            elif order_status == 'FILLED_PART':
                self.on_order_partial_filled(row)
            elif order_status == 'CANCELLED_ALL':
                self.on_order_cancelled(row)
    
    def on_order_filled(self, order_data):
        """订单完全成交处理"""
        print(f"✅ 订单完全成交: {order_data['code']} {order_data['trd_side']} {order_data['qty']}")
    
    def on_order_partial_filled(self, order_data):
        """订单部分成交处理"""
        filled_ratio = order_data['dealt_qty'] / order_data['qty'] * 100
        print(f"⚡ 订单部分成交: {order_data['code']} 成交率{filled_ratio:.1f}%")
    
    def on_order_cancelled(self, order_data):
        """订单取消处理"""
        print(f"❌ 订单已撤销: {order_data['code']} {order_data['trd_side']}")

# 使用示例
trd_ctx = ft.OpenHKTradeContext(host='127.0.0.1', port=11111)

try:
    # 解锁交易
    ret, data = trd_ctx.unlock_trade("123456")
    if ret != ft.RET_OK:
        print("交易解锁失败:", data)
        exit()
    
    # 设置订单推送处理器
    order_handler = TradeOrderHandler()
    trd_ctx.set_handler(order_handler)
    
    # 下单测试
    ret, order_data = trd_ctx.place_order(
        price=100.0,
        qty=100,
        code="HK.00700",
        trd_side=ft.TrdSide.BUY,
        trd_env=ft.TrdEnv.SIMULATE
    )
    
    if ret == ft.RET_OK:
        print("下单成功，等待推送...")
        
        # 保持连接以接收推送
        import time
        time.sleep(30)
    else:
        print("下单失败:", order_data)
        
finally:
    trd_ctx.close()
```

## 推送性能优化

### 批量处理推送数据

```python
import futu as ft
import threading
import queue
import time
from collections import defaultdict

class BatchPushProcessor:
    """批量推送数据处理器"""
    
    def __init__(self, batch_size=100, batch_interval=1.0):
        self.batch_size = batch_size
        self.batch_interval = batch_interval
        self.data_queue = queue.Queue()
        self.processor_thread = None
        self.running = False
        self.batch_buffers = defaultdict(list)
        
    def start(self):
        """启动批量处理线程"""
        self.running = True
        self.processor_thread = threading.Thread(target=self._process_batch_data)
        self.processor_thread.daemon = True
        self.processor_thread.start()
    
    def stop(self):
        """停止批量处理"""
        self.running = False
        if self.processor_thread:
            self.processor_thread.join()
    
    def add_data(self, data_type, data):
        """添加数据到队列"""
        self.data_queue.put((data_type, data, time.time()))
    
    def _process_batch_data(self):
        """批量处理数据"""
        last_process_time = time.time()
        
        while self.running:
            try:
                # 收集数据
                while not self.data_queue.empty():
                    data_type, data, timestamp = self.data_queue.get_nowait()
                    self.batch_buffers[data_type].append((data, timestamp))
                    
                    # 检查是否达到批量大小
                    if len(self.batch_buffers[data_type]) >= self.batch_size:
                        self._process_buffer(data_type)
                
                # 检查是否达到时间间隔
                current_time = time.time()
                if current_time - last_process_time >= self.batch_interval:
                    for data_type in list(self.batch_buffers.keys()):
                        if self.batch_buffers[data_type]:
                            self._process_buffer(data_type)
                    last_process_time = current_time
                
                time.sleep(0.1)  # 避免CPU占用过高
                
            except Exception as e:
                print(f"批量处理异常: {e}")
    
    def _process_buffer(self, data_type):
        """处理缓冲区数据"""
        if not self.batch_buffers[data_type]:
            return
        
        batch_data = self.batch_buffers[data_type]
        self.batch_buffers[data_type] = []
        
        try:
            if data_type == 'quote':
                self._process_quote_batch(batch_data)
            elif data_type == 'deal':
                self._process_deal_batch(batch_data)
            elif data_type == 'kline':
                self._process_kline_batch(batch_data)
        except Exception as e:
            print(f"批量处理{data_type}数据错误: {e}")
    
    def _process_quote_batch(self, batch_data):
        """批量处理报价数据"""
        print(f"批量处理 {len(batch_data)} 条报价数据")
        
        # 按股票代码分组
        code_groups = defaultdict(list)
        for data, timestamp in batch_data:
            for _, row in data.iterrows():
                code_groups[row['code']].append((row, timestamp))
        
        # 处理每个股票的数据
        for code, quotes in code_groups.items():
            latest_quote = max(quotes, key=lambda x: x[1])[0]  # 获取最新的报价
            print(f"  {code}: 最新价 {latest_quote['last_price']:.2f}")
    
    def _process_deal_batch(self, batch_data):
        """批量处理逐笔数据"""
        total_deals = sum(len(data) for data, _ in batch_data)
        print(f"批量处理 {total_deals} 条逐笔数据")
        
        # 统计成交活跃度
        deal_counts = defaultdict(int)
        for data, timestamp in batch_data:
            for _, row in data.iterrows():
                deal_counts[row['code']] += 1
        
        for code, count in deal_counts.items():
            print(f"  {code}: {count} 笔成交")
    
    def _process_kline_batch(self, batch_data):
        """批量处理K线数据"""
        print(f"批量处理 {len(batch_data)} 条K线数据")

# 优化的推送处理器
class OptimizedPushHandler:
    def __init__(self):
        self.batch_processor = BatchPushProcessor(batch_size=50, batch_interval=2.0)
        self.batch_processor.start()
    
    def create_quote_handler(self):
        processor = self.batch_processor
        
        class QuoteHandler(ft.StockQuoteHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    processor.add_data('quote', data)
                return ret_code, data
        
        return QuoteHandler()
    
    def create_deal_handler(self):
        processor = self.batch_processor
        
        class DealHandler(ft.DealHandlerBase):
            def on_recv_rsp(self, rsp_pb):
                ret_code, data = super().on_recv_rsp(rsp_pb)
                if ret_code == ft.RET_OK:
                    processor.add_data('deal', data)
                return ret_code, data
        
        return DealHandler()
    
    def cleanup(self):
        """清理资源"""
        self.batch_processor.stop()

# 使用示例
def optimized_push_example():
    push_handler = OptimizedPushHandler()
    quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
    
    try:
        # 设置优化的推送处理器
        quote_ctx.set_handler(push_handler.create_quote_handler())
        quote_ctx.set_handler(push_handler.create_deal_handler())
        
        # 订阅数据
        codes = ['HK.00700', 'HK.00001', 'HK.00005', 'HK.00941']
        ret, err = quote_ctx.subscribe(codes, [ft.SubType.QUOTE, ft.SubType.DEAL])
        
        if ret == ft.RET_OK:
            print("订阅成功，开始批量处理推送数据...")
            time.sleep(60)  # 运行1分钟
        else:
            print("订阅失败:", err)
    
    finally:
        push_handler.cleanup()
        quote_ctx.close()

if __name__ == "__main__":
    optimized_push_example()
```

## 推送使用注意事项

### 1. 性能考虑
- 推送频率很高，避免在回调中进行耗时操作
- 使用异步处理或队列来处理推送数据
- 及时清理历史数据，避免内存泄漏

### 2. 错误处理
- 回调函数中的异常不会中断推送
- 建议在回调中添加try-catch处理
- 记录错误日志便于调试

### 3. 线程安全
- 推送在独立线程中执行
- 访问共享数据时注意线程安全
- 使用锁或线程安全的数据结构

### 4. 订阅管理
- 推送基于订阅，确保已订阅相应数据
- 及时取消不需要的订阅以节约资源
- 监控订阅状态和推送质量

通过合理使用数据推送功能，可以构建高效的实时交易和监控系统。