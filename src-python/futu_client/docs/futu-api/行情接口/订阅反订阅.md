# 订阅反订阅

富途OpenAPI通过订阅机制提供实时数据推送服务。用户需要先订阅相关数据类型，然后通过回调函数接收实时推送的数据。

## subscribe - 订阅实时数据

### 接口原型

```python
subscribe(code_list, subtype_list, subscribe_push=True, is_first_push=True, subscribe_type=SubType.QUOTE, is_req_after_sub=True)
```

### 功能说明

订阅指定股票的实时数据，支持报价、K线、逐笔、摆盘等多种数据类型。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code_list | list | 是 | 股票代码列表，例如['HK.00700', 'US.AAPL'] |
| subtype_list | list | 是 | 订阅数据类型列表，见SubType枚举 |
| subscribe_push | bool | 否 | 是否订阅该数据类型的推送，默认True |
| is_first_push | bool | 否 | 订阅成功后是否立即推送一次数据，默认True |
| subscribe_type | SubType | 否 | 订阅类型，默认SubType.QUOTE |
| is_req_after_sub | bool | 否 | 订阅之后是否立即拉取一次数据，默认True |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | str | 成功时为空字符串，失败时为错误描述 |

### 代码示例

#### 基本订阅

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅腾讯的报价数据
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
else:
    print('订阅成功')

quote_ctx.close()
```

#### 批量订阅多种数据类型

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅多只股票的多种数据类型
code_list = ['HK.00700', 'HK.00001', 'HK.00005']
subtype_list = [ft.SubType.QUOTE, ft.SubType.K_1M, ft.SubType.TICKER]

ret, err_message = quote_ctx.subscribe(code_list, subtype_list)
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
else:
    print('批量订阅成功')

quote_ctx.close()
```

#### 带回调的订阅

```python
import futu as ft
import time

class StockQuoteHandler(ft.StockQuoteHandlerBase):
    def on_recv_rsp(self, rsp_pb):
        ret_code, data = super(StockQuoteHandler, self).on_recv_rsp(rsp_pb)
        if ret_code != ft.RET_OK:
            print("报价推送错误:", data)
            return ft.RET_ERROR, data
        
        print("收到报价推送:")
        print(data)
        return ft.RET_OK, data

# 创建上下文并设置回调
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)
handler = StockQuoteHandler()
quote_ctx.set_handler(handler)

# 订阅数据
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
else:
    print('订阅成功，等待推送数据...')
    time.sleep(10)  # 等待接收推送数据

quote_ctx.close()
```

## unsubscribe - 取消订阅

### 接口原型

```python
unsubscribe(code_list, subtype_list, unsubscribe_all=False)
```

### 功能说明

取消指定股票的订阅数据类型。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code_list | list | 是 | 股票代码列表 |
| subtype_list | list | 是 | 取消订阅的数据类型列表 |
| unsubscribe_all | bool | 否 | 是否取消该股票的所有订阅，默认False |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | str | 成功时为空字符串，失败时为错误描述 |

### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 先订阅
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE, ft.SubType.K_1M])

# 取消部分订阅
ret, err_message = quote_ctx.unsubscribe(['HK.00700'], [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('取消订阅失败:', err_message)
else:
    print('取消订阅成功')

# 取消该股票的所有订阅
ret, err_message = quote_ctx.unsubscribe(['HK.00700'], [], unsubscribe_all=True)

quote_ctx.close()
```

## unsubscribe_all - 取消所有订阅

### 接口原型

```python
unsubscribe_all()
```

### 功能说明

取消当前连接的所有订阅。

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果 |
| data | str | 成功时为空字符串，失败时为错误描述 |

### 代码示例

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅多种数据
quote_ctx.subscribe(['HK.00700', 'HK.00001'], [ft.SubType.QUOTE, ft.SubType.K_1M])

# 取消所有订阅
ret, err_message = quote_ctx.unsubscribe_all()
if ret != ft.RET_OK:
    print('取消全部订阅失败:', err_message)
else:
    print('取消全部订阅成功')

quote_ctx.close()
```

## SubType - 订阅数据类型

### 报价数据

| 常量 | 说明 |
|------|------|
| SubType.QUOTE | 报价推送 |
| SubType.ORDER_BOOK | 摆盘推送 |
| SubType.TICKER | 逐笔推送 |
| SubType.RT_DATA | 分时推送 |
| SubType.BROKER | 经纪队列推送 |

### K线数据

| 常量 | 说明 |
|------|------|
| SubType.K_1M | 1分钟K线 |
| SubType.K_3M | 3分钟K线 |
| SubType.K_5M | 5分钟K线 |
| SubType.K_15M | 15分钟K线 |
| SubType.K_30M | 30分钟K线 |
| SubType.K_60M | 60分钟K线 |
| SubType.K_DAY | 日K线 |
| SubType.K_WEEK | 周K线 |
| SubType.K_MONTH | 月K线 |
| SubType.K_QUARTER | 季K线 |
| SubType.K_YEAR | 年K线 |

## 订阅限制

### 免费用户限制

| 数据类型 | 订阅上限 |
|----------|----------|
| 港股报价 | 10只 |
| 美股报价 | 10只 |
| A股报价 | 10只 |
| K线数据 | 10只 |
| 逐笔数据 | 5只 |
| 摆盘数据 | 5只 |

### 付费用户权限

具体权限请参考富途官方说明或联系客服。

## 注意事项

1. **订阅前检查**：确保OpenD正常运行且已登录
2. **权限验证**：部分数据需要相应的行情权限
3. **连接管理**：建议程序退出前取消所有订阅
4. **推送处理**：设置合适的回调处理器处理推送数据
5. **订阅优化**：批量订阅比单个订阅更高效

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| RET_ERROR | 一般错误 | 检查参数和网络连接 |
| Futu.Common.RetType.RET_DATA_STATUS_ERROR | 数据状态错误 | 检查股票代码格式 |
| 订阅达到上限 | 超出订阅限制 | 取消部分订阅或升级权限 |

## 相关接口

- [获取订阅状态](query-subscription.md) - 查询当前订阅状态
- [股票报价推送](update-stock-quote.md) - 报价推送回调处理
- [K线推送](update-kl.md) - K线推送回调处理