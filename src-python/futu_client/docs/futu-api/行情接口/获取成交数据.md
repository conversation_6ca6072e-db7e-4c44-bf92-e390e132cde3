# 获取逐笔数据

获取股票的逐笔成交数据，包括每笔交易的价格、数量、时间和方向等详细信息，用于分析交易活跃度和资金流向。

## get_deal - 获取逐笔成交

### 接口原型

```python
get_deal(code, num=500)
```

### 功能说明

获取指定股票的逐笔成交数据。**注意：使用此接口前需要先订阅相应股票的逐笔数据。**

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 是 | 股票代码，例如'HK.00700' |
| num | int | 否 | 获取的成交笔数，默认500，最大1000 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 逐笔成交DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| time | str | 成交时间 |
| price | float | 成交价格 |
| volume | int | 成交数量 |
| turnover | float | 成交金额 |
| type | str | 成交类型（外盘/内盘/中性盘） |
| direction | str | 成交方向（BUY/SELL/NEUTRAL） |

## request_history_deal - 获取历史逐笔

### 接口原型

```python
request_history_deal(code, start=None, end=None, max_count=1000, page_req_key=None)
```

### 功能说明

获取历史逐笔成交数据，不需要事先订阅，支持分页获取大量历史数据。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 是 | 股票代码 |
| start | str | 否 | 开始时间，格式'YYYY-MM-DD HH:MM:SS' |
| end | str | 否 | 结束时间，格式'YYYY-MM-DD HH:MM:SS' |
| max_count | int | 否 | 最大数据量，默认1000 |
| page_req_key | str | 否 | 分页请求key |

### 代码示例

#### 获取实时逐笔数据

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 先订阅逐笔数据
    ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.DEAL])
    if ret != ft.RET_OK:
        print('订阅逐笔失败:', err_message)
        exit()
    
    # 获取最近500笔成交
    ret, data = quote_ctx.get_deal('HK.00700', num=500)
    
    if ret == ft.RET_OK:
        print("腾讯(00700)最近成交:")
        print("=" * 80)
        print("时间            价格    数量      成交额    方向")
        print("-" * 80)
        
        # 显示最近20笔成交
        recent_deals = data.head(20)
        
        for _, deal in recent_deals.iterrows():
            direction_map = {'BUY': '买入', 'SELL': '卖出', 'NEUTRAL': '中性'}
            direction = direction_map.get(deal['direction'], deal['direction'])
            
            print(f"{deal['time']:<15} {deal['price']:>7.2f} {deal['volume']:>8,} "
                  f"{deal['turnover']:>10,.0f} {direction:>4}")
        
        # 统计信息
        total_volume = data['volume'].sum()
        total_turnover = data['turnover'].sum()
        avg_price = total_turnover / total_volume if total_volume > 0 else 0
        
        print(f"\n统计信息:")
        print(f"总成交量: {total_volume:,}")
        print(f"总成交额: {total_turnover:,.0f}")
        print(f"平均价格: {avg_price:.2f}")
        
    else:
        print('获取逐笔失败:', data)
        
finally:
    quote_ctx.close()
```

#### 分析资金流向

```python
import futu as ft
import pandas as pd

def analyze_money_flow(quote_ctx, code, num_deals=1000):
    """分析资金流向"""
    
    ret, data = quote_ctx.get_deal(code, num=num_deals)
    
    if ret != ft.RET_OK:
        print(f"获取{code}逐笔数据失败:", data)
        return
    
    print(f"\n{code} 资金流向分析:")
    print("=" * 50)
    
    # 1. 按方向统计
    buy_deals = data[data['direction'] == 'BUY']
    sell_deals = data[data['direction'] == 'SELL']
    neutral_deals = data[data['direction'] == 'NEUTRAL']
    
    buy_volume = buy_deals['volume'].sum()
    sell_volume = sell_deals['volume'].sum()
    neutral_volume = neutral_deals['volume'].sum()
    
    buy_turnover = buy_deals['turnover'].sum()
    sell_turnover = sell_deals['turnover'].sum()
    neutral_turnover = neutral_deals['turnover'].sum()
    
    total_turnover = buy_turnover + sell_turnover + neutral_turnover
    
    print("1. 资金流向统计:")
    print(f"   买入: {buy_turnover:,.0f} ({buy_turnover/total_turnover*100:.1f}%) "
          f"量:{buy_volume:,}")
    print(f"   卖出: {sell_turnover:,.0f} ({sell_turnover/total_turnover*100:.1f}%) "
          f"量:{sell_volume:,}")
    print(f"   中性: {neutral_turnover:,.0f} ({neutral_turnover/total_turnover*100:.1f}%) "
          f"量:{neutral_volume:,}")
    
    # 2. 净流入
    net_inflow = buy_turnover - sell_turnover
    print(f"\n2. 净资金流入: {net_inflow:+,.0f}")
    
    # 3. 平均单笔金额
    avg_buy_amount = buy_turnover / len(buy_deals) if len(buy_deals) > 0 else 0
    avg_sell_amount = sell_turnover / len(sell_deals) if len(sell_deals) > 0 else 0
    
    print(f"\n3. 平均单笔金额:")
    print(f"   买入: {avg_buy_amount:,.0f}")
    print(f"   卖出: {avg_sell_amount:,.0f}")
    
    # 4. 大单分析（单笔超过10万）
    large_threshold = 100000
    large_buy_deals = buy_deals[buy_deals['turnover'] >= large_threshold]
    large_sell_deals = sell_deals[sell_deals['turnover'] >= large_threshold]
    
    large_buy_turnover = large_buy_deals['turnover'].sum()
    large_sell_turnover = large_sell_deals['turnover'].sum()
    
    print(f"\n4. 大单分析(≥{large_threshold:,}):")
    print(f"   大单买入: {large_buy_turnover:,.0f} ({len(large_buy_deals)}笔)")
    print(f"   大单卖出: {large_sell_turnover:,.0f} ({len(large_sell_deals)}笔)")
    print(f"   大单净流入: {large_buy_turnover - large_sell_turnover:+,.0f}")
    
    return {
        'net_inflow': net_inflow,
        'buy_ratio': buy_turnover / total_turnover * 100,
        'large_net_inflow': large_buy_turnover - large_sell_turnover,
        'avg_buy_amount': avg_buy_amount,
        'avg_sell_amount': avg_sell_amount
    }

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅逐笔数据
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.DEAL])
    
    # 分析资金流向
    result = analyze_money_flow(quote_ctx, 'HK.00700')
    
finally:
    quote_ctx.close()
```

#### 交易活跃度分析

```python
import futu as ft
import pandas as pd
from datetime import datetime, timedelta

def analyze_trading_activity(quote_ctx, code, time_window_minutes=30):
    """分析交易活跃度"""
    
    ret, data = quote_ctx.get_deal(code, num=1000)
    
    if ret != ft.RET_OK or data.empty:
        print(f"获取{code}逐笔数据失败")
        return
    
    # 转换时间格式
    data['datetime'] = pd.to_datetime(data['time'])
    
    # 筛选指定时间窗口内的数据
    cutoff_time = datetime.now() - timedelta(minutes=time_window_minutes)
    recent_data = data[data['datetime'] >= cutoff_time]
    
    if recent_data.empty:
        print(f"最近{time_window_minutes}分钟内无交易数据")
        return
    
    print(f"\n{code} 交易活跃度分析 (最近{time_window_minutes}分钟):")
    print("=" * 60)
    
    # 1. 基本统计
    total_deals = len(recent_data)
    total_volume = recent_data['volume'].sum()
    total_turnover = recent_data['turnover'].sum()
    avg_price = total_turnover / total_volume if total_volume > 0 else 0
    
    print(f"1. 基本统计:")
    print(f"   成交笔数: {total_deals:,}")
    print(f"   成交数量: {total_volume:,}")
    print(f"   成交金额: {total_turnover:,.0f}")
    print(f"   平均价格: {avg_price:.2f}")
    print(f"   平均每笔: {total_turnover/total_deals:,.0f}")
    
    # 2. 交易频率
    time_span = (recent_data['datetime'].max() - recent_data['datetime'].min()).total_seconds() / 60
    frequency = total_deals / time_span if time_span > 0 else 0
    
    print(f"\n2. 交易频率:")
    print(f"   时间跨度: {time_span:.1f}分钟")
    print(f"   平均频率: {frequency:.1f}笔/分钟")
    
    # 3. 价格分布
    price_std = recent_data['price'].std()
    price_range = recent_data['price'].max() - recent_data['price'].min()
    
    print(f"\n3. 价格波动:")
    print(f"   价格区间: {recent_data['price'].min():.2f} - {recent_data['price'].max():.2f}")
    print(f"   价格范围: {price_range:.2f}")
    print(f"   价格标准差: {price_std:.3f}")
    
    # 4. 单笔大小分布
    volume_bins = [0, 500, 2000, 10000, 50000, float('inf')]
    volume_labels = ['<500', '500-2K', '2K-10K', '10K-50K', '>50K']
    recent_data['volume_category'] = pd.cut(recent_data['volume'], bins=volume_bins, labels=volume_labels)
    
    print(f"\n4. 单笔数量分布:")
    volume_dist = recent_data['volume_category'].value_counts().sort_index()
    for category, count in volume_dist.items():
        percentage = count / total_deals * 100
        print(f"   {category}: {count} 笔 ({percentage:.1f}%)")
    
    # 5. 时间分布（按分钟统计）
    recent_data['minute'] = recent_data['datetime'].dt.floor('T')
    minute_stats = recent_data.groupby('minute').agg({
        'volume': 'sum',
        'turnover': 'sum',
        'price': 'count'
    }).rename(columns={'price': 'deals'})
    
    if len(minute_stats) > 1:
        print(f"\n5. 分钟级活跃度:")
        print(f"   最活跃分钟: {minute_stats['deals'].idxmax().strftime('%H:%M')} "
              f"({minute_stats['deals'].max()}笔)")
        print(f"   平均每分钟: {minute_stats['deals'].mean():.1f}笔")
    
    return {
        'total_deals': total_deals,
        'frequency': frequency,
        'price_volatility': price_std,
        'avg_deal_size': total_volume / total_deals if total_deals > 0 else 0
    }

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅逐笔数据
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.DEAL])
    
    # 分析交易活跃度
    activity = analyze_trading_activity(quote_ctx, 'HK.00700', time_window_minutes=60)
    
finally:
    quote_ctx.close()
```

#### 获取历史逐笔数据

```python
import futu as ft
from datetime import datetime, timedelta

def get_historical_deals(quote_ctx, code, hours_back=2):
    """获取历史逐笔数据"""
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=hours_back)
    
    start_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
    end_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"获取 {code} 历史逐笔数据:")
    print(f"时间范围: {start_str} 至 {end_str}")
    
    all_deals = []
    page_req_key = None
    page_count = 0
    
    while True:
        ret, data, page_req_key = quote_ctx.request_history_deal(
            code=code,
            start=start_str,
            end=end_str,
            max_count=1000,
            page_req_key=page_req_key
        )
        
        if ret != ft.RET_OK:
            print(f"获取历史逐笔失败: {data}")
            break
        
        if data.empty:
            break
        
        all_deals.append(data)
        page_count += 1
        print(f"已获取第 {page_count} 页，{len(data)} 条记录")
        
        if page_req_key is None:  # 没有更多数据
            break
    
    if all_deals:
        # 合并所有数据
        combined_data = pd.concat(all_deals, ignore_index=True)
        
        print(f"\n历史逐笔数据汇总:")
        print(f"总记录数: {len(combined_data)}")
        print(f"时间跨度: {combined_data['time'].min()} 至 {combined_data['time'].max()}")
        print(f"总成交量: {combined_data['volume'].sum():,}")
        print(f"总成交额: {combined_data['turnover'].sum():,.0f}")
        
        # 按小时统计
        combined_data['datetime'] = pd.to_datetime(combined_data['time'])
        combined_data['hour'] = combined_data['datetime'].dt.hour
        
        hourly_stats = combined_data.groupby('hour').agg({
            'volume': 'sum',
            'turnover': 'sum',
            'price': 'count'
        }).rename(columns={'price': 'deals'})
        
        print(f"\n按小时统计:")
        print("小时    成交笔数    成交量        成交额")
        print("-" * 50)
        for hour, stats in hourly_stats.iterrows():
            print(f"{hour:02d}:00   {stats['deals']:>8}  {stats['volume']:>10,}  {stats['turnover']:>12,.0f}")
        
        return combined_data
    else:
        print("未获取到历史数据")
        return None

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 获取过去2小时的历史逐笔数据
    historical_data = get_historical_deals(quote_ctx, 'HK.00700', hours_back=2)
    
finally:
    quote_ctx.close()
```

#### 逐笔数据实时监控

```python
import futu as ft
import time
from datetime import datetime
from collections import deque

class DealMonitor:
    def __init__(self, quote_ctx, code):
        self.quote_ctx = quote_ctx
        self.code = code
        self.deal_buffer = deque(maxlen=100)  # 保存最近100笔交易
        self.monitoring = False
        
    def add_deal_alerts(self, large_deal_threshold=50000, price_change_threshold=0.5):
        """添加交易预警"""
        self.large_deal_threshold = large_deal_threshold
        self.price_change_threshold = price_change_threshold
        
    def check_alerts(self, new_deals):
        """检查交易预警"""
        alerts = []
        
        for _, deal in new_deals.iterrows():
            # 大单预警
            if hasattr(self, 'large_deal_threshold') and deal['turnover'] >= self.large_deal_threshold:
                direction_map = {'BUY': '买入', 'SELL': '卖出', 'NEUTRAL': '中性'}
                direction = direction_map.get(deal['direction'], deal['direction'])
                alerts.append(f"大单交易: {direction} {deal['turnover']:,.0f} @ {deal['price']:.2f}")
            
            # 价格异动预警（与前一笔价格相比）
            if (hasattr(self, 'price_change_threshold') and 
                len(self.deal_buffer) > 0):
                
                last_price = self.deal_buffer[-1]['price']
                price_change_pct = abs(deal['price'] - last_price) / last_price * 100
                
                if price_change_pct >= self.price_change_threshold:
                    alerts.append(f"价格异动: {last_price:.2f} → {deal['price']:.2f} "
                               f"({price_change_pct:+.2f}%)")
        
        return alerts
    
    def monitor_real_time(self, duration_minutes=30, check_interval=5):
        """实时监控逐笔交易"""
        print(f"开始实时监控 {self.code} 逐笔交易")
        print(f"监控时长: {duration_minutes} 分钟，检查间隔: {check_interval} 秒")
        print("-" * 60)
        
        self.monitoring = True
        start_time = time.time()
        end_time = start_time + duration_minutes * 60
        last_check_time = None
        
        while time.time() < end_time and self.monitoring:
            try:
                # 获取最新逐笔数据
                ret, data = self.quote_ctx.get_deal(self.code, num=50)
                
                if ret == ft.RET_OK and not data.empty:
                    # 如果是第一次获取，记录时间
                    if last_check_time is None:
                        last_check_time = data.iloc[0]['time']
                        print(f"开始监控时间: {last_check_time}")
                        continue
                    
                    # 筛选新的交易数据
                    new_deals = data[data['time'] > last_check_time]
                    
                    if not new_deals.empty:
                        current_time = datetime.now().strftime('%H:%M:%S')
                        print(f"\n[{current_time}] 新增 {len(new_deals)} 笔交易:")
                        
                        # 显示新交易
                        for _, deal in new_deals.head(5).iterrows():  # 只显示前5笔
                            direction_map = {'BUY': '↑', 'SELL': '↓', 'NEUTRAL': '→'}
                            direction = direction_map.get(deal['direction'], deal['direction'])
                            
                            print(f"  {deal['time']} {direction} {deal['price']:>7.2f} "
                                  f"{deal['volume']:>6,} {deal['turnover']:>10,.0f}")
                        
                        # 检查预警
                        alerts = self.check_alerts(new_deals)
                        if alerts:
                            print("  🚨 预警:")
                            for alert in alerts:
                                print(f"    {alert}")
                        
                        # 更新缓存
                        for _, deal in new_deals.iterrows():
                            self.deal_buffer.append(deal.to_dict())
                        
                        # 更新检查时间
                        last_check_time = new_deals.iloc[0]['time']
                    
                    else:
                        # 显示监控状态
                        current_time = datetime.now().strftime('%H:%M:%S')
                        print(f"[{current_time}] 监控中... (缓存{len(self.deal_buffer)}笔)")
                
                else:
                    print(f"获取逐笔数据失败: {data}")
                
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\n监控已手动停止")
                break
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(check_interval)
        
        self.monitoring = False
        print(f"\n监控结束，共缓存 {len(self.deal_buffer)} 笔交易")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
    
    def get_summary(self):
        """获取监控期间的交易摘要"""
        if not self.deal_buffer:
            return "监控期间无交易数据"
        
        deals_df = pd.DataFrame(list(self.deal_buffer))
        
        summary = f"监控摘要 - {self.code}\n"
        summary += f"交易笔数: {len(deals_df)}\n"
        summary += f"总成交量: {deals_df['volume'].sum():,}\n"
        summary += f"总成交额: {deals_df['turnover'].sum():,.0f}\n"
        summary += f"价格区间: {deals_df['price'].min():.2f} - {deals_df['price'].max():.2f}\n"
        
        # 方向统计
        direction_counts = deals_df['direction'].value_counts()
        summary += "交易方向:\n"
        for direction, count in direction_counts.items():
            summary += f"  {direction}: {count} 笔\n"
        
        return summary

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅逐笔数据
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.DEAL])
    
    # 创建监控器
    monitor = DealMonitor(quote_ctx, 'HK.00700')
    
    # 设置预警条件
    monitor.add_deal_alerts(large_deal_threshold=100000, price_change_threshold=1.0)
    
    # 开始实时监控（监控10分钟，每5秒检查一次）
    monitor.monitor_real_time(duration_minutes=10, check_interval=5)
    
    # 显示摘要
    print(monitor.get_summary())
    
finally:
    quote_ctx.close()
```

### 成交类型说明

#### Direction（成交方向）
| 值 | 说明 |
|----|------|
| BUY | 主动买入（外盘） |
| SELL | 主动卖出（内盘） |
| NEUTRAL | 中性盘 |

#### Type（成交类型）
- **外盘**：以卖一价成交的交易
- **内盘**：以买一价成交的交易
- **中性盘**：以买卖中间价成交的交易

### 使用限制

#### 数据限制
- **实时逐笔**：最多获取1000笔
- **历史逐笔**：需要历史数据权限
- **频率限制**：建议配合推送使用，避免频繁拉取

#### 权限要求
- **Level-1**：基础逐笔数据
- **Level-2**：包含更多细节和订单信息

### 注意事项

1. **订阅要求**：获取实时逐笔前需先订阅DEAL数据
2. **数据时效**：逐笔数据更新频率很高，建议使用推送
3. **存储考虑**：逐笔数据量大，注意存储空间管理
4. **网络开销**：频繁获取会增加网络开销
5. **时区处理**：注意不同市场的时区差异

### 相关接口

- [订阅反订阅](sub.md) - 订阅逐笔数据
- [逐笔推送](update-deal.md) - 逐笔数据推送回调
- [获取摆盘数据](get-order-book.md) - 买卖盘数据