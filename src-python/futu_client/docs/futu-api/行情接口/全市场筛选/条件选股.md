# 获取条件选股

> 对应官方文档: `/futu-api-doc/quote/get-stock-filter.html`

## 接口介绍

根据设定的筛选条件，从全市场股票中筛选出符合条件的股票列表。

## 函数原型

```python
get_stock_filter(market, filter_list, plate_code_list=None, begin=0, num=200)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场标识 |
| filter_list | list | 筛选条件列表 |
| plate_code_list | list | 板块代码列表，可选 |
| begin | int | 数据起始点，默认0 |
| num | int | 请求数据个数，默认200 |

## 筛选条件类型

### 价格筛选

```python
# 股价筛选
SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 10.0)  # 股价 >= 10元
SimpleFilter.create(StockField.CUR_PRICE, FilterType.LE, 100.0)  # 股价 <= 100元
```

### 市值筛选

```python
# 市值筛选
SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 10000000000)  # 市值 >= 100亿
SimpleFilter.create(StockField.MARKET_VAL, FilterType.LE, 100000000000)  # 市值 <= 1000亿
```

### 财务指标筛选

```python
# PE筛选
SimpleFilter.create(StockField.PE_RATIO, FilterType.GE, 5.0)  # PE >= 5
SimpleFilter.create(StockField.PE_RATIO, FilterType.LE, 30.0)  # PE <= 30

# PB筛选
SimpleFilter.create(StockField.PB_RATIO, FilterType.GE, 0.5)  # PB >= 0.5
SimpleFilter.create(StockField.PB_RATIO, FilterType.LE, 5.0)  # PB <= 5.0
```

### 涨跌幅筛选

```python
# 涨跌幅筛选
SimpleFilter.create(StockField.CHANGE_RATE, FilterType.GE, 0.0)  # 涨跌幅 >= 0%
SimpleFilter.create(StockField.CHANGE_RATE, FilterType.LE, 10.0)  # 涨跌幅 <= 10%
```

### 换手率筛选

```python
# 换手率筛选
SimpleFilter.create(StockField.TURNOVER_RATE, FilterType.GE, 2.0)  # 换手率 >= 2%
SimpleFilter.create(StockField.TURNOVER_RATE, FilterType.LE, 20.0)  # 换手率 <= 20%
```

## 使用示例

### 基础筛选示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 筛选条件：港股价格10-100元，市值100-1000亿，PE 5-30
filter_list = [
    SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 10.0),
    SimpleFilter.create(StockField.CUR_PRICE, FilterType.LE, 100.0),
    SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 10000000000),
    SimpleFilter.create(StockField.MARKET_VAL, FilterType.LE, 100000000000),
    SimpleFilter.create(StockField.PE_RATIO, FilterType.GE, 5.0),
    SimpleFilter.create(StockField.PE_RATIO, FilterType.LE, 30.0)
]

ret, data = quote_ctx.get_stock_filter(Market.HK, filter_list)

if ret == RET_OK:
    print(f"筛选出 {len(data)} 只股票:")
    print(data)
    
    for index, row in data.iterrows():
        print(f"股票: {row['code']} - {row['stock_name']}")
        print(f"价格: {row['cur_price']:.2f}")
        print(f"市值: {row['market_val']:,.0f}")
        print(f"PE: {row['pe_ratio']:.2f}")
        print(f"涨跌幅: {row['change_rate']:+.2f}%")
        print("-" * 40)
else:
    print('条件选股失败:', data)

quote_ctx.close()
```

### 成长股筛选策略

```python
from futu import *

def screen_growth_stocks():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 成长股筛选条件
    filter_list = [
        # 基本条件
        SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 5.0),  # 价格 >= 5元
        SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 1000000000),  # 市值 >= 10亿
        
        # 成长性指标
        SimpleFilter.create(StockField.PE_RATIO, FilterType.GE, 10.0),  # PE >= 10
        SimpleFilter.create(StockField.PE_RATIO, FilterType.LE, 50.0),  # PE <= 50
        SimpleFilter.create(StockField.PB_RATIO, FilterType.GE, 1.0),   # PB >= 1
        SimpleFilter.create(StockField.PB_RATIO, FilterType.LE, 10.0),  # PB <= 10
        
        # 活跃度
        SimpleFilter.create(StockField.TURNOVER_RATE, FilterType.GE, 1.0),  # 换手率 >= 1%
        SimpleFilter.create(StockField.CHANGE_RATE, FilterType.GE, -5.0),   # 涨跌幅 >= -5%
    ]
    
    ret, data = quote_ctx.get_stock_filter(Market.HK, filter_list, num=100)
    
    if ret == RET_OK:
        print("成长股筛选结果:")
        print("=" * 60)
        
        # 按市值排序
        data_sorted = data.sort_values('market_val', ascending=False)
        
        for index, row in data_sorted.head(20).iterrows():
            # 计算成长评分
            pe_score = max(0, 50 - row['pe_ratio']) / 50 * 30  # PE越低分越高
            pb_score = max(0, 10 - row['pb_ratio']) / 10 * 20   # PB越低分越高
            turnover_score = min(row['turnover_rate'], 10) / 10 * 25  # 换手率适中
            change_score = max(0, row['change_rate'] + 5) / 15 * 25   # 涨跌幅越高分越高
            
            total_score = pe_score + pb_score + turnover_score + change_score
            
            print(f"股票: {row['stock_name']} ({row['code']})")
            print(f"价格: {row['cur_price']:.2f} | 市值: {row['market_val']/100000000:.1f}亿")
            print(f"PE: {row['pe_ratio']:.1f} | PB: {row['pb_ratio']:.1f}")
            print(f"换手: {row['turnover_rate']:.2f}% | 涨跌: {row['change_rate']:+.2f}%")
            print(f"成长评分: {total_score:.1f}/100")
            print("-" * 50)
    
    quote_ctx.close()

screen_growth_stocks()
```

### 价值股筛选策略

```python
from futu import *

def screen_value_stocks():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 价值股筛选条件
    filter_list = [
        # 基本条件
        SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 1.0),  # 价格 >= 1元
        SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 5000000000),  # 市值 >= 50亿
        
        # 价值指标
        SimpleFilter.create(StockField.PE_RATIO, FilterType.GE, 3.0),   # PE >= 3
        SimpleFilter.create(StockField.PE_RATIO, FilterType.LE, 20.0),  # PE <= 20
        SimpleFilter.create(StockField.PB_RATIO, FilterType.GE, 0.3),   # PB >= 0.3
        SimpleFilter.create(StockField.PB_RATIO, FilterType.LE, 2.0),   # PB <= 2
        
        # 股息率（如果有）
        # SimpleFilter.create(StockField.DIVIDEND_YIELD, FilterType.GE, 3.0),
    ]
    
    ret, data = quote_ctx.get_stock_filter(Market.HK, filter_list, num=100)
    
    if ret == RET_OK:
        print("价值股筛选结果:")
        print("=" * 60)
        
        # 计算价值评分
        data['value_score'] = 0
        for index, row in data.iterrows():
            # PE评分 (PE越低越好)
            pe_score = max(0, (20 - row['pe_ratio']) / 17 * 40)
            # PB评分 (PB越低越好)
            pb_score = max(0, (2 - row['pb_ratio']) / 1.7 * 35)
            # 市值评分 (大市值稳定性好)
            mv_score = min(row['market_val'] / 50000000000, 1) * 25
            
            total_score = pe_score + pb_score + mv_score
            data.loc[index, 'value_score'] = total_score
        
        # 按价值评分排序
        data_sorted = data.sort_values('value_score', ascending=False)
        
        for index, row in data_sorted.head(15).iterrows():
            print(f"股票: {row['stock_name']} ({row['code']})")
            print(f"价格: {row['cur_price']:.2f} | 市值: {row['market_val']/100000000:.1f}亿")
            print(f"PE: {row['pe_ratio']:.1f} | PB: {row['pb_ratio']:.1f}")
            print(f"价值评分: {row['value_score']:.1f}/100")
            print("-" * 50)
    
    quote_ctx.close()

screen_value_stocks()
```

### 技术面筛选策略

```python
from futu import *

def screen_technical_stocks():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 技术面筛选条件
    filter_list = [
        # 基本条件
        SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 2.0),  # 价格 >= 2元
        SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 2000000000),  # 市值 >= 20亿
        
        # 技术指标
        SimpleFilter.create(StockField.CHANGE_RATE, FilterType.GE, 2.0),  # 涨幅 >= 2%
        SimpleFilter.create(StockField.CHANGE_RATE, FilterType.LE, 9.9),  # 涨幅 <= 9.9%（避免涨停）
        SimpleFilter.create(StockField.TURNOVER_RATE, FilterType.GE, 3.0),  # 换手率 >= 3%
        SimpleFilter.create(StockField.VOLUME, FilterType.GE, 1000000),  # 成交量 >= 100万股
    ]
    
    ret, data = quote_ctx.get_stock_filter(Market.HK, filter_list, num=100)
    
    if ret == RET_OK:
        print("技术面强势股筛选结果:")
        print("=" * 60)
        
        # 计算技术评分
        for index, row in data.iterrows():
            # 涨幅评分
            change_score = min(row['change_rate'], 10) / 10 * 30
            # 换手率评分 
            turnover_score = min(row['turnover_rate'], 20) / 20 * 30
            # 成交量评分
            volume_score = min(row['volume'] / 10000000, 1) * 25  # 1000万股满分
            # 市值评分
            mv_score = min(row['market_val'] / 20000000000, 1) * 15  # 200亿满分
            
            total_score = change_score + turnover_score + volume_score + mv_score
            data.loc[index, 'tech_score'] = total_score
        
        # 按技术评分排序
        data_sorted = data.sort_values('tech_score', ascending=False)
        
        for index, row in data_sorted.head(20).iterrows():
            print(f"股票: {row['stock_name']} ({row['code']})")
            print(f"价格: {row['cur_price']:.2f} | 涨幅: {row['change_rate']:+.2f}%")
            print(f"换手: {row['turnover_rate']:.2f}% | 成交量: {row['volume']:,.0f}")
            print(f"技术评分: {row['tech_score']:.1f}/100")
            print("-" * 50)
    
    quote_ctx.close()

screen_technical_stocks()
```

### 板块内筛选示例

```python
from futu import *

def screen_stocks_in_plates():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 指定板块筛选
    plate_codes = ["HK.BK1001", "HK.BK1002"]  # 科技板块和金融板块
    
    filter_list = [
        SimpleFilter.create(StockField.CUR_PRICE, FilterType.GE, 5.0),
        SimpleFilter.create(StockField.MARKET_VAL, FilterType.GE, 5000000000),
        SimpleFilter.create(StockField.CHANGE_RATE, FilterType.GE, 0.0),
        SimpleFilter.create(StockField.PE_RATIO, FilterType.LE, 25.0),
    ]
    
    ret, data = quote_ctx.get_stock_filter(
        Market.HK, 
        filter_list, 
        plate_code_list=plate_codes,
        num=50
    )
    
    if ret == RET_OK:
        print("板块内筛选结果:")
        print("=" * 50)
        
        for index, row in data.iterrows():
            print(f"股票: {row['stock_name']} ({row['code']})")
            print(f"价格: {row['cur_price']:.2f} | PE: {row['pe_ratio']:.1f}")
            print(f"市值: {row['market_val']/100000000:.1f}亿")
            print("-" * 40)
    
    quote_ctx.close()

screen_stocks_in_plates()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| cur_price | float | 当前价格 |
| change_val | float | 涨跌额 |
| change_rate | float | 涨跌幅 |
| market_val | float | 市值 |
| pe_ratio | float | 市盈率 |
| pb_ratio | float | 市净率 |
| turnover_rate | float | 换手率 |
| volume | int | 成交量 |
| turnover | float | 成交额 |

## 筛选策略总结

### 成长股策略
- 中等PE/PB比率
- 适度换手率
- 正向涨跌幅
- 中大市值

### 价值股策略  
- 低PE/PB比率
- 大市值为主
- 稳定基本面
- 高股息收益

### 技术面策略
- 强势涨幅
- 高换手率
- 大成交量
- 突破形态

## 应用场景

1. **量化选股**: 系统化筛选投资标的
2. **策略回测**: 验证选股策略效果
3. **风险控制**: 设定筛选边界条件
4. **组合构建**: 构建多样化投资组合

## 注意事项

1. 筛选条件过多可能导致结果为空
2. 建议合理设置筛选范围
3. 需要相应的行情权限
4. 数据更新有一定延迟
5. 单次最多返回200只股票