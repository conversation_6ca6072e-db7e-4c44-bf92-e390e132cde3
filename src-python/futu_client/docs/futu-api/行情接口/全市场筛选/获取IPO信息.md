# 获取IPO信息

> 对应官方文档: `/futu-api-doc/quote/get-ipo-list.html`

## 接口介绍

获取指定市场的IPO信息，包括新股发行、上市时间等详细信息。

## 函数原型

```python
get_ipo_list(market, begin=None, end=None)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场标识 |
| begin | str | 开始时间，格式YYYY-MM-DD |
| end | str | 结束时间，格式YYYY-MM-DD |

## 使用示例

### 获取最近IPO信息

```python
from futu import *
from datetime import datetime, timedelta

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取最近30天的港股IPO信息
end_date = datetime.now().strftime('%Y-%m-%d')
begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

ret, data = quote_ctx.get_ipo_list(Market.HK, begin=begin_date, end=end_date)

if ret == RET_OK:
    print(f"最近30天港股IPO数量: {len(data)}")
    
    for index, row in data.iterrows():
        print(f"股票代码: {row['code']}")
        print(f"股票名称: {row['name']}")
        print(f"上市日期: {row['list_time']}")
        print(f"发行价: {row['issue_price']}")
        print(f"发行数量: {row['issue_vol']}")
        print(f"募资金额: {row['raise_money']}")
        print(f"市值: {row['market_val']}")
        print("-" * 50)
else:
    print('获取IPO信息失败:', data)

quote_ctx.close()
```

### IPO统计分析

```python
from futu import *
import pandas as pd
from datetime import datetime, timedelta

def analyze_ipo_trends():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取过去一年的IPO数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    begin_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股")
    ]
    
    print("IPO市场分析报告")
    print("=" * 60)
    
    for market, market_name in markets:
        ret, data = quote_ctx.get_ipo_list(market, begin=begin_date, end=end_date)
        
        if ret == RET_OK and not data.empty:
            print(f"\n{market_name}市场 ({begin_date} 至 {end_date}):")
            print("-" * 40)
            
            # 基本统计
            print(f"IPO数量: {len(data)}")
            print(f"总募资金额: {data['raise_money'].sum():,.0f}")
            print(f"平均募资金额: {data['raise_money'].mean():,.0f}")
            print(f"平均发行价: {data['issue_price'].mean():.2f}")
            
            # 按月分组分析
            data['list_time'] = pd.to_datetime(data['list_time'])
            monthly_stats = data.groupby(data['list_time'].dt.to_period('M')).agg({
                'code': 'count',
                'raise_money': 'sum',
                'issue_price': 'mean'
            }).rename(columns={'code': 'ipo_count'})
            
            print(f"\n月度IPO趋势:")
            for month, row in monthly_stats.iterrows():
                print(f"  {month}: {row['ipo_count']}只, 募资{row['raise_money']:,.0f}")
            
            # 大型IPO分析
            large_ipos = data[data['raise_money'] > data['raise_money'].quantile(0.8)]
            if not large_ipos.empty:
                print(f"\n大型IPO (前20%):")
                for index, row in large_ipos.iterrows():
                    print(f"  {row['name']}: 募资{row['raise_money']:,.0f}")
    
    quote_ctx.close()

analyze_ipo_trends()
```

### IPO投资机会分析

```python
from futu import *
import pandas as pd
from datetime import datetime, timedelta

def analyze_ipo_opportunities():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取未来30天的IPO信息
    begin_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    
    ret, data = quote_ctx.get_ipo_list(Market.HK, begin=begin_date, end=end_date)
    
    if ret == RET_OK and not data.empty:
        print("即将上市的IPO投资机会分析:")
        print("=" * 60)
        
        # 按上市时间排序
        data['list_time'] = pd.to_datetime(data['list_time'])
        data_sorted = data.sort_values('list_time')
        
        for index, row in data_sorted.iterrows():
            print(f"\n股票: {row['name']} ({row['code']})")
            print(f"上市时间: {row['list_time'].strftime('%Y-%m-%d')}")
            print(f"发行价: {row['issue_price']:.2f}")
            print(f"募资金额: {row['raise_money']:,.0f}")
            print(f"预计市值: {row['market_val']:,.0f}")
            
            # 投资价值评估
            pe_ratio = row['market_val'] / row['raise_money'] if row['raise_money'] > 0 else 0
            
            # 规模评级
            if row['market_val'] > 50000000000:  # 500亿以上
                size_rating = "超大型"
            elif row['market_val'] > 10000000000:  # 100亿以上
                size_rating = "大型"
            elif row['market_val'] > 1000000000:  # 10亿以上
                size_rating = "中型"
            else:
                size_rating = "小型"
            
            print(f"规模评级: {size_rating}")
            print(f"市值/募资比: {pe_ratio:.2f}")
            
            # 投资建议
            if row['market_val'] > 10000000000 and pe_ratio < 20:
                suggestion = "值得关注"
            elif row['market_val'] > 1000000000 and pe_ratio < 30:
                suggestion = "可以考虑"
            else:
                suggestion = "风险较高"
            
            print(f"投资建议: {suggestion}")
            print("-" * 50)
    else:
        print("未来30天暂无IPO信息")
    
    quote_ctx.close()

analyze_ipo_opportunities()
```

### IPO表现跟踪

```python
from futu import *
import pandas as pd
from datetime import datetime, timedelta

def track_ipo_performance():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取过去3个月的IPO
    end_date = datetime.now().strftime('%Y-%m-%d')
    begin_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    ret, ipo_data = quote_ctx.get_ipo_list(Market.HK, begin=begin_date, end=end_date)
    
    if ret == RET_OK and not ipo_data.empty:
        print("IPO表现跟踪分析:")
        print("=" * 60)
        
        performance_data = []
        
        for index, row in ipo_data.iterrows():
            stock_code = row['code']
            issue_price = row['issue_price']
            
            # 获取当前价格
            ret_quote, quote_data = quote_ctx.get_stock_quote([stock_code])
            
            if ret_quote == RET_OK and not quote_data.empty:
                current_price = quote_data.iloc[0]['cur_price']
                
                # 计算表现
                performance = (current_price - issue_price) / issue_price * 100
                
                performance_data.append({
                    'code': stock_code,
                    'name': row['name'],
                    'list_time': row['list_time'],
                    'issue_price': issue_price,
                    'current_price': current_price,
                    'performance': performance,
                    'market_val': row['market_val']
                })
        
        if performance_data:
            perf_df = pd.DataFrame(performance_data)
            
            # 按表现排序
            perf_df = perf_df.sort_values('performance', ascending=False)
            
            print("IPO表现排名:")
            for index, row in perf_df.iterrows():
                print(f"{row['name']} ({row['code']})")
                print(f"  上市时间: {row['list_time']}")
                print(f"  发行价: {row['issue_price']:.2f}")
                print(f"  现价: {row['current_price']:.2f}")
                print(f"  表现: {row['performance']:+.2f}%")
                print("-" * 40)
            
            # 统计分析
            print("\n整体表现统计:")
            print(f"平均表现: {perf_df['performance'].mean():+.2f}%")
            print(f"最佳表现: {perf_df['performance'].max():+.2f}%")
            print(f"最差表现: {perf_df['performance'].min():+.2f}%")
            print(f"正收益比例: {(perf_df['performance'] > 0).mean()*100:.1f}%")
    
    quote_ctx.close()

track_ipo_performance()
```

### IPO日历提醒

```python
from futu import *
import pandas as pd
from datetime import datetime, timedelta

def create_ipo_calendar():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取未来两个月的IPO信息
    begin_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d')
    
    ret, data = quote_ctx.get_ipo_list(Market.HK, begin=begin_date, end=end_date)
    
    if ret == RET_OK and not data.empty:
        print("IPO日历 (未来60天):")
        print("=" * 60)
        
        # 按日期分组
        data['list_time'] = pd.to_datetime(data['list_time'])
        data_sorted = data.sort_values('list_time')
        
        current_date = None
        for index, row in data_sorted.iterrows():
            list_date = row['list_time'].strftime('%Y-%m-%d')
            
            if current_date != list_date:
                current_date = list_date
                print(f"\n📅 {current_date}:")
                print("-" * 30)
            
            print(f"  🏢 {row['name']} ({row['code']})")
            print(f"     发行价: {row['issue_price']:.2f}")
            print(f"     募资: {row['raise_money']:,.0f}")
            print(f"     市值: {row['market_val']:,.0f}")
            
            # 计算距离上市天数
            days_to_list = (row['list_time'] - datetime.now()).days
            if days_to_list <= 7:
                print(f"     ⚠️  即将上市 ({days_to_list}天)")
            elif days_to_list <= 14:
                print(f"     📋 准备关注 ({days_to_list}天)")
            else:
                print(f"     📝 预备关注 ({days_to_list}天)")
        
        # 创建提醒摘要
        print(f"\n📊 IPO摘要:")
        print(f"未来60天共有 {len(data)} 只新股上市")
        print(f"总募资金额: {data['raise_money'].sum():,.0f}")
        print(f"平均发行价: {data['issue_price'].mean():.2f}")
        
        # 按规模分类
        large_ipos = data[data['market_val'] > 10000000000]
        medium_ipos = data[(data['market_val'] > 1000000000) & (data['market_val'] <= 10000000000)]
        small_ipos = data[data['market_val'] <= 1000000000]
        
        print(f"\n规模分布:")
        print(f"大型IPO (>100亿): {len(large_ipos)}只")
        print(f"中型IPO (10-100亿): {len(medium_ipos)}只")
        print(f"小型IPO (<10亿): {len(small_ipos)}只")
        
    else:
        print("未来60天暂无IPO安排")
    
    quote_ctx.close()

create_ipo_calendar()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| name | str | 股票名称 |
| list_time | str | 上市时间 |
| issue_price | float | 发行价 |
| issue_vol | int | 发行数量 |
| raise_money | float | 募资金额 |
| market_val | float | 市值 |

## 应用场景

1. **IPO投资**: 发现新股投资机会
2. **市场分析**: 分析IPO市场活跃度
3. **风险评估**: 评估新股投资风险
4. **投资组合**: 将新股纳入投资组合

## 注意事项

1. IPO信息可能会发生变化
2. 发行价不等于开盘价
3. 新股上市初期波动较大
4. 需要关注锁定期和解禁时间
5. 建议结合基本面分析