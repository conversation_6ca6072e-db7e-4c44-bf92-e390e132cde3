# 获取板块股票列表

> 对应官方文档: `/futu-api-doc/quote/get-plate-stock.html`

## 接口介绍

获取指定板块的股票列表，支持各种行业板块和概念板块。

## 函数原型

```python
get_plate_stock(plate_code, sort_field=SortField.NONE, ascend=True)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| plate_code | str | 板块代码 |
| sort_field | SortField | 排序字段 |
| ascend | bool | 升序排列 |

## 排序字段

| 类型 | 说明 |
|------|------|
| SortField.NONE | 不排序 |
| SortField.CODE | 按代码排序 |
| SortField.CUR_PRICE | 按当前价格排序 |
| SortField.PRICE_CHANGE_RATIO | 按涨跌幅排序 |
| SortField.MARKET_VAL | 按市值排序 |
| SortField.TURNOVER_RATE | 按换手率排序 |
| SortField.VOLUME | 按成交量排序 |
| SortField.TURNOVER | 按成交额排序 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取科技板块的股票列表
plate_code = "HK.BK1001"  # 科技板块代码
ret, data = quote_ctx.get_plate_stock(plate_code, SortField.MARKET_VAL, ascend=False)

if ret == RET_OK:
    print(f"板块 {plate_code} 的股票列表:")
    print(data)
    
    for index, row in data.iterrows():
        print(f"股票: {row['code']} - {row['stock_name']}")
        print(f"当前价: {row['cur_price']}")
        print(f"涨跌幅: {row['change_rate']:.2f}%")
        print(f"市值: {row['market_val']:,.0f}")
        print(f"换手率: {row['turnover_rate']:.2f}%")
        print("-" * 40)
else:
    print('获取板块股票列表失败:', data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| stock_name | str | 股票名称 |
| cur_price | float | 当前价格 |
| change_val | float | 涨跌额 |
| change_rate | float | 涨跌幅 |
| market_val | float | 市值 |
| turnover_rate | float | 换手率 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| pe_ratio | float | 市盈率 |
| pb_ratio | float | 市净率 |

## 板块分析示例

```python
from futu import *

def analyze_plate_performance(plate_code, plate_name):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_plate_stock(plate_code, SortField.CHANGE_RATE, ascend=False)
    
    if ret == RET_OK:
        print(f"\n{plate_name} 板块分析:")
        print("=" * 50)
        print(f"成分股数量: {len(data)}")
        
        # 涨跌分布
        up_stocks = data[data['change_rate'] > 0]
        down_stocks = data[data['change_rate'] < 0]
        flat_stocks = data[data['change_rate'] == 0]
        
        print(f"上涨: {len(up_stocks)}只 | 下跌: {len(down_stocks)}只 | 平盘: {len(flat_stocks)}只")
        
        # 涨跌幅统计
        avg_change = data['change_rate'].mean()
        max_change = data['change_rate'].max()
        min_change = data['change_rate'].min()
        
        print(f"平均涨跌幅: {avg_change:.2f}%")
        print(f"最大涨幅: {max_change:.2f}%")
        print(f"最大跌幅: {min_change:.2f}%")
        
        # 龙头股票
        print(f"\n板块龙头股:")
        top_stocks = data.nlargest(5, 'market_val')
        for _, row in top_stocks.iterrows():
            print(f"  {row['stock_name']}: 市值{row['market_val']:,.0f} | 涨跌{row['change_rate']:+.2f}%")
        
        # 活跃股票
        print(f"\n活跃股票:")
        active_stocks = data.nlargest(5, 'turnover_rate')
        for _, row in active_stocks.iterrows():
            print(f"  {row['stock_name']}: 换手率{row['turnover_rate']:.2f}%")
        
        # 表现最好的股票
        print(f"\n表现最好:")
        best_stocks = data.nlargest(3, 'change_rate')
        for _, row in best_stocks.iterrows():
            print(f"  {row['stock_name']}: {row['change_rate']:+.2f}%")
    
    quote_ctx.close()

# 分析科技板块
analyze_plate_performance("HK.BK1001", "科技板块")
```

## 板块对比示例

```python
from futu import *

def compare_plates(plate_configs):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    print("板块对比分析:")
    print("=" * 80)
    
    for plate_code, plate_name in plate_configs:
        ret, data = quote_ctx.get_plate_stock(plate_code)
        
        if ret == RET_OK:
            # 计算板块指标
            avg_change = data['change_rate'].mean()
            up_ratio = (len(data[data['change_rate'] > 0]) / len(data)) * 100
            total_market_val = data['market_val'].sum()
            avg_turnover = data['turnover_rate'].mean()
            
            print(f"{plate_name}:")
            print(f"  成分股数: {len(data)}只")
            print(f"  平均涨跌幅: {avg_change:+.2f}%")
            print(f"  上涨比例: {up_ratio:.1f}%")
            print(f"  总市值: {total_market_val:,.0f}")
            print(f"  平均换手率: {avg_turnover:.2f}%")
            
            # 板块强度评估
            if avg_change > 2:
                strength = "强势 🔴"
            elif avg_change > 0:
                strength = "偏强 🟡"
            elif avg_change > -2:
                strength = "偏弱 🔵"
            else:
                strength = "弱势 🟢"
            
            print(f"  板块强度: {strength}")
            print("-" * 40)
    
    quote_ctx.close()

# 对比多个板块
plate_configs = [
    ("HK.BK1001", "科技板块"),
    ("HK.BK1002", "金融板块"),
    ("HK.BK1003", "医疗板块"),
    ("HK.BK1004", "消费板块")
]

compare_plates(plate_configs)
```

## 选股策略示例

```python
from futu import *

def screen_stocks_by_plate(plate_code, criteria):
    """
    根据板块和条件筛选股票
    criteria: 筛选条件字典
    """
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_plate_stock(plate_code)
    
    if ret == RET_OK:
        # 应用筛选条件
        filtered_data = data.copy()
        
        # 市值筛选
        if 'min_market_val' in criteria:
            filtered_data = filtered_data[filtered_data['market_val'] >= criteria['min_market_val']]
        
        # 涨跌幅筛选
        if 'min_change_rate' in criteria:
            filtered_data = filtered_data[filtered_data['change_rate'] >= criteria['min_change_rate']]
        
        # 换手率筛选
        if 'min_turnover_rate' in criteria:
            filtered_data = filtered_data[filtered_data['turnover_rate'] >= criteria['min_turnover_rate']]
        
        # PE筛选
        if 'max_pe_ratio' in criteria:
            filtered_data = filtered_data[filtered_data['pe_ratio'] <= criteria['max_pe_ratio']]
        
        print(f"筛选结果: {len(filtered_data)} / {len(data)} 只股票")
        
        if len(filtered_data) > 0:
            # 按市值排序
            filtered_data = filtered_data.sort_values('market_val', ascending=False)
            
            print("符合条件的股票:")
            for _, row in filtered_data.head(10).iterrows():
                print(f"  {row['stock_name']}: 价格{row['cur_price']:.2f} | "
                      f"涨幅{row['change_rate']:+.2f}% | "
                      f"市值{row['market_val']:,.0f} | "
                      f"PE{row['pe_ratio']:.1f}")
        
        return filtered_data
    
    quote_ctx.close()
    return None

# 使用筛选策略
criteria = {
    'min_market_val': 10000000000,  # 100亿以上市值
    'min_change_rate': 1.0,         # 涨幅1%以上
    'min_turnover_rate': 2.0,       # 换手率2%以上
    'max_pe_ratio': 30              # PE小于30
}

screen_stocks_by_plate("HK.BK1001", criteria)
```

## 应用场景

1. **板块分析**: 分析板块整体表现和成分股分布
2. **选股策略**: 在特定板块内筛选符合条件的股票
3. **板块轮动**: 跟踪不同板块的强弱变化
4. **风险管理**: 了解板块内股票的风险特征

## 注意事项

1. 板块代码需要从板块列表接口获取
2. 排序功能可以快速找到板块内的优质股票
3. 建议结合市场快照数据进行综合分析
4. 板块成分股可能随时间变化