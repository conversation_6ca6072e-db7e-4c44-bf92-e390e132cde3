# 获取股票基本信息

> 对应官方文档: `/futu-api-doc/quote/get-stock-basicinfo.html`

## 接口介绍

获取股票的基本信息，包括股票名称、行业、市值、财务指标等详细信息。

## 函数原型

```python
get_stock_basicinfo(market, stock_type=SecurityType.STOCK, code_list=None)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场标识 |
| stock_type | SecurityType | 股票类型，默认为普通股票 |
| code_list | list | 股票代码列表，可选 |

## 股票类型

| 类型 | 说明 |
|------|------|
| SecurityType.STOCK | 普通股票 |
| SecurityType.ETF | 交易所交易基金 |
| SecurityType.WARRANT | 窝轮 |
| SecurityType.BOND | 债券 |

## 使用示例

### 获取所有股票基本信息

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股所有股票基本信息
ret, data = quote_ctx.get_stock_basicinfo(Market.HK)

if ret == RET_OK:
    print(f"共获取 {len(data)} 只股票信息")
    print(data.head())
    
    # 显示前10只股票的详细信息
    for index, row in data.head(10).iterrows():
        print(f"股票: {row['code']} - {row['name']}")
        print(f"所属行业: {row['industry']}")
        print(f"市值: {row['market_val']:,.0f}")
        print(f"股本: {row['equity']:,.0f}股")
        print(f"每手股数: {row['lot_size']}")
        print(f"上市日期: {row['list_time']}")
        print("-" * 50)
else:
    print('获取股票基本信息失败:', data)

quote_ctx.close()
```

### 获取指定股票信息

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取指定股票列表的基本信息
stock_codes = ['HK.00700', 'HK.00005', 'HK.00941', 'HK.00388']

ret, data = quote_ctx.get_stock_basicinfo(Market.HK, code_list=stock_codes)

if ret == RET_OK:
    print("指定股票基本信息:")
    print("=" * 80)
    
    for index, row in data.iterrows():
        print(f"股票代码: {row['code']}")
        print(f"股票名称: {row['name']}")
        print(f"行业分类: {row['industry']}")
        print(f"市值: {row['market_val']:,.0f}")
        print(f"流通股本: {row['circulation_equity']:,.0f}股")
        print(f"总股本: {row['equity']:,.0f}股")
        print(f"每手股数: {row['lot_size']}")
        print(f"上市时间: {row['list_time']}")
        print(f"所属板块: {row['plate_list']}")
        print("-" * 70)
else:
    print('获取指定股票信息失败:', data)

quote_ctx.close()
```

### 行业分析示例

```python
from futu import *
import pandas as pd

def analyze_industry_distribution():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_stock_basicinfo(Market.HK)
    
    if ret == RET_OK:
        print("港股行业分布分析:")
        print("=" * 60)
        
        # 行业分布统计
        industry_stats = data.groupby('industry').agg({
            'code': 'count',
            'market_val': ['sum', 'mean', 'median'],
            'equity': 'sum'
        }).round(2)
        
        industry_stats.columns = ['股票数量', '总市值', '平均市值', '中位数市值', '总股本']
        industry_stats = industry_stats.sort_values('总市值', ascending=False)
        
        print("按总市值排序的行业分布:")
        print(industry_stats.head(15))
        
        # 大市值股票分布
        large_cap = data[data['market_val'] > 10000000000]  # 市值>100亿
        print(f"\n大市值股票(>100亿)行业分布:")
        large_cap_industry = large_cap.groupby('industry').size().sort_values(ascending=False)
        print(large_cap_industry.head(10))
        
        # 新上市股票分析
        data['list_time'] = pd.to_datetime(data['list_time'])
        recent_ipo = data[data['list_time'] > '2020-01-01']
        print(f"\n2020年后上市股票行业分布:")
        recent_industry = recent_ipo.groupby('industry').size().sort_values(ascending=False)
        print(recent_industry.head(10))
    
    quote_ctx.close()

analyze_industry_distribution()
```

### 股票筛选和分析

```python
from futu import *

def screen_and_analyze_stocks():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_stock_basicinfo(Market.HK)
    
    if ret == RET_OK:
        print("股票筛选和分析:")
        print("=" * 60)
        
        # 筛选条件
        filtered_data = data[
            (data['market_val'] > 5000000000) &  # 市值>50亿
            (data['lot_size'] <= 1000) &         # 每手股数<=1000
            (data['list_time'] < '2020-01-01')   # 2020年前上市
        ]
        
        print(f"筛选出 {len(filtered_data)} 只符合条件的股票")
        
        # 按市值排序
        top_stocks = filtered_data.nlargest(20, 'market_val')
        
        print("\n市值排名前20的股票:")
        for index, row in top_stocks.iterrows():
            # 计算流通比例
            circulation_ratio = (row['circulation_equity'] / row['equity']) * 100
            
            print(f"排名 {index+1}: {row['name']} ({row['code']})")
            print(f"  市值: {row['market_val']/100000000:.1f}亿")
            print(f"  行业: {row['industry']}")
            print(f"  流通比例: {circulation_ratio:.1f}%")
            print(f"  上市时间: {row['list_time']}")
            print("-" * 50)
        
        # 行业集中度分析
        print("\n行业集中度分析:")
        industry_concentration = top_stocks.groupby('industry').agg({
            'market_val': ['sum', 'count']
        })
        industry_concentration.columns = ['总市值', '股票数量']
        industry_concentration = industry_concentration.sort_values('总市值', ascending=False)
        
        for industry, row in industry_concentration.iterrows():
            print(f"{industry}: {row['股票数量']}只股票, 总市值{row['总市值']/100000000:.1f}亿")
    
    quote_ctx.close()

screen_and_analyze_stocks()
```

### ETF基本信息获取

```python
from futu import *

def get_etf_info():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取ETF基本信息
    ret, data = quote_ctx.get_stock_basicinfo(Market.HK, stock_type=SecurityType.ETF)
    
    if ret == RET_OK:
        print("港股ETF基本信息:")
        print("=" * 70)
        
        # 按市值排序
        etf_sorted = data.sort_values('market_val', ascending=False)
        
        for index, row in etf_sorted.head(15).iterrows():
            print(f"ETF: {row['name']} ({row['code']})")
            print(f"市值: {row['market_val']/100000000:.1f}亿")
            print(f"总股本: {row['equity']:,.0f}股")
            print(f"每手股数: {row['lot_size']}")
            print(f"上市时间: {row['list_time']}")
            print("-" * 60)
        
        # ETF规模分析
        total_etf_market_val = data['market_val'].sum()
        avg_etf_market_val = data['market_val'].mean()
        
        print(f"\nETF市场统计:")
        print(f"ETF总数: {len(data)}")
        print(f"ETF总市值: {total_etf_market_val/100000000:.1f}亿")
        print(f"ETF平均市值: {avg_etf_market_val/100000000:.1f}亿")
        
        # 按规模分类
        large_etf = data[data['market_val'] > 1000000000]  # >10亿
        medium_etf = data[(data['market_val'] > 100000000) & (data['market_val'] <= 1000000000)]  # 1-10亿
        small_etf = data[data['market_val'] <= 100000000]  # <=1亿
        
        print(f"\n按规模分类:")
        print(f"大型ETF(>10亿): {len(large_etf)}只")
        print(f"中型ETF(1-10亿): {len(medium_etf)}只")
        print(f"小型ETF(<=1亿): {len(small_etf)}只")
    
    quote_ctx.close()

get_etf_info()
```

### 股票对比分析

```python
from futu import *

def compare_stocks(stock_codes):
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_stock_basicinfo(Market.HK, code_list=stock_codes)
    
    if ret == RET_OK:
        print("股票对比分析:")
        print("=" * 80)
        
        for index, row in data.iterrows():
            # 计算关键指标
            circulation_ratio = (row['circulation_equity'] / row['equity']) * 100
            
            print(f"股票: {row['name']} ({row['code']})")
            print(f"行业: {row['industry']}")
            print(f"市值: {row['market_val']/100000000:.1f}亿")
            print(f"总股本: {row['equity']/100000000:.1f}亿股")
            print(f"流通股本: {row['circulation_equity']/100000000:.1f}亿股")
            print(f"流通比例: {circulation_ratio:.1f}%")
            print(f"每手股数: {row['lot_size']}")
            print(f"上市时间: {row['list_time']}")
            
            # 股票特征分析
            if row['market_val'] > 50000000000:
                size_category = "大盘股"
            elif row['market_val'] > 10000000000:
                size_category = "中盘股"
            else:
                size_category = "小盘股"
            
            liquidity = "高" if circulation_ratio > 80 else "中" if circulation_ratio > 50 else "低"
            
            print(f"规模类别: {size_category}")
            print(f"流通性: {liquidity}")
            print("=" * 70)
        
        # 对比总结
        if len(data) > 1:
            print("\n对比总结:")
            max_mv = data.loc[data['market_val'].idxmax()]
            min_mv = data.loc[data['market_val'].idxmin()]
            
            print(f"最大市值: {max_mv['name']} - {max_mv['market_val']/100000000:.1f}亿")
            print(f"最小市值: {min_mv['name']} - {min_mv['market_val']/100000000:.1f}亿")
            
            # 行业分布
            industries = data['industry'].value_counts()
            print(f"行业分布: {dict(industries)}")
    
    quote_ctx.close()

# 对比腾讯、阿里、美团、小米
compare_stocks(['HK.00700', 'HK.09988', 'HK.03690', 'HK.01810'])
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| name | str | 股票名称 |
| industry | str | 所属行业 |
| market_val | float | 市值 |
| equity | int | 总股本 |
| circulation_equity | int | 流通股本 |
| lot_size | int | 每手股数 |
| list_time | str | 上市时间 |
| plate_list | str | 所属板块列表 |

## 应用场景

1. **基本面分析**: 了解股票基本属性和财务特征
2. **行业研究**: 分析行业分布和集中度
3. **投资筛选**: 根据基本信息筛选投资标的
4. **风险评估**: 评估股票规模、流通性等风险因素
5. **组合构建**: 构建多样化的投资组合

## 注意事项

1. 数据更新频率为日级别
2. 部分新上市股票可能信息不完整
3. 市值数据基于最新股价计算
4. 需要相应的行情权限
5. 建议结合实时行情数据使用