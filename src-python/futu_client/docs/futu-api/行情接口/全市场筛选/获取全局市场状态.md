# 获取全局市场状态

> 对应官方文档: `/futu-api-doc/quote/get-global-state.html`

## 接口介绍

获取全局市场状态信息，包括各市场的交易状态、开盘收盘时间等。

## 函数原型

```python
get_global_state()
```

## 参数说明

该接口无需参数。

## 使用示例

### 获取全局市场状态

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取全局市场状态
ret, data = quote_ctx.get_global_state()

if ret == RET_OK:
    print("全局市场状态:")
    print(data)
    
    for index, row in data.iterrows():
        print(f"市场: {row['market_name']}")
        print(f"交易状态: {row['market_state']}")
        print(f"开盘时间: {row['open_time']}")
        print(f"收盘时间: {row['close_time']}")
        print(f"时区: {row['timezone']}")
        print("-" * 40)
else:
    print('获取全局市场状态失败:', data)

quote_ctx.close()
```

### 市场状态监控

```python
from futu import *
import time
from datetime import datetime

def monitor_market_status():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    print("市场状态监控:")
    print("=" * 60)
    
    while True:
        ret, data = quote_ctx.get_global_state()
        
        if ret == RET_OK:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"\n更新时间: {current_time}")
            print("-" * 40)
            
            for index, row in data.iterrows():
                market_name = row['market_name']
                market_state = row['market_state']
                
                # 状态映射
                state_map = {
                    0: "未开盘",
                    1: "开盘中",
                    2: "休市",
                    3: "已收盘",
                    4: "盘前交易",
                    5: "盘后交易"
                }
                
                state_desc = state_map.get(market_state, "未知状态")
                
                # 状态指示器
                if market_state == 1:  # 开盘中
                    indicator = "🟢"
                elif market_state in [4, 5]:  # 盘前盘后
                    indicator = "🟡"
                else:  # 未开盘、休市、已收盘
                    indicator = "🔴"
                
                print(f"{indicator} {market_name}: {state_desc}")
                print(f"   开盘: {row['open_time']} | 收盘: {row['close_time']}")
        
        # 等待5分钟后再次检查
        time.sleep(300)
    
    quote_ctx.close()

# 运行监控 (注意：这会持续运行)
# monitor_market_status()
```

### 交易时间规划

```python
from futu import *
from datetime import datetime, timedelta

def plan_trading_schedule():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    ret, data = quote_ctx.get_global_state()
    
    if ret == RET_OK:
        print("交易时间规划:")
        print("=" * 60)
        
        current_time = datetime.now()
        
        for index, row in data.iterrows():
            market_name = row['market_name']
            market_state = row['market_state']
            open_time = row['open_time']
            close_time = row['close_time']
            
            print(f"\n{market_name}:")
            print(f"当前状态: {market_state}")
            print(f"交易时间: {open_time} - {close_time}")
            print(f"时区: {row['timezone']}")
            
            # 计算距离下次开盘/收盘的时间
            if market_state == 1:  # 开盘中
                print(f"💹 市场开盘中，距离收盘还有时间")
            elif market_state == 0:  # 未开盘
                print(f"⏰ 市场未开盘，等待开盘")
            elif market_state == 3:  # 已收盘
                print(f"🏁 市场已收盘，等待明日开盘")
            elif market_state == 4:  # 盘前交易
                print(f"🌅 盘前交易时间")
            elif market_state == 5:  # 盘后交易
                print(f"🌆 盘后交易时间")
            
            print("-" * 40)
        
        # 生成交易建议
        print("\n📋 交易建议:")
        
        # 检查哪些市场正在交易
        open_markets = data[data['market_state'] == 1]
        if not open_markets.empty:
            print("🟢 当前可交易市场:")
            for index, row in open_markets.iterrows():
                print(f"  • {row['market_name']}")
        
        # 检查即将开盘的市场
        upcoming_markets = data[data['market_state'] == 0]
        if not upcoming_markets.empty:
            print("⏰ 即将开盘市场:")
            for index, row in upcoming_markets.iterrows():
                print(f"  • {row['market_name']} - {row['open_time']}")
        
        # 检查盘前盘后交易
        extended_markets = data[data['market_state'].isin([4, 5])]
        if not extended_markets.empty:
            print("🟡 盘前盘后交易:")
            for index, row in extended_markets.iterrows():
                state_desc = "盘前" if row['market_state'] == 4 else "盘后"
                print(f"  • {row['market_name']} - {state_desc}")
    
    quote_ctx.close()

plan_trading_schedule()
```

### 全球市场时钟

```python
from futu import *
import time
from datetime import datetime

def create_global_market_clock():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    print("全球市场时钟")
    print("=" * 80)
    
    while True:
        ret, data = quote_ctx.get_global_state()
        
        if ret == RET_OK:
            # 清屏显示
            print("\033[H\033[J")  # ANSI转义序列清屏
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            print(f"全球市场时钟 - 更新时间: {current_time}")
            print("=" * 80)
            
            # 按市场状态分组显示
            open_markets = []
            closed_markets = []
            extended_markets = []
            
            for index, row in data.iterrows():
                market_info = {
                    'name': row['market_name'],
                    'state': row['market_state'],
                    'open_time': row['open_time'],
                    'close_time': row['close_time'],
                    'timezone': row['timezone']
                }
                
                if row['market_state'] == 1:  # 开盘中
                    open_markets.append(market_info)
                elif row['market_state'] in [4, 5]:  # 盘前盘后
                    extended_markets.append(market_info)
                else:  # 其他状态
                    closed_markets.append(market_info)
            
            # 显示开盘市场
            if open_markets:
                print("\n🟢 开盘中的市场:")
                for market in open_markets:
                    print(f"  📈 {market['name']} | {market['open_time']} - {market['close_time']} | {market['timezone']}")
            
            # 显示盘前盘后市场
            if extended_markets:
                print("\n🟡 盘前盘后交易:")
                for market in extended_markets:
                    status = "盘前" if market['state'] == 4 else "盘后"
                    print(f"  📊 {market['name']} ({status}) | {market['timezone']}")
            
            # 显示休市市场
            if closed_markets:
                print("\n🔴 休市中的市场:")
                for market in closed_markets:
                    print(f"  💤 {market['name']} | {market['open_time']} - {market['close_time']} | {market['timezone']}")
            
            # 显示统计信息
            total_markets = len(data)
            active_markets = len(open_markets) + len(extended_markets)
            print(f"\n📊 市场统计: {active_markets}/{total_markets} 市场活跃")
            
            # 显示时间提示
            print(f"\n⏰ 当前时间: {current_time}")
            print("按 Ctrl+C 退出监控")
            
        # 等待30秒后更新
        time.sleep(30)
    
    quote_ctx.close()

# 运行全球市场时钟 (注意：这会持续运行)
# create_global_market_clock()
```

### 市场状态警报

```python
from futu import *
import time
from datetime import datetime

class MarketStatusAlert:
    def __init__(self):
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.previous_states = {}
        self.alert_log = []
    
    def check_market_changes(self):
        """检查市场状态变化"""
        ret, data = self.quote_ctx.get_global_state()
        
        if ret == RET_OK:
            current_states = {}
            
            for index, row in data.iterrows():
                market_name = row['market_name']
                market_state = row['market_state']
                current_states[market_name] = market_state
                
                # 检查状态变化
                if market_name in self.previous_states:
                    prev_state = self.previous_states[market_name]
                    
                    if prev_state != market_state:
                        self.trigger_alert(market_name, prev_state, market_state)
                
                self.previous_states[market_name] = market_state
    
    def trigger_alert(self, market_name, prev_state, new_state):
        """触发状态变化警报"""
        state_map = {
            0: "未开盘",
            1: "开盘中",
            2: "休市",
            3: "已收盘",
            4: "盘前交易",
            5: "盘后交易"
        }
        
        prev_desc = state_map.get(prev_state, "未知")
        new_desc = state_map.get(new_state, "未知")
        
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        alert_msg = f"[{timestamp}] 🚨 {market_name}: {prev_desc} → {new_desc}"
        
        print(alert_msg)
        self.alert_log.append(alert_msg)
        
        # 特殊警报
        if new_state == 1:  # 开盘
            print(f"🔔 {market_name} 开盘提醒！")
        elif new_state == 3:  # 收盘
            print(f"🔔 {market_name} 收盘提醒！")
    
    def start_monitoring(self):
        """开始监控"""
        print("市场状态监控启动")
        print("=" * 50)
        
        try:
            while True:
                self.check_market_changes()
                time.sleep(60)  # 每分钟检查一次
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.show_alert_summary()
    
    def show_alert_summary(self):
        """显示警报摘要"""
        print("\n警报摘要:")
        print("=" * 50)
        
        if self.alert_log:
            for alert in self.alert_log:
                print(alert)
        else:
            print("无状态变化警报")
    
    def close(self):
        self.quote_ctx.close()

# 使用示例
# alert_system = MarketStatusAlert()
# alert_system.start_monitoring()
# alert_system.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| market_name | str | 市场名称 |
| market_state | int | 市场状态 |
| open_time | str | 开盘时间 |
| close_time | str | 收盘时间 |
| timezone | str | 时区 |

## 市场状态说明

| 状态值 | 说明 |
|--------|------|
| 0 | 未开盘 |
| 1 | 开盘中 |
| 2 | 休市 |
| 3 | 已收盘 |
| 4 | 盘前交易 |
| 5 | 盘后交易 |

## 应用场景

1. **交易时间规划**: 合理安排交易时间
2. **全球投资**: 跨市场投资时间管理
3. **自动化交易**: 根据市场状态控制交易程序
4. **风险管理**: 及时了解市场开闭状态

## 注意事项

1. 时区差异需要特别注意
2. 节假日可能影响交易时间
3. 部分市场有盘前盘后交易
4. 建议定期刷新获取最新状态
5. 不同市场的交易规则可能不同