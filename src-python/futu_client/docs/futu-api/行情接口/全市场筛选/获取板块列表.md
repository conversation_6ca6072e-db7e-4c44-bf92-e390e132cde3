# 获取板块列表

> 对应官方文档: `/futu-api-doc/quote/get-plate-list.html`

## 接口介绍

获取指定市场的板块列表，包括行业板块和概念板块。

## 函数原型

```python
get_plate_list(market, plate_class)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场标识 |
| plate_class | PlateClass | 板块分类 |

## 板块分类

| 类型 | 说明 |
|------|------|
| PlateClass.INDUSTRY | 行业板块 |
| PlateClass.CONCEPT | 概念板块 |
| PlateClass.AREA | 地域板块 |

## 使用示例

### 获取行业板块列表

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股行业板块列表
ret, data = quote_ctx.get_plate_list(Market.HK, PlateClass.INDUSTRY)

if ret == RET_OK:
    print(f"港股行业板块数量: {len(data)}")
    print(data)
    
    for index, row in data.iterrows():
        print(f"板块代码: {row['plate_code']}")
        print(f"板块名称: {row['plate_name']}")
        print(f"板块ID: {row['plate_id']}")
        print("-" * 40)
else:
    print('获取行业板块列表失败:', data)

quote_ctx.close()
```

### 获取概念板块列表

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股概念板块列表
ret, data = quote_ctx.get_plate_list(Market.HK, PlateClass.CONCEPT)

if ret == RET_OK:
    print(f"港股概念板块数量: {len(data)}")
    
    for index, row in data.iterrows():
        print(f"概念板块: {row['plate_name']} ({row['plate_code']})")
        print(f"板块ID: {row['plate_id']}")
        print("-" * 40)
else:
    print('获取概念板块列表失败:', data)

quote_ctx.close()
```

### 多市场板块对比

```python
from futu import *

def compare_market_plates():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股"),
        (Market.CN, "A股")
    ]
    
    print("各市场板块数量对比:")
    print("=" * 60)
    
    for market, market_name in markets:
        print(f"\n{market_name}市场:")
        
        # 行业板块
        ret_industry, data_industry = quote_ctx.get_plate_list(market, PlateClass.INDUSTRY)
        if ret_industry == RET_OK:
            print(f"  行业板块: {len(data_industry)}个")
            
            # 显示前5个行业板块
            print("  主要行业板块:")
            for index, row in data_industry.head(5).iterrows():
                print(f"    {row['plate_name']}")
        
        # 概念板块
        ret_concept, data_concept = quote_ctx.get_plate_list(market, PlateClass.CONCEPT)
        if ret_concept == RET_OK:
            print(f"  概念板块: {len(data_concept)}个")
            
            # 显示前3个概念板块
            print("  主要概念板块:")
            for index, row in data_concept.head(3).iterrows():
                print(f"    {row['plate_name']}")
        
        print("-" * 50)
    
    quote_ctx.close()

compare_market_plates()
```

### 板块代码管理

```python
from futu import *

class PlateManager:
    def __init__(self):
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.plate_cache = {}
    
    def get_all_plates(self, market):
        """获取指定市场的所有板块"""
        if market not in self.plate_cache:
            self.plate_cache[market] = {}
            
            # 获取行业板块
            ret_industry, data_industry = self.quote_ctx.get_plate_list(market, PlateClass.INDUSTRY)
            if ret_industry == RET_OK:
                self.plate_cache[market]['industry'] = data_industry
            
            # 获取概念板块
            ret_concept, data_concept = self.quote_ctx.get_plate_list(market, PlateClass.CONCEPT)
            if ret_concept == RET_OK:
                self.plate_cache[market]['concept'] = data_concept
        
        return self.plate_cache[market]
    
    def find_plate_by_name(self, market, plate_name):
        """根据板块名称查找板块代码"""
        plates = self.get_all_plates(market)
        
        for plate_type, data in plates.items():
            matching_plates = data[data['plate_name'].str.contains(plate_name, case=False)]
            if not matching_plates.empty:
                return matching_plates
        
        return None
    
    def get_plate_code(self, market, plate_name):
        """获取板块代码"""
        matching_plates = self.find_plate_by_name(market, plate_name)
        
        if matching_plates is not None and len(matching_plates) > 0:
            return matching_plates.iloc[0]['plate_code']
        
        return None
    
    def list_plates_by_keyword(self, market, keyword):
        """根据关键词列出相关板块"""
        plates = self.get_all_plates(market)
        
        print(f"包含关键词 '{keyword}' 的板块:")
        print("=" * 50)
        
        for plate_type, data in plates.items():
            matching_plates = data[data['plate_name'].str.contains(keyword, case=False)]
            
            if not matching_plates.empty:
                print(f"\n{plate_type.upper()}板块:")
                for index, row in matching_plates.iterrows():
                    print(f"  {row['plate_name']} ({row['plate_code']})")
    
    def close(self):
        self.quote_ctx.close()

# 使用示例
plate_manager = PlateManager()

# 查找科技相关板块
plate_manager.list_plates_by_keyword(Market.HK, "科技")

# 获取具体板块代码
tech_plate_code = plate_manager.get_plate_code(Market.HK, "科技")
print(f"\n科技板块代码: {tech_plate_code}")

# 查找金融相关板块
plate_manager.list_plates_by_keyword(Market.HK, "金融")

plate_manager.close()
```

### 板块分析工具

```python
from futu import *

def analyze_plate_structure():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    print("板块结构分析:")
    print("=" * 60)
    
    # 港股板块分析
    print("\n港股板块分析:")
    
    # 行业板块
    ret_industry, data_industry = quote_ctx.get_plate_list(Market.HK, PlateClass.INDUSTRY)
    if ret_industry == RET_OK:
        print(f"行业板块总数: {len(data_industry)}")
        
        # 按名称长度分析
        name_lengths = data_industry['plate_name'].str.len()
        print(f"板块名称长度分布: 平均{name_lengths.mean():.1f}字符")
        print(f"最长板块名称: {data_industry.loc[name_lengths.idxmax(), 'plate_name']}")
        print(f"最短板块名称: {data_industry.loc[name_lengths.idxmin(), 'plate_name']}")
        
        # 分析包含特定关键词的板块
        keywords = ["科技", "金融", "医疗", "消费", "地产"]
        print("\n关键词分布:")
        for keyword in keywords:
            count = data_industry['plate_name'].str.contains(keyword).sum()
            print(f"  {keyword}: {count}个板块")
    
    # 概念板块
    ret_concept, data_concept = quote_ctx.get_plate_list(Market.HK, PlateClass.CONCEPT)
    if ret_concept == RET_OK:
        print(f"\n概念板块总数: {len(data_concept)}")
        
        # 热门概念关键词
        concept_keywords = ["新能源", "人工智能", "区块链", "5G", "芯片"]
        print("\n热门概念分布:")
        for keyword in concept_keywords:
            count = data_concept['plate_name'].str.contains(keyword).sum()
            print(f"  {keyword}: {count}个板块")
    
    quote_ctx.close()

analyze_plate_structure()
```

### 板块代码导出

```python
from futu import *
import pandas as pd

def export_plate_codes():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    all_plates = []
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股"),
        (Market.CN, "A股")
    ]
    
    for market, market_name in markets:
        # 行业板块
        ret_industry, data_industry = quote_ctx.get_plate_list(market, PlateClass.INDUSTRY)
        if ret_industry == RET_OK:
            data_industry['market'] = market_name
            data_industry['plate_type'] = '行业板块'
            all_plates.append(data_industry)
        
        # 概念板块
        ret_concept, data_concept = quote_ctx.get_plate_list(market, PlateClass.CONCEPT)
        if ret_concept == RET_OK:
            data_concept['market'] = market_name
            data_concept['plate_type'] = '概念板块'
            all_plates.append(data_concept)
    
    # 合并所有数据
    if all_plates:
        combined_data = pd.concat(all_plates, ignore_index=True)
        
        # 重新排列列的顺序
        columns_order = ['market', 'plate_type', 'plate_name', 'plate_code', 'plate_id']
        combined_data = combined_data[columns_order]
        
        # 保存到CSV文件
        combined_data.to_csv('plate_codes.csv', index=False, encoding='utf-8-sig')
        print(f"板块代码已导出到 plate_codes.csv，共{len(combined_data)}个板块")
        
        # 显示统计信息
        print("\n板块统计:")
        print(combined_data.groupby(['market', 'plate_type']).size().unstack(fill_value=0))
    
    quote_ctx.close()

export_plate_codes()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| plate_code | str | 板块代码 |
| plate_name | str | 板块名称 |
| plate_id | str | 板块ID |

## 应用场景

1. **板块轮动分析**: 了解不同板块的构成和分类
2. **选股策略**: 根据板块进行股票筛选
3. **投资组合构建**: 按板块分散投资风险
4. **市场研究**: 分析板块间的相关性和趋势

## 注意事项

1. 板块列表相对稳定，但会定期更新
2. 不同市场的板块分类可能不同
3. 板块代码格式因市场而异
4. 建议缓存板块列表减少API调用