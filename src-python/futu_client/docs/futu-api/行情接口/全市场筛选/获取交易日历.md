# 获取交易日历

> 对应官方文档: `/futu-api-doc/quote/request-trading-days.html`

## 接口介绍

获取指定市场的交易日历信息，包括交易日、节假日等。

## 函数原型

```python
request_trading_days(market, start, end)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场标识 |
| start | str | 开始时间，格式YYYY-MM-DD |
| end | str | 结束时间，格式YYYY-MM-DD |

## 使用示例

### 获取交易日历

```python
from futu import *
from datetime import datetime, timedelta

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股本月交易日历
start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
end_date = (datetime.now().replace(day=1) + timedelta(days=32)).replace(day=1).strftime('%Y-%m-%d')

ret, data = quote_ctx.request_trading_days(Market.HK, start_date, end_date)

if ret == RET_OK:
    print(f"港股交易日历 ({start_date} 至 {end_date}):")
    print(data)
    
    trading_days = data[data['is_trading_day'] == True]
    non_trading_days = data[data['is_trading_day'] == False]
    
    print(f"\n交易日数量: {len(trading_days)}")
    print(f"非交易日数量: {len(non_trading_days)}")
    
    print("\n交易日列表:")
    for index, row in trading_days.iterrows():
        print(f"  {row['time']}")
    
    print("\n非交易日列表:")
    for index, row in non_trading_days.iterrows():
        print(f"  {row['time']}")
        
else:
    print('获取交易日历失败:', data)

quote_ctx.close()
```

### 多市场交易日历对比

```python
from futu import *
from datetime import datetime, timedelta

def compare_trading_calendars():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取本月日历
    start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
    end_date = (datetime.now().replace(day=1) + timedelta(days=32)).replace(day=1).strftime('%Y-%m-%d')
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股"),
        (Market.CN, "A股")
    ]
    
    print("多市场交易日历对比:")
    print("=" * 60)
    
    all_calendars = {}
    
    for market, market_name in markets:
        ret, data = quote_ctx.request_trading_days(market, start_date, end_date)
        
        if ret == RET_OK:
            all_calendars[market_name] = data
            
            trading_days = data[data['is_trading_day'] == True]
            non_trading_days = data[data['is_trading_day'] == False]
            
            print(f"\n{market_name}:")
            print(f"  交易日: {len(trading_days)}天")
            print(f"  非交易日: {len(non_trading_days)}天")
            
            # 显示非交易日
            if len(non_trading_days) > 0:
                print(f"  假期安排:")
                for index, row in non_trading_days.iterrows():
                    print(f"    {row['time']}")
    
    # 找出共同交易日
    if len(all_calendars) > 1:
        print(f"\n共同交易日分析:")
        
        # 获取所有日期的交集
        all_dates = set()
        for market_name, calendar_data in all_calendars.items():
            dates = set(calendar_data['time'])
            all_dates.update(dates)
        
        common_trading_days = []
        for date in sorted(all_dates):
            is_common_trading = True
            for market_name, calendar_data in all_calendars.items():
                date_row = calendar_data[calendar_data['time'] == date]
                if date_row.empty or not date_row.iloc[0]['is_trading_day']:
                    is_common_trading = False
                    break
            
            if is_common_trading:
                common_trading_days.append(date)
        
        print(f"共同交易日: {len(common_trading_days)}天")
        for date in common_trading_days:
            print(f"  {date}")
    
    quote_ctx.close()

compare_trading_calendars()
```

### 交易日计算工具

```python
from futu import *
from datetime import datetime, timedelta
import pandas as pd

class TradingCalendar:
    def __init__(self, market):
        self.market = market
        self.quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
        self.calendar_cache = {}
    
    def get_trading_calendar(self, start_date, end_date):
        """获取交易日历"""
        cache_key = f"{start_date}_{end_date}"
        
        if cache_key not in self.calendar_cache:
            ret, data = self.quote_ctx.request_trading_days(
                self.market, start_date, end_date
            )
            
            if ret == RET_OK:
                self.calendar_cache[cache_key] = data
            else:
                return None
        
        return self.calendar_cache[cache_key]
    
    def is_trading_day(self, date):
        """判断是否为交易日"""
        date_str = date.strftime('%Y-%m-%d')
        
        # 获取包含该日期的日历
        start_date = (date - timedelta(days=7)).strftime('%Y-%m-%d')
        end_date = (date + timedelta(days=7)).strftime('%Y-%m-%d')
        
        calendar_data = self.get_trading_calendar(start_date, end_date)
        
        if calendar_data is not None:
            date_row = calendar_data[calendar_data['time'] == date_str]
            if not date_row.empty:
                return date_row.iloc[0]['is_trading_day']
        
        return False
    
    def get_next_trading_day(self, date):
        """获取下一个交易日"""
        current_date = date
        
        for _ in range(10):  # 最多查找10天
            current_date += timedelta(days=1)
            if self.is_trading_day(current_date):
                return current_date
        
        return None
    
    def get_previous_trading_day(self, date):
        """获取上一个交易日"""
        current_date = date
        
        for _ in range(10):  # 最多查找10天
            current_date -= timedelta(days=1)
            if self.is_trading_day(current_date):
                return current_date
        
        return None
    
    def get_trading_days_in_month(self, year, month):
        """获取指定月份的交易日"""
        start_date = datetime(year, month, 1).strftime('%Y-%m-%d')
        
        if month == 12:
            end_date = datetime(year + 1, 1, 1).strftime('%Y-%m-%d')
        else:
            end_date = datetime(year, month + 1, 1).strftime('%Y-%m-%d')
        
        calendar_data = self.get_trading_calendar(start_date, end_date)
        
        if calendar_data is not None:
            trading_days = calendar_data[calendar_data['is_trading_day'] == True]
            return trading_days['time'].tolist()
        
        return []
    
    def calculate_trading_days_between(self, start_date, end_date):
        """计算两个日期之间的交易日数量"""
        start_str = start_date.strftime('%Y-%m-%d')
        end_str = end_date.strftime('%Y-%m-%d')
        
        calendar_data = self.get_trading_calendar(start_str, end_str)
        
        if calendar_data is not None:
            trading_days = calendar_data[
                (calendar_data['is_trading_day'] == True) &
                (calendar_data['time'] >= start_str) &
                (calendar_data['time'] <= end_str)
            ]
            return len(trading_days)
        
        return 0
    
    def close(self):
        self.quote_ctx.close()

# 使用示例
calendar = TradingCalendar(Market.HK)

# 检查今天是否为交易日
today = datetime.now()
print(f"今天({today.strftime('%Y-%m-%d')})是否为交易日: {calendar.is_trading_day(today)}")

# 获取下一个交易日
next_trading = calendar.get_next_trading_day(today)
if next_trading:
    print(f"下一个交易日: {next_trading.strftime('%Y-%m-%d')}")

# 获取本月交易日
trading_days = calendar.get_trading_days_in_month(today.year, today.month)
print(f"本月交易日数量: {len(trading_days)}")

# 计算两个日期间的交易日数量
start_date = datetime(2024, 1, 1)
end_date = datetime(2024, 1, 31)
trading_count = calendar.calculate_trading_days_between(start_date, end_date)
print(f"2024年1月交易日数量: {trading_count}")

calendar.close()
```

### 假期分析工具

```python
from futu import *
from datetime import datetime, timedelta
import pandas as pd

def analyze_holidays():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取全年数据
    current_year = datetime.now().year
    start_date = f"{current_year}-01-01"
    end_date = f"{current_year}-12-31"
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股"),
        (Market.CN, "A股")
    ]
    
    print(f"{current_year}年假期分析:")
    print("=" * 60)
    
    for market, market_name in markets:
        ret, data = quote_ctx.request_trading_days(market, start_date, end_date)
        
        if ret == RET_OK:
            # 分析假期
            non_trading_days = data[data['is_trading_day'] == False]
            
            print(f"\n{market_name}假期安排:")
            print(f"总假期天数: {len(non_trading_days)}")
            
            # 按月分组
            non_trading_days['time'] = pd.to_datetime(non_trading_days['time'])
            monthly_holidays = non_trading_days.groupby(
                non_trading_days['time'].dt.month
            ).size()
            
            print("月度假期分布:")
            for month, count in monthly_holidays.items():
                print(f"  {month}月: {count}天")
            
            # 找出连续假期
            print("连续假期分析:")
            holidays = sorted(non_trading_days['time'].dt.date)
            
            if holidays:
                consecutive_periods = []
                current_period = [holidays[0]]
                
                for i in range(1, len(holidays)):
                    if (holidays[i] - holidays[i-1]).days == 1:
                        current_period.append(holidays[i])
                    else:
                        if len(current_period) > 1:
                            consecutive_periods.append(current_period)
                        current_period = [holidays[i]]
                
                if len(current_period) > 1:
                    consecutive_periods.append(current_period)
                
                for period in consecutive_periods:
                    if len(period) >= 3:  # 3天以上的长假
                        print(f"  {period[0]} 至 {period[-1]} ({len(period)}天)")
    
    quote_ctx.close()

analyze_holidays()
```

### 交易日提醒系统

```python
from futu import *
from datetime import datetime, timedelta

def create_trading_reminder():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    
    # 获取未来15天的交易日历
    start_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=15)).strftime('%Y-%m-%d')
    
    markets = [
        (Market.HK, "港股"),
        (Market.US, "美股"),
        (Market.CN, "A股")
    ]
    
    print("交易日提醒系统:")
    print("=" * 60)
    
    for market, market_name in markets:
        ret, data = quote_ctx.request_trading_days(market, start_date, end_date)
        
        if ret == RET_OK:
            print(f"\n{market_name} 未来15天交易安排:")
            print("-" * 40)
            
            # 转换日期格式
            data['date'] = pd.to_datetime(data['time'])
            data['weekday'] = data['date'].dt.day_name()
            
            for index, row in data.iterrows():
                date = row['date']
                is_trading = row['is_trading_day']
                weekday = row['weekday']
                
                # 计算距离今天的天数
                days_ahead = (date.date() - datetime.now().date()).days
                
                if is_trading:
                    status = "📈 交易日"
                    if days_ahead == 0:
                        reminder = " (今天)"
                    elif days_ahead == 1:
                        reminder = " (明天)"
                    else:
                        reminder = f" ({days_ahead}天后)"
                else:
                    status = "🏁 休市"
                    reminder = ""
                
                print(f"  {date.strftime('%m-%d')} {weekday}: {status}{reminder}")
            
            # 统计信息
            trading_days = data[data['is_trading_day'] == True]
            non_trading_days = data[data['is_trading_day'] == False]
            
            print(f"\n统计: 交易日{len(trading_days)}天, 休市{len(non_trading_days)}天")
            
            # 下一个交易日提醒
            next_trading = trading_days[trading_days['date'] > datetime.now()]
            if not next_trading.empty:
                next_date = next_trading.iloc[0]['date']
                days_to_next = (next_date.date() - datetime.now().date()).days
                
                if days_to_next == 0:
                    print(f"⏰ 今天是交易日！")
                elif days_to_next == 1:
                    print(f"⏰ 明天是交易日！")
                else:
                    print(f"⏰ 下一个交易日: {next_date.strftime('%m-%d')} ({days_to_next}天后)")
    
    quote_ctx.close()

create_trading_reminder()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| time | str | 日期 |
| is_trading_day | bool | 是否为交易日 |

## 应用场景

1. **交易计划**: 制定交易计划和策略
2. **风险管理**: 避免在非交易日下单
3. **资金管理**: 合理安排资金周转
4. **假期提醒**: 提前准备长假安排
5. **自动化交易**: 程序化交易时间控制

## 注意事项

1. 交易日历可能因突发事件调整
2. 不同市场的假期安排不同
3. 建议定期更新交易日历
4. 需要考虑时区差异
5. 部分市场有半日交易安排