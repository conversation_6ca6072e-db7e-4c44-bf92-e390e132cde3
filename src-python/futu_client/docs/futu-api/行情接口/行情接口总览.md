# 行情接口总览

富途OpenAPI提供丰富的行情数据接口，支持实时数据订阅、推送回调、数据拉取等多种功能。

## 接口分类

| 模块 | 接口名 | 功能简介 |
|------|--------|----------|
| **实时行情** | **订阅** | |
| | `subscribe` | 订阅实时数据，指定股票代码和订阅的数据类型即可 |
| | `unsubscribe` | 取消订阅 |
| | `unsubscribe_all` | 取消所有订阅 |
| | `query_subscription` | 查询订阅信息 |
| | **推送回调** | |
| | `StockQuoteHandlerBase` | 报价推送 |
| | `OrderBookHandlerBase` | 摆盘推送 |
| | `CurKlineHandlerBase` | K线推送 |
| | `TickerHandlerBase` | 逐笔推送 |
| | `RTDataHandlerBase` | 分时推送 |
| | `BrokerHandlerBase` | 经纪队列推送 |
| | **拉取** | |
| | `get_market_snapshot` | 获取市场快照 |
| | `get_stock_quote` | 获取订阅股票报价的实时数据，有订阅要求限制 |
| | `get_order_book` | 获取实时摆盘数据 |
| | `get_cur_kline` | 实时获取指定股票最近num个K线数据 |
| | `get_rt_data` | 获取指定股票的分时数据 |
| | `get_rt_ticker` | 获取指定股票的实时逐笔。取最近num个逐笔 |
| | `get_broker_queue` | 获取股票的经纪队列 |
| **基本数据** | | |
| | `get_market_state` | 获取股票对应市场的市场状态 |
| | `get_capital_flow` | 获取个股资金流向 |
| | `get_capital_distribution` | 获取个股资金分布 |
| | `get_owner_plate` | 获取单支或多支股票的所属板块信息列表 |
| | `request_history_kline` | 获取K线，不需要事先下载K线数据 |
| | `get_rehab` | 获取给定股票的复权因子 |
| **相关衍生品** | | |
| | `get_option_expiration_date` | 通过标的股票，查询期权链的所有到期日 |
| | `get_option_chain` | 通过标的股查询期权 |
| | `get_warrant` | 拉取窝轮和相关衍生品数据接口 |
| | `get_referencestock_list` | 获取证券的关联数据 |
| | `get_future_info` | 获取期货合约资料 |
| **全市场筛选** | | |
| | `get_stock_filter` | 获取条件选股 |
| | `get_plate_stock` | 获取特定板块下的股票列表 |
| | `get_plate_list` | 获取板块集合下的子板块列表 |
| | `get_stock_basicinfo` | 获取指定市场中特定类型或特定股票的基本信息 |
| | `get_ipo_list` | 获取指定市场的IPO列表 |
| | `get_global_state` | 获取全局市场状态 |
| | `request_trading_days` | 获取交易日历 |
| **个性化** | | |
| | `get_history_kl_quota` | 获取已使用过的额度，即当前周期内已经下载过多少只股票 |
| | `set_price_reminder` | 设置到价提醒 |
| | `get_price_reminder` | 获取对某只股票(某个市场)设置的到价提醒列表 |
| | `get_user_security_group` | 获取自选股分组列表 |
| | `get_user_security` | 获取指定分组的自选股列表 |
| | `modify_user_security` | 修改指定分组的自选股列表 |
| | `PriceReminderHandlerBase` | 到价提醒推送 |

## 使用流程

### 1. 实时行情使用流程

1. **订阅数据**：使用`subscribe`接口订阅所需的股票和数据类型
2. **设置回调**：设置相应的回调处理器接收推送数据
3. **获取数据**：通过拉取接口获取实时数据
4. **取消订阅**：使用`unsubscribe`或`unsubscribe_all`取消订阅

### 2. 历史数据获取流程

1. **查询基本信息**：使用`get_stock_basicinfo`获取股票基本信息
2. **获取历史K线**：使用`request_history_kline`获取历史K线数据
3. **处理复权**：使用`get_rehab`获取复权因子进行价格调整

### 3. 筛选和查询流程

1. **市场筛选**：使用`get_stock_filter`进行条件选股
2. **板块查询**：使用`get_plate_list`和`get_plate_stock`查询板块相关信息
3. **衍生品查询**：使用期权和窝轮相关接口查询衍生品信息

## 注意事项

1. **订阅限制**：部分接口需要先订阅才能获取实时数据
2. **频率限制**：各接口都有频率限制，请合理控制调用频率
3. **权限要求**：某些市场数据需要相应的行情权限
4. **数据延迟**：免费用户可能有数据延迟，实时数据需要相应权限

## 下一步

- 了解[行情对象](base.md)的创建和配置
- 查看[订阅反订阅](sub.md)的详细使用方法
- 学习各种[推送回调](update-stock-quote.md)的处理方式