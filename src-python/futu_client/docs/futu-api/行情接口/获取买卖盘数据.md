# 获取摆盘数据

获取股票的详细买卖盘数据，包括多档位的买卖价格和数量信息，用于分析市场深度和流动性。

## get_order_book - 获取摆盘数据

### 接口原型

```python
get_order_book(code, num=10)
```

### 功能说明

获取指定股票的买卖盘摆盘数据。**注意：使用此接口前需要先订阅相应股票的摆盘数据。**

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | str | 是 | 股票代码，例如'HK.00700' |
| num | int | 否 | 获取的摆盘档位数，默认10档，最大40档 |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| bid_frame_table | DataFrame | 买盘DataFrame |
| ask_frame_table | DataFrame | 卖盘DataFrame |

### DataFrame字段说明

#### 买盘/卖盘DataFrame
| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| price | float | 价格 |
| volume | int | 数量 |
| order_num | int | 订单数量 |

### 代码示例

#### 基本摆盘获取

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 先订阅摆盘数据
    ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.ORDER_BOOK])
    if ret != ft.RET_OK:
        print('订阅摆盘失败:', err_message)
        exit()
    
    # 获取摆盘数据
    ret, bid_data, ask_data = quote_ctx.get_order_book('HK.00700', num=10)
    
    if ret == ft.RET_OK:
        print("腾讯(00700)摆盘数据:")
        print("=" * 60)
        print("档位    买盘价格    买盘数量    卖盘价格    卖盘数量")
        print("-" * 60)
        
        # 显示买卖盘对比
        max_rows = max(len(bid_data), len(ask_data))
        
        for i in range(max_rows):
            # 买盘数据（从高到低）
            if i < len(bid_data):
                bid_price = bid_data.iloc[i]['price']
                bid_volume = bid_data.iloc[i]['volume']
                bid_info = f"{bid_price:>8.2f}    {bid_volume:>8,}"
            else:
                bid_info = " " * 20
            
            # 卖盘数据（从低到高）
            if i < len(ask_data):
                ask_price = ask_data.iloc[i]['price']
                ask_volume = ask_data.iloc[i]['volume']
                ask_info = f"{ask_price:>8.2f}    {ask_volume:>8,}"
            else:
                ask_info = " " * 20
            
            print(f"{i+1:>2}     {bid_info}    {ask_info}")
    else:
        print('获取摆盘失败:', bid_data)
        
finally:
    quote_ctx.close()
```

#### 摆盘深度分析

```python
import futu as ft
import pandas as pd

def analyze_order_book_depth(quote_ctx, code, num_levels=10):
    """分析摆盘深度"""
    
    ret, bid_data, ask_data = quote_ctx.get_order_book(code, num=num_levels)
    
    if ret != ft.RET_OK:
        print(f"获取{code}摆盘失败:", bid_data)
        return
    
    print(f"\n{code} 摆盘深度分析:")
    print("=" * 50)
    
    # 1. 基本统计
    if not bid_data.empty and not ask_data.empty:
        best_bid = bid_data.iloc[0]['price']
        best_ask = ask_data.iloc[0]['price']
        spread = best_ask - best_bid
        spread_pct = (spread / best_bid) * 100
        
        print(f"最优买价: {best_bid:.2f}")
        print(f"最优卖价: {best_ask:.2f}")
        print(f"买卖价差: {spread:.2f} ({spread_pct:.3f}%)")
    
    # 2. 买卖盘总量
    total_bid_volume = bid_data['volume'].sum()
    total_ask_volume = ask_data['volume'].sum()
    total_volume = total_bid_volume + total_ask_volume
    
    if total_volume > 0:
        bid_ratio = total_bid_volume / total_volume * 100
        ask_ratio = total_ask_volume / total_volume * 100
        
        print(f"\n买盘总量: {total_bid_volume:,} ({bid_ratio:.1f}%)")
        print(f"卖盘总量: {total_ask_volume:,} ({ask_ratio:.1f}%)")
        print(f"买卖比: {total_bid_volume/total_ask_volume:.2f}" if total_ask_volume > 0 else "买卖比: ∞")
    
    # 3. 订单密度分析
    if 'order_num' in bid_data.columns and 'order_num' in ask_data.columns:
        avg_bid_order_size = total_bid_volume / bid_data['order_num'].sum() if bid_data['order_num'].sum() > 0 else 0
        avg_ask_order_size = total_ask_volume / ask_data['order_num'].sum() if ask_data['order_num'].sum() > 0 else 0
        
        print(f"\n平均买单规模: {avg_bid_order_size:.0f}")
        print(f"平均卖单规模: {avg_ask_order_size:.0f}")
    
    # 4. 价格分布
    if not bid_data.empty and not ask_data.empty:
        bid_price_range = bid_data['price'].max() - bid_data['price'].min()
        ask_price_range = ask_data['price'].max() - ask_data['price'].min()
        
        print(f"\n买盘价格范围: {bid_price_range:.2f}")
        print(f"卖盘价格范围: {ask_price_range:.2f}")
    
    return {
        'spread': spread if not bid_data.empty and not ask_data.empty else None,
        'spread_pct': spread_pct if not bid_data.empty and not ask_data.empty else None,
        'bid_volume': total_bid_volume,
        'ask_volume': total_ask_volume,
        'bid_ratio': bid_ratio if total_volume > 0 else None
    }

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅摆盘数据
    codes = ['HK.00700', 'HK.00001', 'HK.00005']
    quote_ctx.subscribe(codes, [ft.SubType.ORDER_BOOK])
    
    # 分析多只股票的摆盘深度
    for code in codes:
        analyze_order_book_depth(quote_ctx, code)
        
finally:
    quote_ctx.close()
```

#### 摆盘变化监控

```python
import futu as ft
import time
from datetime import datetime

class OrderBookMonitor:
    def __init__(self, quote_ctx, code):
        self.quote_ctx = quote_ctx
        self.code = code
        self.previous_data = None
        self.changes_log = []
    
    def get_current_order_book(self):
        """获取当前摆盘数据"""
        ret, bid_data, ask_data = self.quote_ctx.get_order_book(self.code, num=5)
        
        if ret == ft.RET_OK:
            return {
                'timestamp': datetime.now(),
                'best_bid': bid_data.iloc[0]['price'] if not bid_data.empty else None,
                'best_ask': ask_data.iloc[0]['price'] if not ask_data.empty else None,
                'bid_volume': bid_data['volume'].sum(),
                'ask_volume': ask_data['volume'].sum(),
                'bid_data': bid_data,
                'ask_data': ask_data
            }
        return None
    
    def detect_changes(self, current_data):
        """检测摆盘变化"""
        if self.previous_data is None:
            self.previous_data = current_data
            return []
        
        changes = []
        
        # 检测最优价格变化
        if current_data['best_bid'] != self.previous_data['best_bid']:
            changes.append(f"最优买价: {self.previous_data['best_bid']:.2f} → {current_data['best_bid']:.2f}")
        
        if current_data['best_ask'] != self.previous_data['best_ask']:
            changes.append(f"最优卖价: {self.previous_data['best_ask']:.2f} → {current_data['best_ask']:.2f}")
        
        # 检测量能变化
        bid_volume_change = current_data['bid_volume'] - self.previous_data['bid_volume']
        ask_volume_change = current_data['ask_volume'] - self.previous_data['ask_volume']
        
        if abs(bid_volume_change) > 10000:  # 买盘变化超过1万股
            changes.append(f"买盘量变化: {bid_volume_change:+,}")
        
        if abs(ask_volume_change) > 10000:  # 卖盘变化超过1万股
            changes.append(f"卖盘量变化: {ask_volume_change:+,}")
        
        self.previous_data = current_data
        return changes
    
    def monitor(self, duration_minutes=30, check_interval=5):
        """开始监控摆盘变化"""
        print(f"开始监控 {self.code} 摆盘变化")
        print(f"监控时长: {duration_minutes} 分钟，检查间隔: {check_interval} 秒")
        print("-" * 50)
        
        start_time = time.time()
        end_time = start_time + duration_minutes * 60
        
        while time.time() < end_time:
            current_data = self.get_current_order_book()
            
            if current_data:
                changes = self.detect_changes(current_data)
                
                if changes:
                    timestamp = current_data['timestamp'].strftime('%H:%M:%S')
                    print(f"\n[{timestamp}] 摆盘变化:")
                    for change in changes:
                        print(f"  {change}")
                    
                    # 记录变化
                    self.changes_log.append({
                        'timestamp': current_data['timestamp'],
                        'changes': changes
                    })
                else:
                    # 显示当前状态
                    timestamp = current_data['timestamp'].strftime('%H:%M:%S')
                    spread = current_data['best_ask'] - current_data['best_bid']
                    print(f"[{timestamp}] 价差:{spread:.2f} 买:{current_data['bid_volume']:,} 卖:{current_data['ask_volume']:,}")
            
            time.sleep(check_interval)
        
        print(f"\n监控结束，共记录 {len(self.changes_log)} 次变化")
    
    def get_summary(self):
        """获取监控摘要"""
        if not self.changes_log:
            return "监控期间无显著变化"
        
        summary = f"监控摘要 - {self.code}\n"
        summary += f"变化次数: {len(self.changes_log)}\n"
        summary += "主要变化:\n"
        
        for i, log in enumerate(self.changes_log[-5:], 1):  # 显示最近5次变化
            timestamp = log['timestamp'].strftime('%H:%M:%S')
            summary += f"  {i}. [{timestamp}] {'; '.join(log['changes'])}\n"
        
        return summary

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 订阅摆盘数据
    quote_ctx.subscribe(['HK.00700'], [ft.SubType.ORDER_BOOK])
    
    # 创建监控器
    monitor = OrderBookMonitor(quote_ctx, 'HK.00700')
    
    # 开始监控（监控10分钟，每5秒检查一次）
    monitor.monitor(duration_minutes=10, check_interval=5)
    
    # 显示摘要
    print(monitor.get_summary())
    
finally:
    quote_ctx.close()
```

#### 市场流动性分析

```python
import futu as ft
import numpy as np

def analyze_market_liquidity(quote_ctx, codes):
    """分析多只股票的市场流动性"""
    
    results = []
    
    for code in codes:
        ret, bid_data, ask_data = quote_ctx.get_order_book(code, num=10)
        
        if ret != ft.RET_OK:
            continue
        
        if bid_data.empty or ask_data.empty:
            continue
        
        # 计算流动性指标
        best_bid = bid_data.iloc[0]['price']
        best_ask = ask_data.iloc[0]['price']
        spread = best_ask - best_bid
        spread_pct = (spread / best_bid) * 100
        
        # 累计深度（前5档）
        bid_depth_5 = bid_data.head(5)['volume'].sum()
        ask_depth_5 = ask_data.head(5)['volume'].sum()
        total_depth_5 = bid_depth_5 + ask_depth_5
        
        # 价格影响成本（模拟大额交易的价格影响）
        def calculate_impact_cost(data, target_volume):
            cumulative_volume = 0
            weighted_price = 0
            
            for _, row in data.iterrows():
                if cumulative_volume >= target_volume:
                    break
                
                available_volume = min(row['volume'], target_volume - cumulative_volume)
                weighted_price += row['price'] * available_volume
                cumulative_volume += available_volume
            
            return weighted_price / cumulative_volume if cumulative_volume > 0 else None
        
        # 计算买入10万股的平均成本
        buy_impact_price = calculate_impact_cost(ask_data, 100000)
        buy_impact_cost = ((buy_impact_price - best_ask) / best_ask * 100) if buy_impact_price else None
        
        # 计算卖出10万股的平均成本
        sell_impact_price = calculate_impact_cost(bid_data.sort_values('price', ascending=False), 100000)
        sell_impact_cost = ((best_bid - sell_impact_price) / best_bid * 100) if sell_impact_price else None
        
        results.append({
            'code': code,
            'spread': spread,
            'spread_pct': spread_pct,
            'depth_5': total_depth_5,
            'bid_depth_5': bid_depth_5,
            'ask_depth_5': ask_depth_5,
            'buy_impact_cost': buy_impact_cost,
            'sell_impact_cost': sell_impact_cost
        })
    
    # 生成流动性报告
    print("=== 市场流动性分析报告 ===\n")
    
    if results:
        results_df = pd.DataFrame(results)
        
        print("流动性排名 (按价差百分比排序):")
        print("-" * 70)
        print("代码        价差    价差%   深度(5档)  买入冲击  卖出冲击")
        print("-" * 70)
        
        sorted_results = results_df.sort_values('spread_pct')
        
        for _, row in sorted_results.iterrows():
            buy_impact = f"{row['buy_impact_cost']:.3f}%" if row['buy_impact_cost'] else "N/A"
            sell_impact = f"{row['sell_impact_cost']:.3f}%" if row['sell_impact_cost'] else "N/A"
            
            print(f"{row['code']:<10} {row['spread']:>6.2f} {row['spread_pct']:>6.3f}% "
                  f"{row['depth_5']:>10,} {buy_impact:>8} {sell_impact:>8}")
        
        # 流动性统计
        print(f"\n流动性统计:")
        print(f"平均价差: {results_df['spread_pct'].mean():.3f}%")
        print(f"最佳流动性: {sorted_results.iloc[0]['code']} (价差 {sorted_results.iloc[0]['spread_pct']:.3f}%)")
        print(f"最差流动性: {sorted_results.iloc[-1]['code']} (价差 {sorted_results.iloc[-1]['spread_pct']:.3f}%)")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 港股蓝筹股
    blue_chips = ['HK.00700', 'HK.00001', 'HK.00005', 'HK.00941', 'HK.03690']
    
    # 订阅摆盘数据
    quote_ctx.subscribe(blue_chips, [ft.SubType.ORDER_BOOK])
    
    # 等待数据到达
    time.sleep(2)
    
    # 分析市场流动性
    analyze_market_liquidity(quote_ctx, blue_chips)
    
finally:
    quote_ctx.close()
```

### Level-2 深度摆盘

#### 获取深度摆盘数据

```python
import futu as ft

def get_level2_order_book(quote_ctx, code):
    """获取Level-2深度摆盘数据"""
    
    # 订阅Level-2摆盘（需要Level-2权限）
    ret, err_message = quote_ctx.subscribe([code], [ft.SubType.ORDER_BOOK], is_level_2=True)
    if ret != ft.RET_OK:
        print(f"订阅Level-2摆盘失败: {err_message}")
        return
    
    # 获取深度摆盘（最多40档）
    ret, bid_data, ask_data = quote_ctx.get_order_book(code, num=40)
    
    if ret == ft.RET_OK:
        print(f"{code} Level-2 深度摆盘:")
        print("=" * 80)
        print("档位    买价      买量      订单数    卖价      卖量      订单数")
        print("-" * 80)
        
        max_levels = min(20, max(len(bid_data), len(ask_data)))  # 显示前20档
        
        for i in range(max_levels):
            # 买盘信息
            if i < len(bid_data):
                bid_row = bid_data.iloc[i]
                bid_info = f"{bid_row['price']:>7.2f} {bid_row['volume']:>8,} {bid_row.get('order_num', 0):>7}"
            else:
                bid_info = " " * 24
            
            # 卖盘信息
            if i < len(ask_data):
                ask_row = ask_data.iloc[i]
                ask_info = f"{ask_row['price']:>7.2f} {ask_row['volume']:>8,} {ask_row.get('order_num', 0):>7}"
            else:
                ask_info = " " * 24
            
            print(f"{i+1:>2}   {bid_info}   {ask_info}")
    else:
        print(f"获取深度摆盘失败: {bid_data}")

# 使用示例（需要Level-2权限）
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    get_level2_order_book(quote_ctx, 'HK.00700')
finally:
    quote_ctx.close()
```

### 使用限制

#### 权限要求
- **Level-1摆盘**：基础实时行情权限
- **Level-2摆盘**：需要Level-2行情权限
- **档位数量**：Level-1最多10档，Level-2最多40档

#### 订阅限制
- **订阅要求**：使用前必须先订阅摆盘数据
- **数据更新**：摆盘数据实时更新
- **并发限制**：避免频繁调用，建议配合推送使用

### 注意事项

1. **订阅前提**：必须先订阅ORDER_BOOK数据类型
2. **数据实时性**：摆盘数据变化频繁，建议结合推送使用
3. **权限差异**：Level-1和Level-2权限的数据深度不同
4. **市场差异**：不同市场的摆盘档位数可能不同
5. **停牌处理**：停牌股票可能返回空数据

### 错误处理

```python
import futu as ft
import time

def safe_get_order_book(quote_ctx, code, num=10, max_retries=3):
    """安全获取摆盘数据"""
    
    # 先检查是否已订阅
    ret, sub_data = quote_ctx.query_subscription()
    if ret == ft.RET_OK:
        subscribed_codes = sub_data[sub_data['sub_type'] == 'ORDER_BOOK']['code'].tolist()
        if code not in subscribed_codes:
            print(f"正在订阅 {code} 的摆盘数据...")
            ret, err_msg = quote_ctx.subscribe([code], [ft.SubType.ORDER_BOOK])
            if ret != ft.RET_OK:
                return False, f"订阅失败: {err_msg}", None, None
            time.sleep(1)  # 等待订阅生效
    
    # 尝试获取摆盘数据
    for attempt in range(max_retries):
        try:
            ret, bid_data, ask_data = quote_ctx.get_order_book(code, num=num)
            
            if ret == ft.RET_OK:
                return True, "成功", bid_data, ask_data
            else:
                print(f"第{attempt + 1}次尝试失败: {bid_data}")
                if attempt < max_retries - 1:
                    time.sleep(1)
        
        except Exception as e:
            print(f"第{attempt + 1}次尝试异常: {e}")
            if attempt < max_retries - 1:
                time.sleep(1)
    
    return False, "获取摆盘数据失败", None, None

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    success, message, bid_data, ask_data = safe_get_order_book(quote_ctx, 'HK.00700')
    
    if success:
        print("摆盘数据获取成功")
        print(f"买盘档数: {len(bid_data)}, 卖盘档数: {len(ask_data)}")
    else:
        print("获取失败:", message)

finally:
    quote_ctx.close()
```

### 相关接口

- [订阅反订阅](sub.md) - 订阅摆盘数据
- [获取市场快照](get-market-snapshot.md) - 获取基础买卖盘信息
- [摆盘推送](update-order-book.md) - 摆盘数据推送回调