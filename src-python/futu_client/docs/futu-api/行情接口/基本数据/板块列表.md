# 板块列表

> 对应官方文档: `/futu-api-doc/base/get-plate-list.html`

## 接口介绍

获取指定市场的板块列表，包括行业板块、概念板块等分类信息。

## 函数原型

```python
get_plate_list(market, plate_class)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场类型 |
| plate_class | PlateClass | 板块分类 |

## 板块分类

| 类型 | 说明 |
|------|------|
| PlateClass.INDUSTRY | 行业板块 |
| PlateClass.CONCEPT | 概念板块 |
| PlateClass.AREA | 地域板块 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股行业板块
ret, data = quote_ctx.get_plate_list(Market.HK, PlateClass.INDUSTRY)

if ret == RET_OK:
    print("港股行业板块:")
    print(data)
    for index, row in data.iterrows():
        print(f"板块: {row['plate_name']}")
        print(f"代码: {row['plate_id']}")
        print(f"股票数量: {row['stock_count']}")
        print("-" * 30)
else:
    print('获取板块列表失败:', data)

# 获取概念板块
ret, concept_data = quote_ctx.get_plate_list(Market.HK, PlateClass.CONCEPT)

if ret == RET_OK:
    print("\n港股概念板块:")
    print(concept_data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| plate_id | str | 板块ID |
| plate_name | str | 板块名称 |
| plate_type | str | 板块类型 |
| stock_count | int | 成分股数量 |

## 多市场分析示例

```python
from futu import *
import pandas as pd

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

markets = [Market.HK, Market.US, Market.SH]
market_names = ['港股', '美股', '沪股']

for market, market_name in zip(markets, market_names):
    print(f"\n{market_name}行业板块:")
    ret, data = quote_ctx.get_plate_list(market, PlateClass.INDUSTRY)
    
    if ret == RET_OK:
        print(f"总板块数: {len(data)}")
        print(f"平均成分股数: {data['stock_count'].mean():.1f}")
        
        # 显示最大的几个板块
        top_plates = data.nlargest(5, 'stock_count')
        print("最大板块:")
        for _, row in top_plates.iterrows():
            print(f"  {row['plate_name']}: {row['stock_count']}只")

quote_ctx.close()
```

## 板块股票获取示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 先获取板块列表
ret, plate_data = quote_ctx.get_plate_list(Market.HK, PlateClass.INDUSTRY)

if ret == RET_OK:
    # 选择科技板块
    tech_plate = plate_data[plate_data['plate_name'].str.contains('科技', na=False)]
    
    if not tech_plate.empty:
        plate_id = tech_plate.iloc[0]['plate_id']
        print(f"板块ID: {plate_id}")
        print(f"板块名称: {tech_plate.iloc[0]['plate_name']}")
        
        # 获取板块成分股
        ret, stock_data = quote_ctx.get_plate_stock(plate_id)
        
        if ret == RET_OK:
            print(f"\n成分股列表:")
            for _, row in stock_data.head(10).iterrows():
                print(f"{row['code']}: {row['stock_name']}")

quote_ctx.close()
```

## 板块监控示例

```python
from futu import *
import time

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

def monitor_plate_performance():
    ret, data = quote_ctx.get_plate_list(Market.HK, PlateClass.INDUSTRY)
    
    if ret == RET_OK:
        print(f"\n{time.strftime('%H:%M:%S')} 板块概况:")
        print(f"总板块数: {len(data)}")
        
        # 可以结合其他接口获取板块表现
        large_plates = data[data['stock_count'] > 50]
        print(f"大型板块数: {len(large_plates)}")
        
        for _, row in large_plates.head(5).iterrows():
            print(f"  {row['plate_name']}: {row['stock_count']}只股票")

# 定期监控板块变化
try:
    while True:
        monitor_plate_performance()
        time.sleep(300)  # 5分钟更新一次
except KeyboardInterrupt:
    print("\n监控已停止")
    quote_ctx.close()
```

## 应用场景

1. **行业分析**: 分析不同行业的股票分布
2. **主题投资**: 基于概念板块进行主题投资
3. **板块轮动**: 跟踪板块轮动策略
4. **风险分散**: 了解板块构成进行风险分散

## 注意事项

1. 板块分类可能随时间调整
2. 不同市场的板块结构不同
3. 板块成分股可能发生变化
4. 建议定期更新板块信息