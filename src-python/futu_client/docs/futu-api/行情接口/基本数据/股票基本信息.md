# 股票基本信息

> 对应官方文档: `/futu-api-doc/base/get-stock-basicinfo.html`

## 接口介绍

获取股票基本信息，包括股票名称、市场、板块等基础数据。

## 函数原型

```python
get_stock_basicinfo(market, stock_type=SecurityType.STOCK, code_list=None)
```

## 参数说明

| 参数 | 类型 | 说明 |
|------|------|------|
| market | Market | 市场类型 |
| stock_type | SecurityType | 股票类型 |
| code_list | list | 股票代码列表，None表示全部 |

## 市场类型

| 类型 | 说明 |
|------|------|
| Market.HK | 港股 |
| Market.US | 美股 |
| Market.SH | 沪股 |
| Market.SZ | 深股 |
| Market.SG | 新加坡股 |
| Market.JP | 日股 |

## 股票类型

| 类型 | 说明 |
|------|------|
| SecurityType.STOCK | 普通股 |
| SecurityType.ETF | ETF |
| SecurityType.WARRANT | 涡轮 |
| SecurityType.BOND | 债券 |

## 使用示例

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

# 获取港股基本信息
ret, data = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK)

if ret == RET_OK:
    print(data)
    # 显示前10只股票信息
    for index, row in data.head(10).iterrows():
        print(f"代码: {row['code']}")
        print(f"名称: {row['name']}")
        print(f"板块: {row['plate_class']}")
        print(f"上市日期: {row['list_time']}")
        print(f"股本: {row['stock_child_type']}")
        print("-" * 40)
else:
    print('获取股票基本信息失败:', data)

# 获取特定股票信息
ret, data = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK, ['HK.00700', 'HK.00981'])

if ret == RET_OK:
    print("\n特定股票信息:")
    print(data)

quote_ctx.close()
```

## 返回数据结构

| 字段名 | 类型 | 说明 |
|-------|------|------|
| code | str | 股票代码 |
| name | str | 股票名称 |
| lot_size | int | 每手股数 |
| stock_type | str | 股票类型 |
| stock_child_type | str | 股票子类型 |
| stock_owner | str | 所属公司 |
| list_time | str | 上市日期 |
| stock_id | int | 股票ID |
| main_contract | bool | 是否主连合约 |
| last_trade_time | str | 最后交易时间 |

## 数据筛选示例

```python
from futu import *
import pandas as pd

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)

ret, data = quote_ctx.get_stock_basicinfo(Market.HK, SecurityType.STOCK)

if ret == RET_OK:
    # 筛选恒生指数成分股
    hsi_stocks = data[data['plate_class'].str.contains('恒生指数', na=False)]
    print(f"恒生指数成分股数量: {len(hsi_stocks)}")
    
    # 筛选最近上市的股票
    recent_list = data[data['list_time'] > '2020-01-01']
    print(f"2020年后上市股票数量: {len(recent_list)}")
    
    # 按板块统计
    sector_count = data['plate_class'].value_counts()
    print("\n板块分布:")
    print(sector_count.head(10))

quote_ctx.close()
```

## 应用场景

1. **股票筛选**: 根据基本信息筛选股票
2. **市场分析**: 分析市场结构和板块分布
3. **投资组合构建**: 基于基本信息构建投资组合
4. **风险管理**: 了解股票基本属性进行风险控制

## 注意事项

1. 全量数据获取可能较慢，建议分批获取
2. 股票基本信息相对稳定，无需频繁更新
3. 不同市场的字段可能有差异
4. 板块分类可能随时间变化