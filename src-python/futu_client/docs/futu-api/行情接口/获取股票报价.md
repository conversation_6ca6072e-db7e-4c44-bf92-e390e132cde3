# 获取股票报价

获取已订阅股票的实时报价数据，包括最新价、买卖价、成交量等信息。

## get_stock_quote - 获取实时报价

### 接口原型

```python
get_stock_quote(code_list)
```

### 功能说明

获取订阅股票的实时报价数据。**注意：使用此接口前需要先订阅相应股票的报价数据。**

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code_list | list | 是 | 股票代码列表，例如['HK.00700', 'US.AAPL'] |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 报价数据DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| data_date | str | 数据日期 |
| data_time | str | 数据时间 |
| last_price | float | 最新价 |
| open_price | float | 开盘价 |
| high_price | float | 最高价 |
| low_price | float | 最低价 |
| prev_close_price | float | 昨收价 |
| volume | int | 成交量 |
| turnover | float | 成交额 |
| turnover_rate | float | 换手率(%) |
| amplitude | float | 振幅(%) |
| suspension | bool | 是否停牌 |
| listing_date | str | 上市日期 |
| price_spread | float | 价差 |
| dark_status | str | 暗盘交易状态 |
| sec_status | str | 股票状态 |
| strike_price | float | 行权价（窝轮） |
| contract_size | int | 合约大小（期货） |
| open_interest | int | 未平仓合约数（期货） |
| implied_volatility | float | 隐含波动率（期权） |
| premium | float | 溢价率（窝轮） |
| delta | float | 对冲值（期权） |
| gamma | float | 伽马值（期权） |
| vega | float | 维加值（期权） |
| theta | float | 西塔值（期权） |
| rho | float | 洛伊值（期权） |

### 代码示例

#### 基本用法

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 先订阅报价数据
ret, err_message = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
    quote_ctx.close()
    exit()

# 获取报价数据
ret, data = quote_ctx.get_stock_quote(['HK.00700'])
if ret == ft.RET_OK:
    print("腾讯实时报价:")
    print(data[['code', 'last_price', 'volume', 'turnover']])
else:
    print('获取报价失败:', data)

quote_ctx.close()
```

#### 批量获取多只股票报价

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅多只股票
code_list = ['HK.00700', 'HK.00001', 'HK.00005']
ret, err_message = quote_ctx.subscribe(code_list, [ft.SubType.QUOTE])
if ret != ft.RET_OK:
    print('订阅失败:', err_message)
    quote_ctx.close()
    exit()

# 批量获取报价
ret, data = quote_ctx.get_stock_quote(code_list)
if ret == ft.RET_OK:
    print("批量股票报价:")
    print(data[['code', 'last_price', 'open_price', 'high_price', 'low_price', 'volume']])
else:
    print('获取报价失败:', data)

quote_ctx.close()
```

#### 详细报价信息分析

```python
import futu as ft
import pandas as pd

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅港股主要股票
major_hk_stocks = ['HK.00700', 'HK.00001', 'HK.00005', 'HK.00941', 'HK.03690']
quote_ctx.subscribe(major_hk_stocks, [ft.SubType.QUOTE])

# 获取详细报价
ret, data = quote_ctx.get_stock_quote(major_hk_stocks)
if ret == ft.RET_OK:
    # 计算涨跌幅
    data['change_rate'] = (data['last_price'] - data['prev_close_price']) / data['prev_close_price'] * 100
    
    # 显示关键信息
    display_columns = ['code', 'last_price', 'prev_close_price', 'change_rate', 
                      'volume', 'turnover', 'turnover_rate', 'amplitude']
    
    print("港股主要股票报价:")
    for _, row in data.iterrows():
        print(f"股票: {row['code']}")
        print(f"  最新价: {row['last_price']:.2f}")
        print(f"  涨跌幅: {row['change_rate']:.2f}%")
        print(f"  成交量: {row['volume']:,}")
        print(f"  成交额: {row['turnover']:,.0f}")
        print(f"  换手率: {row['turnover_rate']:.2f}%")
        print("-" * 30)
else:
    print('获取报价失败:', data)

quote_ctx.close()
```

#### 实时监控价格变化

```python
import futu as ft
import time

class PriceMonitor:
    def __init__(self, quote_ctx, codes):
        self.quote_ctx = quote_ctx
        self.codes = codes
        self.last_prices = {}
        
    def monitor(self, duration=60):
        """监控指定时长（秒）"""
        print(f"开始监控 {self.codes} 的价格变化...")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            ret, data = self.quote_ctx.get_stock_quote(self.codes)
            if ret == ft.RET_OK:
                for _, row in data.iterrows():
                    code = row['code']
                    current_price = row['last_price']
                    
                    if code in self.last_prices:
                        last_price = self.last_prices[code]
                        if current_price != last_price:
                            change = current_price - last_price
                            print(f"{code}: {last_price:.2f} → {current_price:.2f} "
                                  f"({'+' if change > 0 else ''}{change:.2f})")
                    
                    self.last_prices[code] = current_price
            
            time.sleep(5)  # 每5秒检查一次

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅并监控
monitor_codes = ['HK.00700']
quote_ctx.subscribe(monitor_codes, [ft.SubType.QUOTE])

monitor = PriceMonitor(quote_ctx, monitor_codes)
monitor.monitor(duration=30)  # 监控30秒

quote_ctx.close()
```

### 窝轮特有字段

对于窝轮（权证），DataFrame包含额外字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| strike_price | float | 行权价 |
| premium | float | 溢价率(%) |
| conversion_ratio | float | 换股比率 |
| balance_point | float | 打和点 |
| maturity_time | str | 到期日 |

### 期货特有字段

对于期货，DataFrame包含额外字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| contract_size | int | 合约大小 |
| open_interest | int | 未平仓合约数 |
| contract_month | str | 合约月份 |

### 期权特有字段

对于期权，DataFrame包含希腊字母：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| implied_volatility | float | 隐含波动率 |
| delta | float | Delta值 |
| gamma | float | Gamma值 |
| vega | float | Vega值 |
| theta | float | Theta值 |
| rho | float | Rho值 |

### 注意事项

1. **订阅前提**：必须先订阅相应股票的报价数据
2. **数据延迟**：免费用户可能有15分钟延迟
3. **权限要求**：实时数据需要相应的行情权限
4. **市场时间**：非交易时间获取的是最后交易时间的数据
5. **数据完整性**：部分字段可能因市场或股票类型而缺失

### 错误处理

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    # 尝试获取未订阅的股票报价
    ret, data = quote_ctx.get_stock_quote(['HK.00700'])
    if ret != ft.RET_OK:
        print("获取报价失败，可能未订阅:", data)
        
        # 先订阅再获取
        ret_sub, err_msg = quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
        if ret_sub == ft.RET_OK:
            ret, data = quote_ctx.get_stock_quote(['HK.00700'])
            if ret == ft.RET_OK:
                print("订阅后获取成功:", data['last_price'].iloc[0])
        
except Exception as e:
    print(f"获取报价时发生异常: {e}")
finally:
    quote_ctx.close()
```

### 相关接口

- [订阅反订阅](sub.md) - 订阅报价数据
- [股票报价推送](update-stock-quote.md) - 报价推送回调
- [获取市场快照](get-market-snapshot.md) - 获取市场快照数据