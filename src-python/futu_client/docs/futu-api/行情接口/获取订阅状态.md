# 获取订阅状态

查询当前连接已订阅的实时数据信息，包括订阅的股票代码、数据类型等。

## query_subscription - 查询订阅状态

### 接口原型

```python
query_subscription(is_all_conn=True)
```

### 功能说明

查询已订阅的实时数据，返回当前连接的订阅状态信息。

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| is_all_conn | bool | 否 | 是否返回所有连接的订阅状态，默认True |

### 返回值

| 参数名 | 类型 | 说明 |
|--------|------|------|
| ret | RET_CODE | 接口调用结果，RET_OK表示成功 |
| data | DataFrame | 订阅信息DataFrame，失败时返回错误描述 |

### DataFrame字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | str | 股票代码 |
| sub_type | str | 订阅类型描述 |
| sub_type_list | list | 订阅类型列表 |

### 代码示例

#### 基本查询

```python
import futu as ft

# 创建行情上下文
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 先订阅一些数据
quote_ctx.subscribe(['HK.00700', 'HK.00001'], [ft.SubType.QUOTE, ft.SubType.K_1M])

# 查询订阅状态
ret, data = quote_ctx.query_subscription()
if ret == ft.RET_OK:
    print("当前订阅状态:")
    print(data)
else:
    print("查询失败:", data)

quote_ctx.close()
```

#### 输出示例

```
当前订阅状态:
        code                    sub_type              sub_type_list
0  HK.00700    QUOTE,K_1M                  [QUOTE, K_1M]
1  HK.00001    QUOTE,K_1M                  [QUOTE, K_1M]
```

#### 检查特定股票订阅状态

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 订阅数据
quote_ctx.subscribe(['HK.00700'], [ft.SubType.QUOTE])
quote_ctx.subscribe(['HK.00001'], [ft.SubType.K_1M, ft.SubType.TICKER])

# 查询并过滤
ret, data = quote_ctx.query_subscription()
if ret == ft.RET_OK:
    # 查看腾讯的订阅状态
    tencent_sub = data[data['code'] == 'HK.00700']
    if not tencent_sub.empty:
        print("腾讯订阅状态:", tencent_sub['sub_type'].iloc[0])
    
    # 统计订阅数量
    print(f"总共订阅了 {len(data)} 只股票")
    
    # 查看所有订阅类型
    all_types = []
    for types in data['sub_type_list']:
        all_types.extend(types)
    unique_types = list(set(all_types))
    print("订阅的数据类型:", unique_types)

quote_ctx.close()
```

#### 管理订阅状态

```python
import futu as ft

def manage_subscription(quote_ctx, target_codes, target_types):
    """管理订阅状态，确保只订阅指定的股票和类型"""
    
    # 查询当前订阅
    ret, current_data = quote_ctx.query_subscription()
    if ret != ft.RET_OK:
        print("查询订阅状态失败:", current_data)
        return
    
    # 取消不需要的订阅
    for _, row in current_data.iterrows():
        code = row['code']
        if code not in target_codes:
            # 取消整个股票的订阅
            quote_ctx.unsubscribe([code], [], unsubscribe_all=True)
            print(f"取消订阅: {code}")
    
    # 添加新的订阅
    for code in target_codes:
        current_code_data = current_data[current_data['code'] == code]
        if current_code_data.empty:
            # 新订阅
            quote_ctx.subscribe([code], target_types)
            print(f"新增订阅: {code}")
        else:
            # 检查订阅类型是否完整
            current_types = current_code_data['sub_type_list'].iloc[0]
            missing_types = [t for t in target_types if t not in current_types]
            if missing_types:
                quote_ctx.subscribe([code], missing_types)
                print(f"补充订阅 {code}: {missing_types}")

# 使用示例
quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

# 目标订阅
target_codes = ['HK.00700', 'HK.00001', 'HK.00005']
target_types = [ft.SubType.QUOTE, ft.SubType.K_1M]

# 管理订阅
manage_subscription(quote_ctx, target_codes, target_types)

# 验证结果
ret, data = quote_ctx.query_subscription()
if ret == ft.RET_OK:
    print("\n最终订阅状态:")
    print(data)

quote_ctx.close()
```

### 订阅类型说明

查询结果中的 `sub_type` 字段包含以下可能的值：

| 订阅类型 | 说明 |
|----------|------|
| QUOTE | 报价数据 |
| ORDER_BOOK | 摆盘数据 |
| TICKER | 逐笔数据 |
| K_1M | 1分钟K线 |
| K_3M | 3分钟K线 |
| K_5M | 5分钟K线 |
| K_15M | 15分钟K线 |
| K_30M | 30分钟K线 |
| K_60M | 60分钟K线 |
| K_DAY | 日K线 |
| K_WEEK | 周K线 |
| K_MONTH | 月K线 |
| RT_DATA | 分时数据 |
| BROKER | 经纪队列 |

### 注意事项

1. **实时性**：查询结果反映当前连接的订阅状态
2. **连接相关**：不同的连接有独立的订阅状态
3. **性能考虑**：建议在需要时才查询，避免频繁调用
4. **状态管理**：可用于验证订阅是否成功或管理订阅

### 常见应用场景

1. **启动检查**：程序启动时检查当前订阅状态
2. **订阅验证**：确认订阅操作是否成功
3. **状态管理**：动态管理订阅列表
4. **调试工具**：排查订阅相关问题

### 错误处理

```python
import futu as ft

quote_ctx = ft.OpenQuoteContext(host='127.0.0.1', port=11111)

try:
    ret, data = quote_ctx.query_subscription()
    if ret != ft.RET_OK:
        print(f"查询订阅状态失败: {data}")
    else:
        if data.empty:
            print("当前没有任何订阅")
        else:
            print(f"当前订阅了 {len(data)} 只股票")
            print(data)
except Exception as e:
    print(f"查询过程中发生异常: {e}")
finally:
    quote_ctx.close()
```

### 相关接口

- [订阅反订阅](sub.md) - 订阅和取消订阅数据
- [行情对象](base.md) - 行情上下文的基本用法