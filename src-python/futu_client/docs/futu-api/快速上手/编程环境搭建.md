# 编程环境搭建

> **注意**
>
> 不同的编程语言，编程环境搭建的方法有所不同。

## Python 环境

### 环境要求

-   操作系统要求：

    -   Windows 7/10 的 32 或 64 位操作系统
    -   Mac 10.11 及以上的 64 位操作系统
    -   CentOS 7 及以上的 64 位操作系统
    -   Ubuntu 16.04 以上的 64 位操作系统

-   Python 版本要求：
    -   Python 3.6 及以上

### 环境搭建

#### 1. 安装 Python

为避免因环境问题导致的运行失败，我们推荐 Python 3.8 版本。

下载地址：[Python 下载](https://www.python.org/downloads/)

当安装成功后，执行如下命令来查看是否安装成功：`python -V`（Windows） 或 `python3 -V`（Linux 和 Mac）

#### 2. 安装 PyCharm（可选）

我们推荐您使用 [PyCharm](https://www.jetbrains.com/pycharm/download/) 作为 Python IDE（集成开发环境）。

#### 3. 安装 TA-Lib（可选）

TA-Lib 用中文可以称作技术分析库，是一种广泛用在程序化交易中，进行金融市场数据的技术分析的函数库。它提供了多种技术分析的函数，方便我们量化投资中编程工作。

安装方法：在 cmd 中直接使用 uv 安装 `$ uv add TA-Lib`

> **提示**
>
> -   安装 TA-Lib 非必须，可先跳过该步骤
