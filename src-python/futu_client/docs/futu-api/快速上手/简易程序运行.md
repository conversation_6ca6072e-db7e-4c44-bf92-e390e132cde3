# 简易程序运行

## Python 示例

### 第一步：下载安装登录 OpenD

请参考[这里](opend-base.md)，完成 OpenD 的下载、安装和登录。

### 第二步：下载 Python API

-   **方式一**：在 cmd 中直接使用 uv 安装。

    -   初次安装：`$ uv add futu-api`
    -   二次升级：`$ uv add futu-api --upgrade`

-   **方式二**：点击下载最新版本的 [Python API](https://www.futunn.com/download/fetch-lasted-link?name=openapi-python) 安装包。

### 第三步：创建新项目

打开 PyCharm，在 Welcome to PyCharm 窗口中，点击 New Project。如果你已经创建了一个项目，可以选择打开该项目。

![创建新项目](https://openapi.futunn.com/futu-api-doc/assets/demo-newproject-7b644830.png)

### 第四步：创建新文件

在该项目下，创建新 Python 文件，并把下面的示例代码拷贝到文件里。示例代码功能包括查看行情快照、模拟交易下单。

```python
from futu import *

quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)  # 创建行情对象
print(quote_ctx.get_market_snapshot('HK.00700'))  # 获取港股 HK.00700 的快照数据
quote_ctx.close()  # 关闭对象，防止连接条数用尽

trd_ctx = OpenSecTradeContext(host='127.0.0.1', port=11111)  # 创建交易对象
print(trd_ctx.place_order(price=500.0, qty=100, code="HK.00700", trd_side=TrdSide.BUY, trd_env=TrdEnv.SIMULATE))  # 模拟交易，下单（如果是真实环境交易，在此之前需要先解锁交易密码）
trd_ctx.close()  # 关闭对象，防止连接条数用尽
```

### 第五步：运行文件

右键点击运行，可以看到运行成功的返回信息如下：

```
2020-11-05 17:09:29,705 [open_context_base.py] _socket_reconnect_and_wait_ready:255: Start connecting: host=127.0.0.1; port=11111;
2020-11-05 17:09:29,705 [open_context_base.py] on_connected:344: Connected : conn_id=1;
2020-11-05 17:09:29,706 [open_context_base.py] _handle_init_connect:445: InitConnect ok: conn_id=1; info={'server_version': 218, 'login_user_id': 7157878, 'conn_id': 6730043337026687703, 'conn_key': '3F17CF3EEF912C92', 'conn_iv': 'C119DDDD6314F18A', 'keep_alive_interval': 10, 'is_encrypt': False};
(0,          code      update_time  last_price  open_price  high_price  ...  after_high_price  after_low_price  after_change_val  after_change_rate  after_amplitude
0  HK.00700  2020-11-05 16:08:06       625.0       610.0       625.0  ...               N/A              N/A               N/A                N/A             N/A

[1 rows x 132 columns])
2020-11-05 17:09:29,739 [open_context_base.py] _socket_reconnect_and_wait_ready:255: Start connecting: host=127.0.0.1; port=11111;
2020-11-05 17:09:29,739 [network_manager.py] work:366: Close: conn_id=1
2020-11-05 17:09:29,739 [open_context_base.py] on_connected:344: Connected : conn_id=2;
2020-11-05 17:09:29,740 [open_context_base.py] _handle_init_connect:445: InitConnect ok: conn_id=2; info={'server_version': 218, 'login_user_id': 7157878, 'conn_id': 6730043337169705045, 'conn_key': 'A624CF3EEF91703C', 'conn_iv': 'BF1FF3806414617B', 'keep_alive_interval': 10, 'is_encrypt': False};
(0,          code stock_name trd_side order_type order_status  ...  dealt_avg_price last_err_msg remark time_in_force fill_outside_rth
0  HK.00700       腾讯控股      BUY     NORMAL   SUBMITTING  ...              0.0               DAY           N/A

[1 rows x 16 columns])
2020-11-05 17:09:32,843 [network_manager.py] work:366: Close: conn_id=2
(0,          code stock_name trd_side    order_type order_status  ...  dealt_avg_price last_err_msg remark time_in_force fill_outside_rth
0  HK.00700       腾讯控股      BUY  ABSOLUTE_LIMIT    SUBMITTED  ...              0.0               DAY           N/A

[1 rows x 16 columns])
```
