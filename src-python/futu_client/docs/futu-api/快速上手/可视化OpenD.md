# 可视化 OpenD

OpenD 提供可视化和命令行两种运行方式，这里介绍操作比较简单的可视化 OpenD。

如果想要了解命令行的方式请参考[命令行 OpenD](../opend/opend-cmd.md)。

## 可视化 OpenD

### 第一步 下载

可视化 OpenD 支持 Windows、MacOS、CentOS、Ubuntu 四种系统（点击完成下载）。

- OpenD - [Windows](https://www.futunn.com/download/fetch-lasted-link?name=opend-windows)、[MacOS](https://www.futunn.com/download/fetch-lasted-link?name=opend-macos)、[CenOS](https://www.futunn.com/download/fetch-lasted-link?name=opend-centos)、[Ubuntu](https://www.futunn.com/download/fetch-lasted-link?name=opend-ubuntu)

### 第二步 安装运行

- 解压文件，找到对应的安装文件可一键安装运行。
- Windows 系统默认安装在 `%appdata%` 目录下。

### 第三步 配置

- 可视化 OpenD 启动配置在图形界面的右侧，如下图所示：

![ui-config](https://openapi.futunn.com/futu-api-doc/assets/ui-config-6b644830.png)

**配置项列表**：

| 配置项 | 说明 |
|--------|------|
| 监听地址 | API 协议监听地址 |
| 监听端口 | API 协议监听端口 |
| 日志级别 | OpenD 日志级别 |
| 语言 | 中英语言 |
| 期货交易 API 时区 | 期货交易 API 时区 |
| API 推送频率 | API 订阅数据推送频率控制 |
| Telnet 地址 | 远程操作命令监听地址 |
| Telnet 端口 | 远程操作命令监听端口 |
| 加密私钥路径 | API 协议 [RSA](../qa/other.md#4601) 加密私钥（PKCS#1）文件绝对路径 |
| WebSocket 监听地址 | WebSocket 服务监听地址 |
| WebSocket 端口 | WebSocket 服务监听端口 |
| WebSocket 证书 | WebSocket 证书文件路径 |
| WebSocket 私钥 | WebSocket 证书私钥文件路径 |
| WebSocket 鉴权密钥 | 密钥密文（32 位 MD5 加密 16 进制） |

> **提示**
> 
> - 可视化 OpenD，是通过启动命令行 OpenD 来提供服务，且通过 WebSocket 与命令行 OpenD 交互，所以必定启动 WebSocket 功能。
> - 为保证您的证券业务账户安全，如果监听地址不是本地，您必须配置私钥才能使用交易接口。行情接口不受此限制。
> - 当 WebSocket 监听地址不是本地，需配置 SSL 才可以启动，且证书私钥生成不可设置密码。
> - 密文是明文经过 32 位 MD5 加密后用 16 进制表示的数据，搜索在线 MD5 加密（注意，通过第三方网站计算可能有记录撞库的风险）或下载 MD5 计算工具可计算得到。32 位 MD5 密文如下图红框区域（e10adc3949ba59abbe56e057f20f883e）：![md5示例](https://openapi.futunn.com/futu-api-doc/assets/md5-7b644830.png)
> - OpenD 默认读取同目录下的 OpenD.xml。在 MacOS 上，由于系统保护机制，OpenD.app 在运行时会被分配一个随机路径，导致无法找到原本的路径。此时有以下方法：
>   - 执行 tar 包下的 fixrun.sh
>   - 用命令行参数 `-cfg_file` 指定配置文件路径，见下面说明
> - 日志级别默认 info 级别，在系统开发阶段，不建议关闭日志或者将日志修改到 warning，error，fatal 级别，防止出现问题时无法定位。

### 第四步 登录

- 输入账号密码，点击登录。首次登录，您需要先完成问卷评估与协议确认，完成后重新登录即可。登录成功后，您可以看到自己的账号信息和[行情权限](../intro/authority.md#行情权限)。