# 富途 OpenAPI 快速参考

## 🚀 快速开始

### 1. 安装和连接

```bash
# 安装
uv add futu-api
```

```python

# 基础连接
from futu import *
quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
# 使用完毕后必须关闭
quote_ctx.close()
```

### 2. 数据订阅流程

```python
# 1. 订阅数据
ret_sub, err = quote_ctx.subscribe(['HK.08406'], [SubType.QUOTE])

# 2. 等待生效
import time
time.sleep(1)

# 3. 获取数据
ret, data = quote_ctx.get_stock_quote(['HK.08406'])
```

---

## 📊 核心 API 速查

### 基本行情

```python
# 订阅类型: SubType.QUOTE
# API: get_stock_quote()
ret, data = quote_ctx.get_stock_quote(['HK.08406'])

# 返回字段:
# - code: 股票代码
# - last_price: 最新价
# - change_rate: 涨跌幅
# - volume: 成交量
# - turnover: 成交额
```

### 逐笔交易

```python
# 订阅类型: SubType.TICKER
# API: get_rt_ticker()
ret, data = quote_ctx.get_rt_ticker('HK.08406', 20)

# 返回字段:
# - time: 交易时间
# - price: 成交价格
# - volume: 成交数量
# - ticker_direction: 交易方向 (BUY/SELL/NEUTRAL)
# - type: 交易类型 (AUTO_MATCH/AUCTION)
```

### 买卖盘

```python
# 订阅类型: SubType.ORDER_BOOK
# API: get_order_book()
ret, data = quote_ctx.get_order_book('HK.08406')

# 返回结构:
# {
#   'Bid': [(价格, 数量, 笔数, {}), ...],  # 买盘
#   'Ask': [(价格, 数量, 笔数, {}), ...]   # 卖盘
# }
```

### 经纪队列

```python
# 订阅类型: SubType.BROKER
# API: get_broker_queue()
ret, ask_data, bid_data = quote_ctx.get_broker_queue('HK.08406')

# bid_data (买盘经纪): 显示卖方经纪信息
# - ask_broker_id: 经纪ID
# - ask_broker_name: 经纪名称
# - ask_broker_pos: 队列位置

# ask_data (卖盘经纪): 显示买方经纪信息
# - bid_broker_id: 经纪ID
# - bid_broker_name: 经纪名称
# - bid_broker_pos: 队列位置
```

---

## 🔧 实用代码模板

### 安全连接模板

```python
from contextlib import contextmanager

@contextmanager
def futu_connection():
    quote_ctx = OpenQuoteContext(host='127.0.0.1', port=11111)
    try:
        yield quote_ctx
    finally:
        quote_ctx.close()

# 使用
with futu_connection() as ctx:
    ret, data = ctx.get_stock_quote(['HK.08406'])
```

### 完整数据获取模板

```python
def get_complete_data(stock_code):
    """获取股票完整数据"""
    with futu_connection() as ctx:
        # 订阅所有数据类型
        sub_types = [SubType.QUOTE, SubType.TICKER, SubType.ORDER_BOOK, SubType.BROKER]
        ret_sub, _ = ctx.subscribe([stock_code], sub_types)

        if ret_sub != RET_OK:
            return None

        time.sleep(1)  # 等待订阅生效

        result = {}

        # 1. 基本行情
        ret, data = ctx.get_stock_quote([stock_code])
        result['quote'] = data if ret == RET_OK else None

        # 2. 逐笔交易
        ret, data = ctx.get_rt_ticker(stock_code, 20)
        result['ticks'] = data if ret == RET_OK else None

        # 3. 买卖盘
        ret, data = ctx.get_order_book(stock_code)
        result['orderbook'] = data if ret == RET_OK else None

        # 4. 经纪队列
        ret, ask_data, bid_data = ctx.get_broker_queue(stock_code)
        result['brokers'] = {'ask': ask_data, 'bid': bid_data} if ret == RET_OK else None

        return result

# 使用
data = get_complete_data('HK.08406')
```

### 错误处理模板

```python
def safe_api_call(api_func, *args, **kwargs):
    """安全的API调用"""
    try:
        ret, data = api_func(*args, **kwargs)
        if ret == RET_OK:
            return data
        else:
            print(f"API调用失败: {data}")
            return None
    except Exception as e:
        print(f"API调用异常: {e}")
        return None

# 使用
with futu_connection() as ctx:
    quote_data = safe_api_call(ctx.get_stock_quote, ['HK.08406'])
```

---

## ⚠️ 常见问题

### 连接失败

```python
# 检查清单:
# 1. 富途牛牛客户端是否启动并登录
# 2. 是否开启OpenAPI (设置→开发者选项→启用OpenAPI)
# 3. 防火墙是否阻止端口11111
# 4. 是否有其他程序占用端口11111
```

### 数据为空

```python
# 可能原因:
# 1. 未先订阅数据
# 2. 订阅后未等待生效 (建议sleep 1秒)
# 3. 股票代码格式错误 (应为 HK.08406 格式)
# 4. 超出订阅限制 (免费账户最多10只股票)
```

### 字段显示 N/A

```python
# 处理方法:
def safe_get_value(data, field, default='未知'):
    try:
        value = data.get(field, default)
        return default if str(value).upper() in ['N/A', 'NAN', 'NONE'] else value
    except:
        return default
```

---

## 📋 订阅类型对照表

| 订阅类型             | 对应 API             | 用途     | 免费账户 |
| -------------------- | -------------------- | -------- | -------- |
| `SubType.QUOTE`      | `get_stock_quote()`  | 基本行情 | ✅       |
| `SubType.TICKER`     | `get_rt_ticker()`    | 逐笔交易 | ✅       |
| `SubType.ORDER_BOOK` | `get_order_book()`   | 买卖盘   | ✅       |
| `SubType.BROKER`     | `get_broker_queue()` | 经纪队列 | ✅       |

---

## 🎯 最佳实践

1. **总是使用上下文管理器**确保连接正确关闭
2. **先订阅再获取**，订阅后等待 1 秒
3. **处理异常情况**，检查返回值和数据有效性
4. **控制调用频率**，避免过于频繁的 API 调用
5. **及时取消订阅**，避免超出订阅限制

---

## 🔗 相关链接

-   [完整文档](./富途OpenAPI详细使用文档.md)
-   [示例代码](./stock_detail_query.py)
-   [官方文档](https://openapi.futunn.com/)

---

_快速参考 - 2025-07-13_
