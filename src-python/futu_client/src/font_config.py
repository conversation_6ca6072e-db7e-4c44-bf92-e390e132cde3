#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨平台字体配置
================
使用系统默认字体，无需打包额外字体文件
"""

import platform
from pathlib import Path

def get_system_fonts():
    """获取系统默认中文字体路径"""
    system = platform.system()
    
    if system == "Darwin":  # macOS
        return [
            "/System/Library/Fonts/PingFang.ttc",          # 苹方（主推荐）
            "/System/Library/Fonts/STHeiti Medium.ttc",     # 黑体
            "/System/Library/Fonts/Helvetica.ttc",         # Helvetica（备用）
            "/System/Library/Fonts/Arial Unicode MS.ttf",  # Arial Unicode
        ]
    elif system == "Windows":
        return [
            "C:/Windows/Fonts/msyh.ttc",      # 微软雅黑（主推荐）
            "C:/Windows/Fonts/simhei.ttf",    # 黑体
            "C:/Windows/Fonts/simsun.ttc",    # 宋体
            "C:/Windows/Fonts/arial.ttf",     # Arial（备用）
        ]
    else:  # Linux
        return [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansCJK-Regular.ttc",  # 如果系统安装了
        ]

def get_all_font_paths():
    """获取所有可用的字体路径（仅系统字体）"""
    return get_system_fonts()