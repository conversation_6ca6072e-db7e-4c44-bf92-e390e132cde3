#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
富途OpenAPI 股票数据管理器
========================
专为软件开发设计的数据管理类，支持实时推送和按需查询

特点：
1. 实时数据推送 - 无频率限制
2. 数据缓存管理 - 提高响应速度
3. 事件回调机制 - 便于GUI集成
4. 线程安全设计 - 支持多线程环境

作者：AI助手
日期：2025-07-14
"""

from futu import *
import pandas as pd
import threading
import time
from datetime import datetime
from typing import Dict, List, Callable, Optional
from dataclasses import dataclass
from enum import Enum

class DataType(Enum):
    """数据类型枚举"""
    QUOTE = "quote"           # 基本行情
    TICKER = "ticker"         # 逐笔交易
    ORDER_BOOK = "orderbook"  # 买卖盘
    BROKER = "broker"         # 经纪队列

@dataclass
class StockData:
    """股票数据结构"""
    code: str
    name: str
    last_price: float
    change_rate: float
    volume: int
    turnover: float
    update_time: str

@dataclass
class TickerData:
    """逐笔交易数据结构"""
    time: str
    price: float
    volume: int
    turnover: float
    direction: str
    trade_type: str

@dataclass
class OrderBookData:
    """买卖盘数据结构"""
    bid_list: List[Dict]  # 买盘 [{'price': 0.111, 'volume': 1000, 'orders': 1}]
    ask_list: List[Dict]  # 卖盘
    update_time: str

@dataclass
class BrokerData:
    """经纪队列数据结构"""
    bid_brokers: List[Dict]  # 买盘经纪 [{'broker_id': 'B001', 'broker_name': '券商名'}]
    ask_brokers: List[Dict]  # 卖盘经纪
    update_time: str

class StockDataManager:
    """股票数据管理器"""
    
    def __init__(self, host='127.0.0.1', port=11111):
        self.host = host
        self.port = port
        self.quote_ctx = None
        
        # 数据存储
        self._data_lock = threading.RLock()
        self._stock_data: Dict[str, StockData] = {}
        self._ticker_data: Dict[str, List[TickerData]] = {}
        self._orderbook_data: Dict[str, OrderBookData] = {}
        self._broker_data: Dict[str, BrokerData] = {}
        
        # 订阅管理
        self._subscribed_stocks = set()
        self._running = False
        
        # 回调函数
        self._callbacks: Dict[DataType, List[Callable]] = {
            DataType.QUOTE: [],
            DataType.TICKER: [],
            DataType.ORDER_BOOK: [],
            DataType.BROKER: []
        }
        
        # 处理器
        self._handlers = {}
        
    def add_callback(self, data_type: DataType, callback: Callable):
        """添加数据更新回调函数"""
        self._callbacks[data_type].append(callback)
    
    def remove_callback(self, data_type: DataType, callback: Callable):
        """移除数据更新回调函数"""
        if callback in self._callbacks[data_type]:
            self._callbacks[data_type].remove(callback)
    
    def _notify_callbacks(self, data_type: DataType, stock_code: str, data):
        """通知回调函数"""
        for callback in self._callbacks[data_type]:
            try:
                callback(stock_code, data)
            except Exception as e:
                print(f"回调函数执行异常: {e}")
    
    def start(self):
        """启动数据管理器"""
        if self._running:
            return
        
        try:
            self.quote_ctx = OpenQuoteContext(host=self.host, port=self.port)
            self._setup_handlers()
            self._running = True
            # print("✅ 股票数据管理器启动成功")
            return True
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def stop(self):
        """停止数据管理器"""
        self._running = False
        if self.quote_ctx:
            try:
                self.quote_ctx.close()
                print("🔌 数据管理器已停止")
            except:
                pass
    
    def _setup_handlers(self):
        """设置数据处理器"""
        
        class QuoteHandler(StockQuoteHandlerBase):
            def __init__(self, manager):
                super().__init__()
                self.manager = manager
            
            def on_recv_rsp(self, rsp_pb):
                ret_code, quote_list = super().on_recv_rsp(rsp_pb)
                if ret_code == RET_OK:
                    self.manager._handle_quote_data(quote_list)
                return ret_code, quote_list
        
        class TickerHandler(TickerHandlerBase):
            def __init__(self, manager):
                super().__init__()
                self.manager = manager
            
            def on_recv_rsp(self, rsp_pb):
                ret_code, ticker_list = super().on_recv_rsp(rsp_pb)
                if ret_code == RET_OK:
                    self.manager._handle_ticker_data(ticker_list)
                return ret_code, ticker_list
        
        class OrderBookHandler(OrderBookHandlerBase):
            def __init__(self, manager):
                super().__init__()
                self.manager = manager

            def on_recv_rsp(self, rsp_pb):
                ret_code, orderbook_list = super().on_recv_rsp(rsp_pb)
                if ret_code == RET_OK:
                    self.manager._handle_orderbook_data(orderbook_list)
                return ret_code, orderbook_list

        class BrokerHandler(BrokerHandlerBase):
            def __init__(self, manager):
                super().__init__()
                self.manager = manager

            def on_recv_rsp(self, rsp_pb):
                ret_code, broker_list = super().on_recv_rsp(rsp_pb)
                if ret_code == RET_OK:
                    self.manager._handle_broker_data(broker_list)
                return ret_code, broker_list

        # 创建并设置处理器
        self._handlers['quote'] = QuoteHandler(self)
        self._handlers['ticker'] = TickerHandler(self)
        self._handlers['orderbook'] = OrderBookHandler(self)
        self._handlers['broker'] = BrokerHandler(self)

        self.quote_ctx.set_handler(self._handlers['quote'])
        self.quote_ctx.set_handler(self._handlers['ticker'])
        self.quote_ctx.set_handler(self._handlers['orderbook'])
        self.quote_ctx.set_handler(self._handlers['broker'])
    
    def _handle_quote_data(self, quote_list):
        """处理基本行情数据"""
        with self._data_lock:
            for quote in quote_list:
                code = quote['code']
                stock_data = StockData(
                    code=code,
                    name=quote.get('stock_name', ''),
                    last_price=float(quote['last_price']),
                    change_rate=float(quote['change_rate']),
                    volume=int(quote['volume']),
                    turnover=float(quote['turnover']),
                    update_time=datetime.now().strftime('%H:%M:%S')
                )
                self._stock_data[code] = stock_data
                
                # 通知回调
                self._notify_callbacks(DataType.QUOTE, code, stock_data)
    
    def _handle_ticker_data(self, ticker_list):
        """处理逐笔交易数据"""
        with self._data_lock:
            for ticker in ticker_list:
                code = ticker['code']
                
                # 解析交易方向和类型
                direction_map = {
                    'BUY': '买盘',
                    'SELL': '卖盘', 
                    'NEUTRAL': '中性'
                }
                type_map = {
                    'AUTO_MATCH': '自动撮合',
                    'AUCTION': '竞价交易',
                    'ODD_LOT': '碎股交易'
                }
                
                ticker_data = TickerData(
                    time=ticker['time'],
                    price=float(ticker['price']),
                    volume=int(ticker['volume']),
                    turnover=float(ticker['turnover']),
                    direction=direction_map.get(ticker.get('ticker_direction'), '未知'),
                    trade_type=type_map.get(ticker.get('type'), '未知')
                )
                
                # 保存到列表，保持最新50笔
                if code not in self._ticker_data:
                    self._ticker_data[code] = []
                
                self._ticker_data[code].insert(0, ticker_data)
                if len(self._ticker_data[code]) > 50:
                    self._ticker_data[code] = self._ticker_data[code][:50]
                
                # 通知回调
                self._notify_callbacks(DataType.TICKER, code, ticker_data)
    
    def _handle_orderbook_data(self, orderbook_list):
        """处理买卖盘数据"""
        with self._data_lock:
            for orderbook in orderbook_list:
                code = orderbook['code']
                
                # 解析买卖盘
                bid_list = []
                ask_list = []
                
                if 'Bid' in orderbook:
                    for bid in orderbook['Bid'][:10]:
                        if len(bid) >= 3:
                            bid_list.append({
                                'price': float(bid[0]),
                                'volume': int(bid[1]),
                                'orders': int(bid[2])
                            })
                
                if 'Ask' in orderbook:
                    for ask in orderbook['Ask'][:10]:
                        if len(ask) >= 3:
                            ask_list.append({
                                'price': float(ask[0]),
                                'volume': int(ask[1]),
                                'orders': int(ask[2])
                            })
                
                orderbook_data = OrderBookData(
                    bid_list=bid_list,
                    ask_list=ask_list,
                    update_time=datetime.now().strftime('%H:%M:%S')
                )
                
                self._orderbook_data[code] = orderbook_data
                
                # 通知回调
                self._notify_callbacks(DataType.ORDER_BOOK, code, orderbook_data)

    def _handle_broker_data(self, broker_list):
        """处理经纪队列数据"""
        with self._data_lock:
            for broker in broker_list:
                code = broker['code']

                # 解析买卖盘经纪
                bid_brokers = []
                ask_brokers = []

                if 'Bid' in broker:
                    for bid_broker in broker['Bid'][:10]:
                        if len(bid_broker) >= 2:
                            bid_brokers.append({
                                'broker_id': str(bid_broker[0]),
                                'broker_name': str(bid_broker[1])
                            })

                if 'Ask' in broker:
                    for ask_broker in broker['Ask'][:10]:
                        if len(ask_broker) >= 2:
                            ask_brokers.append({
                                'broker_id': str(ask_broker[0]),
                                'broker_name': str(ask_broker[1])
                            })

                broker_data = BrokerData(
                    bid_brokers=bid_brokers,
                    ask_brokers=ask_brokers,
                    update_time=datetime.now().strftime('%H:%M:%S')
                )

                self._broker_data[code] = broker_data

                # 通知回调
                self._notify_callbacks(DataType.BROKER, code, broker_data)
    
    def subscribe_stock(self, stock_code: str, data_types: List[DataType] = None):
        """订阅股票数据"""
        if not self._running:
            print("❌ 数据管理器未启动")
            return False
        
        if data_types is None:
            data_types = [DataType.QUOTE, DataType.TICKER, DataType.ORDER_BOOK]
        
        try:
            # 转换为富途API的订阅类型
            sub_types = []
            for data_type in data_types:
                if data_type == DataType.QUOTE:
                    sub_types.append(SubType.QUOTE)
                elif data_type == DataType.TICKER:
                    sub_types.append(SubType.TICKER)
                elif data_type == DataType.ORDER_BOOK:
                    sub_types.append(SubType.ORDER_BOOK)
                elif data_type == DataType.BROKER:
                    sub_types.append(SubType.BROKER)
            
            ret_sub, err_message = self.quote_ctx.subscribe([stock_code], sub_types)
            if ret_sub == RET_OK:
                self._subscribed_stocks.add(stock_code)
                print(f"✅ 成功订阅 {stock_code}")
                return True
            else:
                print(f"❌ 订阅失败 {stock_code}: {err_message}")
                return False
                
        except Exception as e:
            print(f"❌ 订阅异常: {e}")
            return False
    
    def unsubscribe_stock(self, stock_code: str):
        """取消订阅股票"""
        if stock_code in self._subscribed_stocks:
            try:
                ret_unsub, err_message = self.quote_ctx.unsubscribe([stock_code], [SubType.QUOTE, SubType.TICKER, SubType.ORDER_BOOK])
                if ret_unsub == RET_OK:
                    self._subscribed_stocks.remove(stock_code)
                    print(f"✅ 取消订阅 {stock_code}")
                    return True
                else:
                    print(f"❌ 取消订阅失败: {err_message}")
                    return False
            except Exception as e:
                print(f"❌ 取消订阅异常: {e}")
                return False
    
    def get_stock_data(self, stock_code: str) -> Optional[StockData]:
        """获取股票基本数据"""
        with self._data_lock:
            return self._stock_data.get(stock_code)
    
    def get_ticker_data(self, stock_code: str, count: int = 20) -> List[TickerData]:
        """获取逐笔交易数据"""
        with self._data_lock:
            return self._ticker_data.get(stock_code, [])[:count]
    
    def get_orderbook_data(self, stock_code: str) -> Optional[OrderBookData]:
        """获取买卖盘数据"""
        with self._data_lock:
            return self._orderbook_data.get(stock_code)

    def get_broker_data(self, stock_code: str) -> Optional[BrokerData]:
        """获取经纪队列数据"""
        with self._data_lock:
            return self._broker_data.get(stock_code)

    def get_subscribed_stocks(self) -> List[str]:
        """获取已订阅的股票列表"""
        return list(self._subscribed_stocks)

    def is_running(self) -> bool:
        """检查是否运行中"""
        return self._running
