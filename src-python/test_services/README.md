# 测试计数器服务

## 📋 概述

这是一个用于测试 **Tauri Sidecar + 标准I/O** 通信方案的计数器服务，模拟富途和华盛的实时数据推送。

## 🎯 目的

在真正对接富途或华盛之前，先用这个简单的计数器服务验证新的通信方式是否工作正常。

## 📁 文件结构

```
test_services/
├── counter_service.py      # 主要的计数器服务
├── test_counter.py         # 直接测试计数器服务
├── tauri_sidecar_test.py   # 模拟 Tauri Sidecar 管理器
├── run_tests.py            # 测试运行器
└── README.md               # 本文档
```

## 🔧 功能特性

### 计数器服务 (counter_service.py)

#### 模拟的数据流
- **富途行情数据** (`futu_quote`): 每1秒推送一次股价数据
- **富途逐笔数据** (`futu_ticker`): 每2秒推送一次交易数据
- **华盛资金数据** (`huasheng_funds`): 每3秒推送一次资金数据
- **华盛订单数据** (`huasheng_orders`): 每4秒推送一次订单数据

#### 支持的命令
- `ping`: 心跳检查
- `start_counter`: 启动指定计数器
- `stop_counter`: 停止指定计数器
- `get_counter`: 获取计数器当前值
- `reset_counter`: 重置计数器

#### 通信协议
- **输入**: 通过 `stdin` 接收 JSON 格式的命令
- **输出**: 通过 `stdout` 发送 JSON 格式的响应和推送数据
- **日志**: 通过 `stderr` 输出调试日志

### 消息格式

#### 命令格式 (stdin)
```json
{
  "id": "command-uuid",
  "action": "ping",
  "params": {},
  "timestamp": 1234567890.123
}
```

#### 响应格式 (stdout)
```json
{
  "type": "response",
  "command_id": "command-uuid",
  "success": true,
  "data": {"status": "alive"},
  "timestamp": 1234567890.123
}
```

#### 推送格式 (stdout)
```json
{
  "type": "push",
  "source": "futu_quote",
  "data": {
    "symbol": "HK.00700",
    "price": 305.5,
    "counter": 42
  },
  "timestamp": 1234567890.123
}
```

## 🚀 使用方法

### 1. 直接运行计数器服务

```bash
cd src-python/test_services
python counter_service.py
```

然后可以通过 stdin 发送命令：
```bash
echo '{"id":"test1","action":"ping","params":{}}' | python counter_service.py
```

### 2. 运行单独测试

```bash
# 测试计数器服务基本功能
python test_counter.py

# 测试 Tauri Sidecar 通信
python tauri_sidecar_test.py
```

### 3. 运行完整测试套件

```bash
python run_tests.py
```

## 📊 测试场景

### 场景1: 基本功能测试
- 启动计数器服务
- 测试各种命令
- 观察实时数据推送
- 验证计数器递增

### 场景2: Sidecar 通信测试
- 模拟 Tauri 后端启动 Python 进程
- 测试双向通信
- 验证消息格式
- 测试错误处理

### 场景3: 压力测试
- 快速发送多个命令
- 测试并发处理能力
- 验证响应匹配

## 📈 预期输出示例

```
🧪 Tauri Sidecar 通信测试
==================================================
🚀 启动 Python Sidecar...
✅ Python Sidecar 已启动
👂 开始监听 stdout...
🔍 [Sidecar Log] 2025-07-20 14:30:15,123 - __main__ - INFO - Starting Counter Service...
🎉 系统消息: {'status': 'started', 'service': 'counter'}

🔧 测试基本命令...
📤 发送命令: ping (ID: 12345678...)
✅ 命令响应 [12345678...]: {'status': 'alive', 'timestamp': 1234567890.123}

📡 观察实时数据推送 (20秒)...
📈 富途行情 - HK.00700: ¥302.45 (计数: 1)
📊 富途逐笔 - HK.00700: ¥301.80 买盘 (计数: 1)
💰 华盛资金 - 可用: ¥49,850.30 (计数: 1)
📋 华盛订单 - ORD000001: 买入 已报 (计数: 1)
```

## 🎯 验证要点

### ✅ 通信正常
- [x] stdin 命令接收正常
- [x] stdout 响应发送正常
- [x] stderr 日志输出正常
- [x] JSON 格式解析正确

### ✅ 数据推送
- [x] 多个数据流并行推送
- [x] 计数器正确递增
- [x] 时间戳准确
- [x] 数据格式一致

### ✅ 错误处理
- [x] 无效命令处理
- [x] JSON 解析错误处理
- [x] 进程优雅关闭
- [x] 异常恢复

## 🔄 下一步

验证计数器服务正常工作后，可以：

1. **集成到 Tauri**: 将测试代码集成到真实的 Tauri 应用中
2. **替换为真实服务**: 将计数器服务替换为富途/华盛的真实适配器
3. **前端集成**: 在 React 前端中显示实时数据
4. **性能优化**: 根据测试结果优化通信性能

## 🐛 故障排除

### 常见问题

1. **进程启动失败**
   - 检查 Python 路径
   - 确认文件权限
   - 查看错误日志

2. **JSON 解析错误**
   - 检查命令格式
   - 确认编码正确
   - 验证换行符

3. **数据推送中断**
   - 检查进程状态
   - 查看 stderr 日志
   - 验证网络连接

### 调试技巧

```bash
# 启用详细日志
export PYTHONUNBUFFERED=1
python counter_service.py

# 手动测试命令
echo '{"id":"debug","action":"get_counter","params":{"counter":"all"}}' | python counter_service.py

# 查看进程状态
ps aux | grep counter_service
```

## 📝 总结

这个测试计数器服务提供了一个完整的 **Tauri Sidecar + 标准I/O** 通信方案的验证环境，确保在集成真实的富途和华盛服务之前，通信机制已经完全验证和优化。
