#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计数器服务测试脚本
================
测试计数器服务的各种功能
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path

class CounterServiceTester:
    """计数器服务测试器"""
    
    def __init__(self):
        self.process = None
        self.service_path = Path(__file__).parent / "counter_service.py"
    
    async def start_service(self):
        """启动计数器服务"""
        print("🚀 启动计数器服务...")
        
        self.process = await asyncio.create_subprocess_exec(
            sys.executable, str(self.service_path),
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        print("✅ 计数器服务已启动")
        return self.process
    
    async def send_command(self, action: str, params: dict = None):
        """发送命令到服务"""
        if not self.process:
            raise RuntimeError("Service not started")
        
        command = {
            "id": f"test_{int(time.time() * 1000)}",
            "action": action,
            "params": params or {},
            "timestamp": time.time()
        }
        
        command_json = json.dumps(command) + "\n"
        print(f"📤 发送命令: {action}")
        
        self.process.stdin.write(command_json.encode())
        await self.process.stdin.drain()
    
    async def read_output(self, timeout=5):
        """读取服务输出"""
        try:
            line = await asyncio.wait_for(
                self.process.stdout.readline(), 
                timeout=timeout
            )
            if line:
                data = json.loads(line.decode().strip())
                return data
        except asyncio.TimeoutError:
            print("⏰ 读取输出超时")
        except json.JSONDecodeError as e:
            print(f"❌ JSON 解析错误: {e}")
        return None
    
    async def read_stderr(self):
        """读取错误输出"""
        try:
            line = await asyncio.wait_for(
                self.process.stderr.readline(), 
                timeout=1
            )
            if line:
                return line.decode().strip()
        except asyncio.TimeoutError:
            pass
        return None
    
    async def test_basic_commands(self):
        """测试基本命令"""
        print("\n🧪 测试基本命令...")
        
        # 测试 ping
        await self.send_command("ping")
        response = await self.read_output()
        if response and response.get("type") == "response":
            print(f"✅ Ping 响应: {response['data']}")
        
        # 测试获取计数器
        await self.send_command("get_counter", {"counter": "all"})
        response = await self.read_output()
        if response and response.get("type") == "response":
            print(f"✅ 计数器状态: {response['data']}")
        
        # 测试重置计数器
        await self.send_command("reset_counter", {"counter": "futu_quote"})
        response = await self.read_output()
        if response and response.get("type") == "response":
            print(f"✅ 重置计数器: {response['data']}")
    
    async def test_real_time_data(self, duration=10):
        """测试实时数据推送"""
        print(f"\n📡 测试实时数据推送 ({duration}秒)...")
        
        start_time = time.time()
        data_count = {"futu_quote": 0, "futu_ticker": 0, "huasheng_funds": 0, "huasheng_orders": 0}
        
        while time.time() - start_time < duration:
            # 读取推送数据
            data = await self.read_output(timeout=1)
            if data and data.get("type") == "push":
                source = data.get("source")
                if source in data_count:
                    data_count[source] += 1
                    counter_value = data.get("data", {}).get("counter", "N/A")
                    print(f"📊 {source}: 计数器={counter_value}, 接收次数={data_count[source]}")
            
            # 读取错误日志
            stderr = await self.read_stderr()
            if stderr:
                print(f"🔍 日志: {stderr}")
        
        print(f"\n📈 数据推送统计:")
        for source, count in data_count.items():
            print(f"  {source}: {count} 次")
    
    async def stop_service(self):
        """停止服务"""
        if self.process:
            print("\n🛑 停止计数器服务...")
            self.process.terminate()
            await self.process.wait()
            print("✅ 服务已停止")

async def main():
    """主测试函数"""
    print("🧪 计数器服务测试开始")
    print("=" * 50)
    
    tester = CounterServiceTester()
    
    try:
        # 启动服务
        await tester.start_service()
        
        # 等待服务启动
        await asyncio.sleep(2)
        
        # 读取启动消息
        startup_data = await tester.read_output()
        if startup_data:
            print(f"🎉 服务启动消息: {startup_data}")
        
        # 测试基本命令
        await tester.test_basic_commands()
        
        # 测试实时数据推送
        await tester.test_real_time_data(duration=15)
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        await tester.stop_service()

if __name__ == "__main__":
    asyncio.run(main())
