#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tauri Sidecar 测试管理器
======================
模拟 Tauri 后端与 Python Sidecar 的通信
"""

import asyncio
import json
import sys
import time
import uuid
from pathlib import Path
from typing import Dict, Optional
import subprocess

class TauriSidecarManager:
    """模拟 Tauri Sidecar 管理器"""
    
    def __init__(self):
        self.process = None
        self.service_path = Path(__file__).parent / "counter_service.py"
        self.pending_commands = {}
        self.running = False
    
    async def start_sidecar(self):
        """启动 Python Sidecar 进程"""
        print("🚀 启动 Python Sidecar...")
        
        self.process = await asyncio.create_subprocess_exec(
            sys.executable, str(self.service_path),
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        self.running = True
        
        # 启动输出监听器
        asyncio.create_task(self.stdout_listener())
        asyncio.create_task(self.stderr_listener())
        
        print("✅ Python Sidecar 已启动")
        return True
    
    async def stdout_listener(self):
        """监听 stdout 输出"""
        print("👂 开始监听 stdout...")
        
        while self.running and self.process:
            try:
                line = await self.process.stdout.readline()
                if not line:
                    break
                
                try:
                    message = json.loads(line.decode().strip())
                    await self.handle_message(message)
                except json.JSONDecodeError as e:
                    print(f"❌ JSON 解析错误: {e}")
                    
            except Exception as e:
                print(f"❌ stdout 监听错误: {e}")
                break
        
        print("👂 stdout 监听结束")
    
    async def stderr_listener(self):
        """监听 stderr 输出"""
        while self.running and self.process:
            try:
                line = await self.process.stderr.readline()
                if not line:
                    break
                
                log_line = line.decode().strip()
                if log_line:
                    print(f"🔍 [Sidecar Log] {log_line}")
                    
            except Exception as e:
                print(f"❌ stderr 监听错误: {e}")
                break
    
    async def handle_message(self, message: Dict):
        """处理来自 Sidecar 的消息"""
        msg_type = message.get("type")
        
        if msg_type == "push":
            # 实时数据推送
            await self.handle_push_data(message)
        elif msg_type == "response":
            # 命令响应
            await self.handle_command_response(message)
        else:
            print(f"❓ 未知消息类型: {msg_type}")
    
    async def handle_push_data(self, message: Dict):
        """处理实时推送数据"""
        source = message.get("source")
        data = message.get("data", {})
        timestamp = message.get("timestamp")
        
        if source == "system":
            print(f"🎉 系统消息: {data}")
        elif source == "futu_quote":
            symbol = data.get("symbol", "N/A")
            price = data.get("price", 0)
            counter = data.get("counter", 0)
            print(f"📈 富途行情 - {symbol}: ¥{price:.2f} (计数: {counter})")
        elif source == "futu_ticker":
            symbol = data.get("symbol", "N/A")
            price = data.get("price", 0)
            direction = data.get("direction", "N/A")
            counter = data.get("counter", 0)
            print(f"📊 富途逐笔 - {symbol}: ¥{price:.2f} {direction} (计数: {counter})")
        elif source == "huasheng_funds":
            available = data.get("available_funds", 0)
            counter = data.get("counter", 0)
            print(f"💰 华盛资金 - 可用: ¥{available:.2f} (计数: {counter})")
        elif source == "huasheng_orders":
            order_id = data.get("order_id", "N/A")
            side = data.get("side", "N/A")
            status = data.get("status", "N/A")
            counter = data.get("counter", 0)
            print(f"📋 华盛订单 - {order_id}: {side} {status} (计数: {counter})")
        else:
            print(f"📡 推送数据 [{source}]: {data}")
    
    async def handle_command_response(self, message: Dict):
        """处理命令响应"""
        command_id = message.get("command_id")
        success = message.get("success", False)
        data = message.get("data")
        error = message.get("error")
        
        if command_id in self.pending_commands:
            future = self.pending_commands.pop(command_id)
            if not future.done():
                future.set_result(message)
        
        status = "✅" if success else "❌"
        print(f"{status} 命令响应 [{command_id}]: {data if success else error}")
    
    async def send_command(self, action: str, params: Dict = None) -> Optional[Dict]:
        """发送命令到 Sidecar"""
        if not self.process:
            raise RuntimeError("Sidecar not started")
        
        command_id = str(uuid.uuid4())
        command = {
            "id": command_id,
            "action": action,
            "params": params or {},
            "timestamp": time.time()
        }
        
        # 创建 Future 等待响应
        future = asyncio.Future()
        self.pending_commands[command_id] = future
        
        # 发送命令
        command_json = json.dumps(command) + "\n"
        self.process.stdin.write(command_json.encode())
        await self.process.stdin.drain()
        
        print(f"📤 发送命令: {action} (ID: {command_id[:8]}...)")
        
        try:
            # 等待响应
            response = await asyncio.wait_for(future, timeout=10)
            return response
        except asyncio.TimeoutError:
            print(f"⏰ 命令超时: {action}")
            self.pending_commands.pop(command_id, None)
            return None
    
    async def stop_sidecar(self):
        """停止 Sidecar"""
        print("🛑 停止 Python Sidecar...")
        self.running = False
        
        if self.process:
            self.process.terminate()
            await self.process.wait()
        
        print("✅ Python Sidecar 已停止")

async def test_sidecar_communication():
    """测试 Sidecar 通信"""
    print("🧪 Tauri Sidecar 通信测试")
    print("=" * 50)
    
    manager = TauriSidecarManager()
    
    try:
        # 启动 Sidecar
        await manager.start_sidecar()
        
        # 等待启动完成
        await asyncio.sleep(2)
        
        # 测试基本命令
        print("\n🔧 测试基本命令...")
        
        # Ping 测试
        response = await manager.send_command("ping")
        
        # 获取计数器状态
        response = await manager.send_command("get_counter", {"counter": "all"})
        
        # 重置计数器
        response = await manager.send_command("reset_counter", {"counter": "futu_quote"})
        
        # 观察实时数据推送
        print("\n📡 观察实时数据推送 (20秒)...")
        await asyncio.sleep(20)
        
        # 再次获取计数器状态
        print("\n📊 获取最终计数器状态...")
        response = await manager.send_command("get_counter", {"counter": "all"})
        
        print("\n✅ 通信测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await manager.stop_sidecar()

if __name__ == "__main__":
    asyncio.run(test_sidecar_communication())
