#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
=========
运行各种测试场景
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, str(Path(__file__).parent))

from test_counter import CounterServiceTester
from tauri_sidecar_test import TauriSidecarManager

async def test_scenario_1():
    """测试场景1: 直接测试计数器服务"""
    print("🎯 测试场景1: 直接测试计数器服务")
    print("=" * 60)
    
    tester = CounterServiceTester()
    
    try:
        await tester.start_service()
        await asyncio.sleep(2)
        
        # 读取启动消息
        startup_data = await tester.read_output()
        if startup_data:
            print(f"🎉 服务启动: {startup_data}")
        
        # 测试命令
        await tester.test_basic_commands()
        
        # 观察数据推送
        await tester.test_real_time_data(duration=10)
        
    finally:
        await tester.stop_service()

async def test_scenario_2():
    """测试场景2: <PERSON>ri Sidecar 通信测试"""
    print("\n🎯 测试场景2: <PERSON><PERSON> Sidecar 通信测试")
    print("=" * 60)
    
    manager = TauriSidecarManager()
    
    try:
        await manager.start_sidecar()
        await asyncio.sleep(2)
        
        # 测试命令
        print("🔧 测试命令交互...")
        await manager.send_command("ping")
        await manager.send_command("get_counter", {"counter": "all"})
        await manager.send_command("reset_counter", {"counter": "all"})
        
        # 观察推送
        print("📡 观察数据推送...")
        await asyncio.sleep(15)
        
        # 最终状态
        await manager.send_command("get_counter", {"counter": "all"})
        
    finally:
        await manager.stop_sidecar()

async def test_scenario_3():
    """测试场景3: 压力测试"""
    print("\n🎯 测试场景3: 命令压力测试")
    print("=" * 60)
    
    manager = TauriSidecarManager()
    
    try:
        await manager.start_sidecar()
        await asyncio.sleep(2)
        
        # 快速发送多个命令
        print("⚡ 快速发送多个命令...")
        tasks = []
        for i in range(10):
            task = manager.send_command("get_counter", {"counter": "futu_quote"})
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r and not isinstance(r, Exception))
        print(f"📊 命令成功率: {success_count}/10")
        
        # 继续观察推送
        await asyncio.sleep(10)
        
    finally:
        await manager.stop_sidecar()

async def main():
    """主测试函数"""
    print("🧪 计数器服务完整测试套件")
    print("=" * 80)
    
    try:
        # 运行所有测试场景
        await test_scenario_1()
        await asyncio.sleep(2)
        
        await test_scenario_2()
        await asyncio.sleep(2)
        
        await test_scenario_3()
        
        print("\n🎉 所有测试场景完成!")
        print("=" * 80)
        
        # 测试总结
        print("\n📋 测试总结:")
        print("✅ 场景1: 计数器服务基本功能")
        print("✅ 场景2: Tauri Sidecar 通信")
        print("✅ 场景3: 命令压力测试")
        print("\n🎯 下一步: 集成到真实的 Tauri 应用中")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
