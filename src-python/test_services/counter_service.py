#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单计数器服务
============
用于测试 Tauri Sidecar + 标准I/O 通信方案的简单计数器服务
每秒递增计数器并推送到前端
"""

import sys
import json
import asyncio
import time
import logging
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
import signal
import traceback
from datetime import datetime

# === 诊断增强：确保在任何崩溃时都能留下调试信息 ===
# 选择一个绝对路径，确保应用有权限写入
log_dir = os.path.expanduser("~/Desktop")
log_file_path = os.path.join(log_dir, f"counter_service_debug_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# 创建调试日志文件
try:
    debug_log = open(log_file_path, "w", encoding='utf-8')
    debug_log.write(f"=== Counter Service Debug Log ===\n")
    debug_log.write(f"Start time: {datetime.now()}\n")
    debug_log.write(f"Python executable: {sys.executable}\n")
    debug_log.write(f"Python version: {sys.version}\n")
    debug_log.write(f"Current working directory: {os.getcwd()}\n")
    debug_log.write(f"Script file path: {__file__ if '__file__' in globals() else 'unknown'}\n")
    debug_log.write(f"Command line args: {sys.argv}\n")
    debug_log.write(f"Environment variables (relevant): PYTHONPATH={os.environ.get('PYTHONPATH', 'None')}\n")
    debug_log.write(f"sys.path: {sys.path}\n")
    debug_log.write(f"Current process ID: {os.getpid()}\n")
    debug_log.flush()
    
    def debug_write(message):
        """写入调试消息"""
        debug_log.write(f"[{datetime.now()}] {message}\n")
        debug_log.flush()
    
    debug_write("Debug logging initialized successfully")
    
except Exception as e:
    def debug_write(message):
        """备用调试函数 - 如果文件创建失败"""
        print(f"DEBUG: {message}", file=sys.stderr, flush=True)

# 配置日志输出到 stderr
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

@dataclass
class Message:
    """消息数据结构"""
    type: str
    source: str = "counter"
    command_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    timestamp: float = None
    success: bool = True
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class CounterService:
    """简单计数器服务"""

    def __init__(self):
        self.running = True
        self.counter = 0  # 初始值为 0
        
        # 命令处理器
        self.command_handlers = {
            'get_counter': self.handle_get_counter,
            'reset_counter': self.handle_reset_counter,
            'ping': self.handle_ping,
            'stop': self.handle_stop,
        }
        
        # 设置信号处理
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """优雅关闭处理"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def send_message(self, message: Message):
        """发送消息到 Tauri (stdout)"""
        try:
            data = {
                "type": message.type,
                "source": message.source,
                "command_id": message.command_id,
                "data": message.data,
                "timestamp": message.timestamp,
                "success": message.success,
                "error": message.error
            }
            message_json = json.dumps(data)
            
            # 检查 stdout 是否可用
            if sys.stdout.closed:
                logger.error("stdout is closed, cannot send message")
                return
                
            print(message_json, flush=True)
            sys.stdout.flush()  # 强制刷新输出
            
            logger.debug(f"Sent message: {message_json[:100]}...")  # 只记录前100个字符
            
        except BrokenPipeError as e:
            logger.error(f"Broken pipe when sending message: {e}")
            logger.error("Tauri may have disconnected, stopping service...")
            self.running = False
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            import traceback
            logger.error(f"Send message traceback: {traceback.format_exc()}")
            sys.stderr.flush()  # 确保错误日志也被刷新
    
    def send_response(self, command_id: str, success: bool, data: Any = None, error: str = None):
        """发送命令响应"""
        message = Message(
            type="response",
            command_id=command_id,
            data=data,
            success=success,
            error=error
        )
        self.send_message(message)
    
    def send_push(self, source: str, data: Any):
        """发送实时推送数据"""
        message = Message(
            type="push",
            source=source,
            data=data
        )
        self.send_message(message)
    
    async def stdin_listener(self):
        """监听来自 Tauri 的命令"""
        logger.info("Starting stdin listener...")
        sys.stderr.flush()
        
        loop = asyncio.get_event_loop()
        while self.running:
            try:
                # 检查 stdin 是否仍然可用
                if sys.stdin.closed:
                    logger.error("stdin is closed, stopping listener")
                    break
                    
                # 非阻塞读取 stdin
                line = await loop.run_in_executor(None, sys.stdin.readline)
                if not line:
                    logger.warning("stdin closed by parent process")
                    break
                    
                line_stripped = line.strip()
                if not line_stripped:
                    continue  # 忽略空行
                    
                logger.debug(f"Received command: {line_stripped[:100]}...")  # 只记录前100个字符
                
                command = json.loads(line_stripped)
                await self.handle_command(command)
                
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON received: {e}, line: {line[:100] if 'line' in locals() else 'unknown'}")
            except BrokenPipeError as e:
                logger.error(f"Broken pipe in stdin listener: {e}")
                break
            except EOFError:
                logger.warning("EOF received from stdin, parent process may have closed")
                break
            except Exception as e:
                logger.error(f"Error in stdin listener: {e}")
                import traceback
                logger.error(f"Stdin listener traceback: {traceback.format_exc()}")
                
        logger.info("Stdin listener stopped")
        self.running = False  # 确保主循环也停止
        sys.stderr.flush()
    
    async def handle_command(self, command: Dict[str, Any]):
        """处理收到的命令"""
        command_id = command.get('id', 'unknown')
        action = command.get('action', '')
        params = command.get('params', {})
        
        logger.info(f"Handling command: {action} (ID: {command_id})")
        
        if action in self.command_handlers:
            try:
                result = await self.command_handlers[action](params)
                self.send_response(command_id, True, result)
            except Exception as e:
                logger.error(f"Command {action} failed: {e}")
                self.send_response(command_id, False, error=str(e))
        else:
            self.send_response(command_id, False, error=f"Unknown action: {action}")
    
    async def handle_ping(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理心跳检查"""
        return {"status": "alive", "timestamp": time.time()}

    async def handle_get_counter(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取计数器当前值"""
        return {"counter": self.counter}

    async def handle_reset_counter(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """重置计数器"""
        self.counter = 0
        return {"message": "Counter reset", "counter": self.counter}

    async def handle_stop(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """停止服务"""
        logger.info("Received stop command, shutting down...")
        self.running = False
        return {"message": "Service stopping", "status": "stopping"}
    
    async def counter_stream(self):
        """简单计数器数据流"""
        logger.info("Starting counter stream...")
        sys.stderr.flush()

        try:
            while self.running:
                logger.info(f"Counter stream loop iteration, running={self.running}, counter={self.counter}")
                sys.stderr.flush()

                await asyncio.sleep(1)  # 每秒推送一次

                if not self.running:
                    break

                self.counter += 1

                # 推送计数器数据
                counter_data = {
                    "counter": self.counter,
                    "timestamp": time.time()
                }

                logger.info(f"Pushing counter data: {counter_data}")
                sys.stderr.flush()

                self.send_push("counter", counter_data)

        except Exception as e:
            logger.error(f"Error in counter stream: {e}")
            import traceback
            logger.error(f"Counter stream traceback: {traceback.format_exc()}")
            sys.stderr.flush()

        logger.info("Counter stream stopped")
        sys.stderr.flush()

        logger.info("Counter stream stopped")
    
    async def start(self):
        """启动所有异步任务"""
        logger.info("Starting Counter Service...")
        
        # 发送启动消息
        self.send_push("system", {"status": "started", "service": "counter"})
        
        try:
            # 启动所有任务
            results = await asyncio.gather(
                self.stdin_listener(),
                self.counter_stream(),
                return_exceptions=True
            )

            # 检查任务结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Task {i} failed with exception: {result}")

        except Exception as e:
            logger.error(f"Error in main loop: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
        finally:
            logger.info("Counter service shutdown complete")

async def main():
    """主函数"""
    try:
        debug_write("Entering main function")
        
        # 添加启动时的环境信息
        logger.info(f"=== Counter Service Starting ===")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Working directory: {os.getcwd() if 'os' in dir() else 'unknown'}")
        logger.info(f"Script path: {__file__ if '__file__' in globals() else 'unknown'}")
        logger.info(f"Command line args: {sys.argv}")
        logger.info(f"stdout isatty: {sys.stdout.isatty()}")
        logger.info(f"stdin isatty: {sys.stdin.isatty()}")
        logger.info(f"stderr isatty: {sys.stderr.isatty()}")
        
        # 确保所有环境检查完成后才开始
        sys.stderr.flush()
        debug_write("Environment logging completed, creating CounterService")
        
        service = CounterService()
        debug_write("CounterService created, calling start()")
        
        await service.start()
        debug_write("Service.start() completed normally")
        
    except KeyboardInterrupt:
        debug_write("Received keyboard interrupt")
        logger.info("Received keyboard interrupt")
    except Exception as e:
        debug_write(f"CRITICAL ERROR in main(): {e}")
        debug_write(f"TRACEBACK: {traceback.format_exc()}")
        logger.error(f"Unexpected error: {e}")
        logger.error(f"Main function traceback: {traceback.format_exc()}")
        raise  # 重新抛出异常确保进程以错误码退出
    finally:
        debug_write("Main function ending")
        logger.info("Counter service process ending")
        sys.stderr.flush()

if __name__ == "__main__":
    try:
        debug_write("Starting asyncio.run(main())")
        asyncio.run(main())
        debug_write("asyncio.run(main()) completed successfully")
    except Exception as e:
        debug_write(f"FATAL ERROR at top level: {e}")
        debug_write(f"FATAL TRACEBACK: {traceback.format_exc()}")
        print(f"FATAL: {e}", file=sys.stderr, flush=True)
        traceback.print_exc(file=sys.stderr)
        sys.stderr.flush()
        sys.exit(1)
    finally:
        debug_write("Script execution completed")
        if 'debug_log' in globals():
            debug_log.close()
