
"""
TCP客户端模块
============
这个模块实现了与交易服务器的TCP连接和通信功能。
包括：连接管理、消息接收、消息发送等功能。
使用多线程处理消息接收，支持回调函数处理接收到的数据。
"""

import threading
import time
import socket
import json
from typing import Optional, Callable, Any


class RecevieDataThread(threading.Thread):
    """
    消息接收线程类
    负责与服务器建立连接，接收服务器推送的消息，并通过回调函数处理接收到的数据
    """
    
    def __init__(self, threadID, host, port):
        """
        初始化接收数据线程
        :param threadID: 线程ID
        :param host: 服务器主机地址
        :param port: 服务器端口号
        """
        threading.Thread.__init__(self)

        self.isServerStatusRun = False    # 服务器运行状态标志
        self.threadID = threadID          # 线程ID
        self.host = host                  # 服务器主机地址
        self.port = port                  # 服务器端口号
        self.connectResoult = False       # 连接结果标志
        self.client: Optional[socket.socket] = None  # Socket客户端对象
        self.callback_func: Optional[Callable[[Any, str], None]] = None  # 回调函数，用于处理接收到的数据

    def connection(self):
        """
        建立与服务器的连接
        支持重连机制，最多尝试3次连接
        :return: 连接成功返回True，失败返回None
        """
        for _ in range(3):  # 最多尝试3次连接
            if not self.connectResoult:
                try:
                    # 创建或重新创建Socket连接
                    if self.client is None:
                        self.client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    else:
                        self.client.close()  # 关闭旧连接
                        self.client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    
                    # 连接到服务器
                    self.client.connect((self.host, self.port))
                    self.connectResoult = True
                    return True
                except Exception as ex:
                    # 连接失败，等待2秒后重试
                    time.sleep(2)
                    self.connectResoult = False
                    print("连接异常:", ex)

    def ReadMessageAsync(self):
        """
        异步读取服务器消息
        先读取4字节的消息头（消息体长度），再根据长度读取完整的消息体
        :return: 接收到的消息内容（字节数组）
        """
        contentBytesAll = None
        try:
            # 检查客户端连接是否存在
            if self.client is None:
                return None

            # 读取消息头（4字节，包含消息体长度）
            headerBytes = self.client.recv(4)
            if len(headerBytes) < 4:
                time.sleep(0.5)

            # 将字节转换为整数，获取消息体长度（小端序）
            bodylength = int.from_bytes(headerBytes, 'little')

            # 循环读取消息体，直到读取完整
            total = 0
            while total < bodylength:
                contentBytes = self.client.recv(bodylength - total)
                total += len(contentBytes)  # 累加已读取的字节数

                # 拼接消息体
                if contentBytesAll is None:
                    contentBytesAll = contentBytes
                else:
                    contentBytesAll = contentBytesAll + contentBytes

        except Exception as ex:
            # 读取消息异常，标记连接断开
            self.connectResoult = False
            print("读取消息异常:", ex)

        return contentBytesAll

    def run(self):
        """
        线程运行方法
        持续监听服务器消息，接收到消息后调用回调函数处理
        """
        print("开始线程：" + self.name)
        self.isServerStatusRun = True
        
        # 外层循环：处理连接管理
        while self.isServerStatusRun:
            # 尝试建立连接
            if not self.connection():
                time.sleep(2)
                continue
            
            # 内层循环：处理消息接收
            while self.isServerStatusRun:
                # 读取服务器消息
                contentBytes = self.ReadMessageAsync()
                
                # 将字节数组转换为UTF-8字符串
                if contentBytes is not None:
                    content = contentBytes.decode('utf-8')
                    
                    # 如果连接正常且设置了回调函数，则调用回调函数处理消息
                    if self.connectResoult:
                        if self.callback_func is not None:
                            self.callback_func('data', content)
                            continue
                    else:
                        # 连接断开，等待2秒后重新尝试连接
                        time.sleep(2)
                        break
                else:
                    # 接收失败，连接断开
                    self.connectResoult = False
                    time.sleep(2)
                    break


class ApiTcpClient:
    """
    API TCP客户端类（单例模式）
    负责管理与交易服务器的TCP连接，发送交易请求消息
    """
    instance: Optional['ApiTcpClient'] = None

    def __new__(cls, *args, **kwargs):
        """单例模式实现：确保全局只有一个实例"""
        if not cls.instance:
            cls.instance = super().__new__(cls)
        return cls.instance

    def __init__(self):
        """
        初始化TCP客户端
        创建消息接收线程，连接到指定服务器
        """
        host = '***********'    # 服务器主机地址
        port = 8080             # 服务器端口号
        
        # 创建并启动消息接收线程
        self.recevieDataThread = RecevieDataThread(1, host, port)
        self.recevieDataThread.start()
        
        # 等待2秒确保连接建立
        time.sleep(2)
        
        # 注释掉的心跳定时器代码
        # self.heartbeat_timer = threading.Timer(5.0, self.SendHeartBeat)
        # self.heartbeat_timer.start()

    def SendMessage(self, requestData):
        """
        发送消息到服务器
        将请求数据转换为JSON格式，添加消息头后发送
        :param requestData: 请求数据（字典格式）
        """
        # 注释掉的线程锁代码
        # lock = threading.Lock()
        # lock.acquire()
        
        try:
            # 将字典转换为JSON字符串
            json_str = json.dumps(requestData)
            
            # 将JSON字符串编码为UTF-8字节数组
            body_bytes = json_str.encode('utf-8')
            
            # 获取消息体长度
            body_lengh = len(body_bytes)
            
            # 将消息体长度转换为4字节的消息头（小端序）
            head_bytes = body_lengh.to_bytes(4, byteorder='little')
            
            # 拼接消息头和消息体
            messageBytes = head_bytes + body_bytes
            
            # 如果连接正常，发送消息
            if self.recevieDataThread.connectResoult and self.recevieDataThread.client is not None:
                self.recevieDataThread.client.send(messageBytes)
            else:
                print("连接未建立，无法发送消息")
                
        except Exception as ex:
            print("发送消息异常:", ex)
            
        # 注释掉的线程锁释放代码
        # lock.release()
