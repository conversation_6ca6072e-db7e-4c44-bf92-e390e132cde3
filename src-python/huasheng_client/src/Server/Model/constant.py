"""
常量定义模块
============
这个模块定义了交易API中使用的各种常量。
包括：API请求类型、买卖方向、订单类型等枚举值。
"""

# 导入枚举类型
from enum import Enum, IntEnum


class ApiRequestType(IntEnum):
    """
    API请求类型枚举
    定义了所有可用的API请求类型及其对应的数值
    """
    Login = 100          # 登录请求
    Push = 0             # 推送消息（服务器主动推送订单状态等信息）
    QueryFunds = 2       # 查询资金信息
    QueryPosition = 3    # 查询持仓信息
    QueryEntrust = 5     # 查询委托记录
    PlaceOrder = 7       # 下单请求
    UnOrder = 8          # 撤单请求
    ProL2Popup = 9       # ProL2弹窗请求


class ApiPushType(IntEnum):
    """
    API推送类型枚举（仅用于主动推送的ResponseType识别）
    只定义真正的主动推送（RequestType=0或特殊主动推送）需要的ResponseType值

    使用说明：
    - 主动推送：RequestType=0时，使用ResponseType判断具体推送类型
    - 响应推送：RequestType!=0时，直接使用RequestType判断，不需要此枚举
    """
    OrderPush = 101      # 订单状态主动推送（RequestType=0, ResponseType=101）
    StatisticData = 10001 # 统计数据主动推送（RequestType=10001, ResponseType=10001）


class Side(IntEnum):
    """
    买卖方向枚举
    定义了交易的买卖方向
    """
    Buy = 1              # 买入
    Sell = 2             # 卖出


class OrderType(IntEnum):
    """
    订单类型枚举
    定义了不同的订单类型，适用于不同的交易策略
    """
    BiddingAndLimited = 0    # 竞价限价单：在开盘竞价时段使用竞价，其他时段使用限价
    Bidding = 1              # 竞价单：只在开盘竞价时段有效
    EnhanceLimited = 2       # 增强限价单：可以立即成交，剩余部分转为限价单
    Limited = 3              # 限价单：以指定价格或更好价格成交
    SpecialLimited = 4       # 特别限价单：只能以指定价格成交
    Market = 5               # 市价单：以市场最优价格成交
