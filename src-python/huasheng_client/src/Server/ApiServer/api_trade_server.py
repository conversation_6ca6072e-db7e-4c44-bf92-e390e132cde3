"""
交易API服务器类
===============
这是一个交易API服务器类，负责处理各种交易相关的API请求。
使用单例模式确保全局只有一个实例。
主要功能包括：用户登录、查询资金、查询持仓、查询委托记录、下单、撤单等。
"""

# 导入TCP客户端类
from Server.ServerImp.api_tcp_client import ApiTcpClient
# 导入常量定义：请求类型、推送类型、买卖方向、订单类型
from Server.Model.constant import ApiRequestType, ApiPushType, Side, OrderType
# 导入JSON处理模块
import json
from typing import Optional


def format_json_output(data_str: str, title: str = "") -> str:
    """
    格式化JSON输出，使其更易读
    """
    try:
        # 解析JSON数据
        data = json.loads(data_str)

        # 格式化输出
        formatted = json.dumps(data, indent=2, ensure_ascii=False)

        # 添加标题和分隔线
        if title:
            separator = "=" * 60
            return f"\n{separator}\n{title}\n{separator}\n{formatted}\n{separator}"
        else:
            return formatted
    except json.JSONDecodeError:
        # 如果不是有效的JSON，直接返回原始字符串
        return data_str


class ApiTradeServer:
    """交易API服务器类（单例模式）"""
    instance: Optional['ApiTradeServer'] = None

    def __new__(cls, *args, **kwargs):
        """单例模式实现：确保全局只有一个实例"""
        if not cls.instance:
            cls.instance = super().__new__(cls)
        return cls.instance

    def __init__(self):
        """初始化交易服务器"""
        # 创建TCP客户端实例
        self.apiTcpClient = ApiTcpClient()  # 直接使用单例实例
        self.requestId = 1              # 请求ID，用于标识不同的请求
        self.haveLogined = False        # 登录状态标志
        self.token: Optional[str] = None  # 登录token，初始为None
        # 设置回调函数，处理服务器响应
        self.apiTcpClient.recevieDataThread.callback_func = self.Callback_func

    def Login(self, account, password):
        """
        用户登录
        :param account: 交易账号
        :param password: 交易密码
        """
        self.requestId += 1
        # 构造登录请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.Login,
            "Account": account,
            "Password": password
        }
        # 发送登录请求
        self.apiTcpClient.SendMessage(data)

    def QueryFunds(self):
        """查询账户资金信息"""
        self.requestId += 1
        # 构造查询资金请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.QueryFunds,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送查询资金请求
        self.apiTcpClient.SendMessage(data)

    def QueryPositon(self):
        """查询持仓信息"""
        self.requestId += 1
        # 构造查询持仓请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.QueryPosition,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送查询持仓请求
        self.apiTcpClient.SendMessage(data)

    def QueryEntrust(self):
        """查询委托记录（所有委托）"""
        self.requestId += 1
        # 构造查询委托请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.QueryEntrust,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送查询委托请求
        self.apiTcpClient.SendMessage(data)

    def QueryEntrustByPageSize(self, pageNo, pageSize):
        """
        分页查询委托记录
        :param pageNo: 页码（从1开始）
        :param pageSize: 每页显示数量
        """
        self.requestId += 1
        # 构造分页查询委托请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.QueryEntrust,
            "PageNo": pageNo,
            "PageSize": pageSize,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送分页查询委托请求
        self.apiTcpClient.SendMessage(data)

    def PlaceOrder(self, stockCode, price, qty, side, orderType):
        """
        下单操作
        :param stockCode: 股票代码
        :param price: 委托价格
        :param qty: 委托数量
        :param side: 买卖方向（Side.Buy或Side.Sell）
        :param orderType: 订单类型（OrderType枚举值）
        """
        self.requestId += 1
        # 构造下单请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.PlaceOrder,
            "StockCode": stockCode,
            "Price": price,
            "Qty": qty,
            "Side": side,
            "OrderType": orderType,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送下单请求
        self.apiTcpClient.SendMessage(data)

    def UnOrder(self, entrustNo):
        """
        撤单操作
        :param entrustNo: 委托编号
        """
        self.requestId += 1
        # 构造撤单请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.UnOrder,
            "EntrustNo": entrustNo,
            "Token": self.token  # 需要登录后获取的token
        }
        # 发送撤单请求
        self.apiTcpClient.SendMessage(data)

    def ProL2Popup(self, stockCode, showPriceAndQty=True, price=None, qty=None, openAutoSell=False, autoSellPrice=None):
        """
        ProL2弹窗操作
        :param stockCode: 股票代码
        :param showPriceAndQty: 是否显示下单框价格、数量
        :param price: 下单框价格
        :param qty: 下单框数量
        :param openAutoSell: 是否开启自动卖出
        :param autoSellPrice: 自动卖出框价格
        """
        self.requestId += 1
        # 构造ProL2弹窗请求数据
        data = {
            "RequestId": self.requestId,
            "RequestType": ApiRequestType.ProL2Popup,
            "StockCode": stockCode,
            "ShowPriceAndQty": showPriceAndQty,
            "Token": self.token  # 需要登录后获取的token
        }
        
        # 可选参数：只有提供了才添加到请求中
        if price is not None:
            data["Price"] = price
        if qty is not None:
            data["Qty"] = qty
        if openAutoSell:
            data["OpenAutoSell"] = openAutoSell
        if autoSellPrice is not None:
            data["AutoSellPrice"] = autoSellPrice
            
        # 发送ProL2弹窗请求
        self.apiTcpClient.SendMessage(data)

    def Callback_func(self, a, responseData):
        """
        处理服务器响应的回调函数
        :param a: 占位参数
        :param responseData: 服务器响应的JSON数据
        """
        try:
            # 解析服务器返回的JSON数据
            data = json.loads(responseData)
            # 获取请求类型和响应码
            requestType = data['RequestType']
            responseCode = data['ResponseCode']
            
            # 响应码为0表示请求成功
            if responseCode == 0:
                if requestType == ApiRequestType.Push:
                    # 服务器主动推送消息（RequestType = 0）
                    self.handle_active_push(data, responseData)
                elif requestType == ApiPushType.StatisticData:
                    # 统计数据主动推送（特殊情况：RequestType = ResponseType = 10001）
                    self.handle_active_push(data, responseData)
                else:
                    # 响应式推送消息（用户发起请求后的响应）
                    self.handle_response_push(data, responseData, requestType)
            else:
                # 响应码不为0，表示请求失败
                print(format_json_output(responseData, "❌ 请求失败"))

        except Exception as ex:
            # 处理JSON解析异常
            print("处理响应数据异常:", ex)

    def handle_active_push(self, data, responseData):
        """
        处理服务器主动推送消息（RequestType = 0）
        这些是服务器在没有用户请求的情况下主动发送的消息
        """
        requestType = data.get('RequestType', 0)
        responseType = data.get('ResponseType', 0)

        if responseType == ApiPushType.OrderPush:
            # 订单状态主动推送（ResponseType=101的主动推送）
            print(format_json_output(responseData, f"📢 订单状态主动推送 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif responseType == ApiPushType.StatisticData:
            # 统计数据主动推送
            print(format_json_output(responseData, f"📊 统计数据主动推送 (RequestType: {requestType}, ResponseType: {responseType})"))



        else:
            # 其他未知的主动推送
            print(format_json_output(responseData, f"🔔 未知主动推送 (RequestType: {requestType}, ResponseType: {responseType})"))

    def handle_response_push(self, data, responseData, requestType):
        """
        处理响应式推送消息（RequestType != 0）
        这些是用户发起请求后，服务器的响应推送
        """
        responseType = data.get('ResponseType', requestType)

        if requestType == ApiRequestType.Login:
            # 登录响应
            self.haveLogined = True
            self.token = data['Token']  # 保存登录token
            print(format_json_output(responseData, f"🔐 登录响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.QueryFunds:
            # 资金查询响应
            print(format_json_output(responseData, f"💰 资金查询响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.QueryPosition:
            # 持仓查询响应
            print(format_json_output(responseData, f"📊 持仓查询响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.QueryEntrust:
            # 委托记录查询响应
            print(format_json_output(responseData, f"📋 委托记录查询响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.PlaceOrder:
            # 下单响应
            print(format_json_output(responseData, f"📝 下单响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.UnOrder:
            # 撤单响应
            print(format_json_output(responseData, f"❌ 撤单响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        elif requestType == ApiRequestType.ProL2Popup:
            # ProL2弹窗响应
            print(format_json_output(responseData, f"🔔 ProL2弹窗响应 (RequestType: {requestType}, ResponseType: {responseType})"))

        else:
            # 其他未知的响应类型
            print(format_json_output(responseData, f"🔔 未知响应类型 (RequestType: {requestType}, ResponseType: {responseType})"))