"""
交易API演示程序
==============
这是一个用于演示交易API功能的客户端程序。
主要功能包括：登录、查询资金、查询持仓、查询委托记录、下单、撤单等。
"""

# 添加src目录到系统路径
import sys
import os
# 获取src目录的路径：tests -> huasheng_client -> src
current_dir = os.path.dirname(os.path.abspath(__file__))  # tests目录
huasheng_client_dir = os.path.dirname(current_dir)       # huasheng_client目录
src_dir = os.path.join(huasheng_client_dir, 'src')       # src目录
sys.path.insert(0, src_dir)

# 导入交易服务器类
from Server.ApiServer.api_trade_server import ApiTradeServer  # type: ignore
# 导入常量定义：买卖方向和订单类型  
from Server.Model.constant import Side, OrderType  # type: ignore
# 导入时间模块用于延时
import time

# 程序主入口
if __name__ == '__main__':
    # 创建交易服务器实例（单例模式）
    apiTradeServer = ApiTradeServer()
    
    # 主要功能演示循环
    while True:
        # 打印可用接口菜单
        print("###### 接口名: Login              接口描述: 登陆                                         ######")
        print("###### 接口名: QueryFunds         接口描述: 查询资金                                    ######")
        print("###### 接口名: QueryPositon              接口描述: 查询持仓                                         ######")
        print("###### 接口名: QueryEntrust         接口描述: 查询委托记录                                    ######")
        print("###### 接口名: QueryEntrustByPageSize              接口描述: 查询委托记录 分页                                         ######")
        print("###### 接口名: PlaceOrder         接口描述: 下单 (格式: PlaceOrder [side] [code] [price] [qty])  ######")
        print("###### 接口名: UnOrder         接口描述: 撤单                                    ######")
        print("###### 接口名: ProL2Popup         接口描述: ProL2弹窗                                    ######")
        
        # 获取用户输入的接口名和参数
        user_input = input("请输入需要查看的接口名: ")
        input_parts = user_input.strip().split()
        method_name = input_parts[0] if input_parts else ""

        if method_name == "Login":
            # 用户登录功能（使用模拟盘账号）
            account = '***********'      # 模拟盘交易账号
            password = '456789'          # 模拟盘交易密码
            print(f"正在使用模拟盘账号登录: {account}")
            print("使用交易密码进行登录...")
            apiTradeServer.Login(account, password)
            
        elif method_name == "QueryFunds":
            # 查询账户资金信息
            apiTradeServer.QueryFunds()
            
        elif method_name == "QueryPositon":
            # 查询持仓信息
            apiTradeServer.QueryPositon()
            
        elif method_name == "QueryEntrust":
            # 查询委托记录（所有委托）
            apiTradeServer.QueryEntrust()
            
        elif method_name == "QueryEntrustByPageSize":
            # 查询委托记录（分页查询）
            pageNo = 1          # 页码，从1开始
            pageSize = 20       # 每页显示数量
            apiTradeServer.QueryEntrustByPageSize(pageNo, pageSize)

        elif method_name == "PlaceOrder":
            # 下单操作
            # 格式: PlaceOrder [side] [stockCode] [price] [qty]
            # 示例: PlaceOrder 1 08406 0.12 10000
            # 参数说明: side(1=买入,2=卖出), stockCode(股票代码), price(委托价格), qty(委托数量)

            # 默认参数
            default_side = Side.Buy                       # 默认买卖方向：买入
            default_stockCode = '08406'                   # 默认股票代码
            default_price = 0.12                         # 默认委托价格
            default_qty = 10000                          # 默认委托数量
            default_orderType = OrderType.EnhanceLimited # 默认订单类型：增强限价单

            # 解析用户输入的参数
            try:
                # 买卖方向 (1=买入, 2=卖出)
                if len(input_parts) > 1:
                    side_input = int(input_parts[1])
                    side = Side.Buy if side_input == 1 else Side.Sell
                    print(f"使用输入的买卖方向: {'买入' if side == Side.Buy else '卖出'}")
                else:
                    side = default_side
                    print(f"使用默认买卖方向: {'买入' if side == Side.Buy else '卖出'}")

                # 股票代码
                if len(input_parts) > 2:
                    stockCode = input_parts[2]
                    print(f"使用输入的股票代码: {stockCode}")
                else:
                    stockCode = default_stockCode
                    print(f"使用默认股票代码: {stockCode}")

                # 委托价格
                if len(input_parts) > 3:
                    price = float(input_parts[3])
                    print(f"使用输入的委托价格: {price}")
                else:
                    price = default_price
                    print(f"使用默认委托价格: {price}")

                # 委托数量
                if len(input_parts) > 4:
                    qty = int(input_parts[4])
                    print(f"使用输入的委托数量: {qty}")
                else:
                    qty = default_qty
                    print(f"使用默认委托数量: {qty}")

                # 订单类型（固定使用增强限价单）
                orderType = default_orderType
                print(f"使用订单类型: 增强限价单")

                print(f"\n=== 下单参数确认 ===")
                print(f"买卖方向: {'买入' if side == Side.Buy else '卖出'}")
                print(f"股票代码: {stockCode}")
                print(f"委托价格: {price}")
                print(f"委托数量: {qty}")
                print(f"订单类型: 增强限价单")
                print(f"==================\n")

                # 执行下单
                apiTradeServer.PlaceOrder(stockCode, price, qty, side, orderType)

            except (ValueError, IndexError) as e:
                print(f"❌ 参数解析错误: {e}")
                print("📋 使用格式: PlaceOrder [side] [stockCode] [price] [qty]")
                print("📝 示例: PlaceOrder 1 08406 0.12 10000")
                print("📖 参数说明:")
                print("  side: 1=买入, 2=卖出")
                print("  stockCode: 股票代码")
                print("  price: 委托价格")
                print("  qty: 委托数量")
                print("⚠️  请检查参数格式后重新输入，未执行下单操作。")

        elif method_name == "UnOrder":
            # 撤单操作
            entrustNo = 2260862    # 委托编号（使用实际查询到的订单号）
            apiTradeServer.UnOrder(entrustNo)

        elif method_name == "ProL2Popup":
            # ProL2弹窗操作
            stockCode = '00700'                       # 股票代码（示例：腾讯控股）
            showPriceAndQty = True                    # 是否显示下单框价格、数量
            price = 488.6                             # 下单框价格
            qty = 100                                 # 下单框数量
            openAutoSell = False                       # 是否开启自动卖出
            autoSellPrice = 489.8                     # 自动卖出框价格
            apiTradeServer.ProL2Popup(stockCode, showPriceAndQty, price, qty, openAutoSell, autoSellPrice)
            
        # 短暂等待确保响应能够显示，避免菜单立即覆盖响应信息
        time.sleep(0.1)