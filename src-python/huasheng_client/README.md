# 华泰证券 Pro API 交易演示程序

## 项目概述

这是一个华泰证券交易 API 的 Python 演示程序，提供了完整的交易功能接口，包括登录、查询资金、查询持仓、查询委托记录、下单、撤单等功能。

## 项目结构

```
pro_api_demo_python/
├── docs/                          # 文档目录
│   └── ProApi接口文档V1.2.2.md # API接口文档
├── src/                           # 项目源代码
│   ├── Demo/                      # 演示程序目录
│   │   └── api_trade_demo.py     # 主演示程序
│   └── Server/                    # 服务端模块
│       ├── ApiServer/             # API服务器
│       │   └── api_trade_server.py # 交易API服务器类
│       ├── Model/                 # 数据模型
│       │   └── constant.py       # 常量定义
│       └── ServerImp/             # 服务实现
│           └── api_tcp_client.py # TCP客户端实现
├── run_demo.py                    # 项目启动脚本
├── setup.bat                      # Windows环境设置脚本
├── setup.sh                       # macOS/Linux环境设置脚本
├── requirements.txt               # 依赖包列表
├── .gitignore                     # Git忽略文件
└── README.md                     # 项目说明文档
```

## 环境要求

-   Python 3.6 或更高版本
-   macOS / Windows / Linux

## 安装和配置

### 1. 克隆或下载项目

```bash
# 如果从git克隆
git clone <项目地址>
cd pro_api_demo_python

# 或直接解压缩项目文件
```

### 2. 安装依赖

```bash
# 本项目无外部依赖，可选择安装扩展功能依赖
uv sync
```

### 3. 配置服务器信息

项目已配置服务器信息：

-   服务器地址：`10.211.55.4`
-   端口：`8080`

如需修改，请编辑 `src/Server/ServerImp/api_tcp_client.py` 文件第 163-164 行。

## 使用方法

### 启动演示程序

```bash
# 方法1：使用项目启动脚本（推荐）
python run_demo.py

# 方法2：使用环境设置脚本
# Windows:
setup.bat
# macOS/Linux:
./setup.sh

# 方法3：直接运行演示程序
python src/Demo/api_trade_demo.py
```

### 主要功能

程序启动后会显示菜单，支持以下操作：

1. **Login** - 用户登录

    - 账户：15602502292
    - 密码：cjw123456
    - 交易密码：456789

2. **QueryFunds** - 查询资金信息

3. **QueryPositon** - 查询持仓信息

4. **QueryEntrust** - 查询委托记录

5. **QueryEntrustByPageSize** - 分页查询委托记录

6. **PlaceOrder** - 下单操作

7. **UnOrder** - 撤单操作

8. **ProL2Popup** - ProL2 弹窗操作

### 示例操作流程

1. 运行程序后，首先输入 `Login` 进行登录
2. 登录成功后，可以使用其他功能查询资金、持仓等信息
3. 使用 `PlaceOrder` 进行下单操作（注意：这是真实交易，请谨慎操作）
4. 使用 `UnOrder` 撤销未成交的订单

## API 接口说明

### 主要类和方法

#### ApiTradeServer 类

-   `Login(account, password)` - 用户登录
-   `QueryFunds()` - 查询资金
-   `QueryPositon()` - 查询持仓
-   `QueryEntrust()` - 查询委托
-   `QueryEntrustByPageSize(pageNo, pageSize)` - 分页查询委托
-   `PlaceOrder(stockCode, price, qty, side, orderType)` - 下单
-   `UnOrder(entrustNo)` - 撤单
-   `ProL2Popup(stockCode, showPriceAndQty, price, qty, openAutoSell, autoSellPrice)` - ProL2 弹窗

#### 常量定义

-   `Side.Buy` / `Side.Sell` - 买入/卖出方向
-   `OrderType.EnhanceLimited` - 增强限价单
-   `OrderType.Limited` - 限价单
-   `OrderType.Market` - 市价单

## 注意事项

⚠️ **重要提醒**：

1. 这是真实的交易 API，所有操作都会影响实际账户
2. 下单前请仔细确认股票代码、价格、数量等信息
3. 建议先在测试环境中熟悉操作流程
4. 请保护好账户信息，不要在公开场合展示账号密码
5. 程序运行需要网络连接到交易服务器

## 故障排除

### 常见问题

1. **模块导入错误**

    ```
    ModuleNotFoundError: No module named 'Server'
    ```

    解决方案：确保使用 `PYTHONPATH=. python3 Demo/api_trade_demo.py` 运行

2. **连接服务器失败**

    ```
    连接异常: [Errno 61] Connection refused
    ```

    解决方案：检查服务器地址和端口是否正确，确保网络连接正常

3. **Python 版本问题**
   确保使用 Python 3.6 或更高版本，在 macOS 上通常使用 `python3` 命令

## 更新日志

### 2024 年修复版本

-   修复了模块导入问题，添加了必要的 `__init__.py` 文件
-   修复了 `self.token` 未初始化的错误
-   改进了 TCP 客户端的异常处理机制
-   更新了服务器配置信息
-   添加了完整的项目文档

## 支持

如有问题请联系技术支持或查阅华泰证券官方 API 文档。
