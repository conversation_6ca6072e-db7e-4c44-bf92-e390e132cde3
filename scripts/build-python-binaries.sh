#!/bin/bash

# Python 服务构建脚本
# 为 Tauri Sidecar 生产模式创建可执行的 Python 脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
BIN_DIR="$PROJECT_ROOT/src-tauri/bin"

# 获取目标平台信息
ARCH=$(uname -m)
OS=$(uname -s | tr '[:upper:]' '[:lower:]')

# 转换架构名称以匹配 Rust 目标
case "$ARCH" in
    x86_64) TARGET_ARCH="x86_64" ;;
    arm64|aarch64) TARGET_ARCH="aarch64" ;;
    *) TARGET_ARCH="$ARCH" ;;
esac

# 转换操作系统名称
case "$OS" in
    darwin) TARGET_OS="apple-darwin" ;;
    linux) TARGET_OS="unknown-linux-gnu" ;;
    *) TARGET_OS="$OS" ;;
esac

TARGET_SUFFIX="$TARGET_ARCH-$TARGET_OS"

echo "🚀 Building Python services for Tauri Sidecar..."
echo "📂 Project root: $PROJECT_ROOT"
echo "📦 Output directory: $BIN_DIR"
echo "🎯 Target platform: $TARGET_SUFFIX"

# 创建输出目录
mkdir -p "$BIN_DIR"

# 服务映射
SERVICES=(
    "src-python/test_services/counter_service.py:counter-service"
    "src-python/adapters/futu_adapter_enhanced.py:futu-adapter"
    "src-python/adapters/huasheng_adapter_enhanced.py:huasheng-adapter"
)

success_count=0
total_count=${#SERVICES[@]}

# 构建每个服务
for service in "${SERVICES[@]}"; do
    IFS=':' read -r script_path binary_name <<< "$service"
    
    # Tauri 会自动添加平台后缀，所以我们创建两个版本：
    # 1. 不带后缀的版本（用于 Tauri sidecar）
    # 2. 带后缀的版本（用于向后兼容）
    
    binary_name_simple="$binary_name"
    binary_name_with_suffix="$binary_name-$TARGET_SUFFIX"
    
    echo "🔨 Building $binary_name_simple and $binary_name_with_suffix from $script_path..."
    
    script_full_path="$PROJECT_ROOT/$script_path"
    binary_simple_path="$BIN_DIR/$binary_name_simple"
    binary_suffix_path="$BIN_DIR/$binary_name_with_suffix"
    
    if [ ! -f "$script_full_path" ]; then
        echo "⚠️ Script not found: $script_full_path"
        continue
    fi
    
    # 创建简单版本的包装脚本（给 Tauri sidecar 使用）
    cat > "$binary_simple_path" << 'EOF'
#!/bin/bash
# Auto-generated wrapper for Python service (Tauri sidecar)

# 获取脚本所在目录（应用的 MacOS 目录）
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 检查是否在 macOS 应用包环境中
if [[ "$SCRIPT_DIR" == */Contents/MacOS ]]; then
    # 脚本在 Contents/MacOS/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$CONTENTS_DIR/Resources/_up_"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🍎 macOS 应用包环境 (MacOS)" >&2
    echo "📦 Contents目录: $CONTENTS_DIR" >&2
elif [[ "$SCRIPT_DIR" == */Contents/Resources ]]; then
    # 脚本在 Contents/Resources/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$SCRIPT_DIR/_up_"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🍎 macOS 应用包环境 (Resources)" >&2
    echo "📦 Contents目录: $CONTENTS_DIR" >&2
else
    # 开发环境或其他环境
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🔧 开发环境" >&2
fi

echo "🐍 启动 Python 服务"
echo "📂 项目根目录: $PROJECT_ROOT"
echo "📄 Python 脚本: $PYTHON_SCRIPT"

# 检查脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "❌ Python 脚本未找到: $PYTHON_SCRIPT"
    echo "🔍 尝试搜索可用的 Python 文件:"
    find "$PROJECT_ROOT" -name "*.py" -type f 2>/dev/null || echo "未找到任何 Python 文件"
    exit 1
fi

# 切换到项目根目录并运行 Python 脚本
cd "$PROJECT_ROOT"

# 检查 uv 命令是否可用，使用绝对路径作为备选
UV_CMD="uv"
if ! command -v uv >/dev/null 2>&1; then
    # 尝试常见的 uv 安装路径
    if [ -x "/Users/<USER>/.local/bin/uv" ]; then
        UV_CMD="/Users/<USER>/.local/bin/uv"
    elif [ -x "$HOME/.local/bin/uv" ]; then
        UV_CMD="$HOME/.local/bin/uv"
    else
        echo "❌ uv 命令未找到，请安装 uv 或检查 PATH"
        exit 1
    fi
fi

echo "✅ 启动 Python 服务: $UV_CMD run python '$PYTHON_SCRIPT'"
exec "$UV_CMD" run python "$PYTHON_SCRIPT" "$@"
EOF

    # 替换占位符
    sed -i '' "s|PYTHON_SCRIPT_PATH|$script_path|g" "$binary_simple_path"
    
    # 创建带后缀版本的包装脚本（Tauri 实际使用的版本）
    cat > "$binary_suffix_path" << 'EOF'
#!/bin/bash
# Auto-generated wrapper for Python service (Tauri platform-specific binary)

# 获取脚本所在目录（应用的 MacOS 目录）
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 检查是否在 macOS 应用包环境中
if [[ "$SCRIPT_DIR" == */Contents/MacOS ]]; then
    # 脚本在 Contents/MacOS/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$CONTENTS_DIR/Resources/_up_"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🍎 macOS 应用包环境 (MacOS)" >&2
    echo "📦 Contents目录: $CONTENTS_DIR" >&2
elif [[ "$SCRIPT_DIR" == */Contents/Resources ]]; then
    # 脚本在 Contents/Resources/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$SCRIPT_DIR/_up_"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🍎 macOS 应用包环境 (Resources)" >&2
    echo "📦 Contents目录: $CONTENTS_DIR" >&2
else
    # 开发环境或其他环境
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    PYTHON_SCRIPT="$PROJECT_ROOT/PYTHON_SCRIPT_PATH"
    
    echo "🔧 开发环境" >&2
fi

echo "🐍 启动 Python 服务"
echo "📂 项目根目录: $PROJECT_ROOT"
echo "📄 Python 脚本: $PYTHON_SCRIPT"

# 检查脚本是否存在
if [ ! -f "$PYTHON_SCRIPT" ]; then
    echo "❌ Python 脚本未找到: $PYTHON_SCRIPT"
    echo "🔍 尝试搜索可用的 Python 文件:"
    find "$PROJECT_ROOT" -name "*.py" -type f 2>/dev/null || echo "未找到任何 Python 文件"
    exit 1
fi

# 切换到项目根目录并运行 Python 脚本
cd "$PROJECT_ROOT"

# 检查 uv 命令是否可用，使用绝对路径作为备选
UV_CMD="uv"
if ! command -v uv >/dev/null 2>&1; then
    # 尝试常见的 uv 安装路径
    if [ -x "/Users/<USER>/.local/bin/uv" ]; then
        UV_CMD="/Users/<USER>/.local/bin/uv"
    elif [ -x "$HOME/.local/bin/uv" ]; then
        UV_CMD="$HOME/.local/bin/uv"
    else
        echo "❌ uv 命令未找到，请安装 uv 或检查 PATH"
        exit 1
    fi
fi

echo "✅ 启动 Python 服务: $UV_CMD run python '$PYTHON_SCRIPT'"
exec "$UV_CMD" run python "$PYTHON_SCRIPT" "$@"
EOF
    
    # 替换占位符
    sed -i '' "s|PYTHON_SCRIPT_PATH|$script_path|g" "$binary_suffix_path"
    
    # 设置执行权限
    chmod +x "$binary_simple_path"
    chmod +x "$binary_suffix_path"
    
    echo "✅ $binary_name_simple and $binary_name_with_suffix created successfully"
    ((success_count++))
done

echo
echo "🎉 Build completed: $success_count/$total_count services built successfully"

if [ $success_count -lt $total_count ]; then
    echo "⚠️ Some services failed to build."
    exit 1
fi

# 列出生成的二进制文件
echo
echo "📋 Generated binaries in $BIN_DIR:"
if [ -d "$BIN_DIR" ]; then
    ls -la "$BIN_DIR"
fi