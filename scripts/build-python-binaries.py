#!/usr/bin/env python3
"""
Python 服务二进制构建脚本

此脚本用于将 Python 服务打包成独立的二进制文件，供 Tauri Sidecar 生产模式使用。
使用 PyInstaller 来打包 Python 脚本。
"""

import os
import subprocess
import sys
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BIN_DIR = PROJECT_ROOT / "src-tauri" / "bin"

# 服务映射：脚本路径 -> 二进制名称
SERVICES = {
    "src-python/test_services/counter_service.py": "counter-service",
    "src-python/adapters/futu_adapter.py": "futu-adapter", 
    "src-python/adapters/huasheng_adapter.py": "huasheng-adapter",
}

def check_pyinstaller():
    """检查 PyInstaller 是否安装"""
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_pyinstaller():
    """安装 PyInstaller"""
    print("📦 Installing PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyInstaller: {e}")
        return False

def build_service(script_path: str, binary_name: str):
    """构建单个服务"""
    print(f"🔨 Building {binary_name} from {script_path}...")
    
    script_full_path = PROJECT_ROOT / script_path
    if not script_full_path.exists():
        print(f"⚠️ Script not found: {script_full_path}")
        return False
    
    # 确保输出目录存在
    BIN_DIR.mkdir(parents=True, exist_ok=True)
    
    # PyInstaller 命令
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--name", binary_name,          # 输出文件名
        "--distpath", str(BIN_DIR),     # 输出目录
        "--workpath", str(PROJECT_ROOT / "build" / binary_name),  # 临时工作目录
        "--specpath", str(PROJECT_ROOT / "build"),                # spec 文件目录
        "--clean",                      # 清理临时文件
        "--noconfirm",                 # 不确认覆盖
        str(script_full_path)          # 脚本路径
    ]
    
    try:
        # 在项目根目录执行命令
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, check=True, capture_output=True, text=True)
        print(f"✅ {binary_name} built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build {binary_name}: {e}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False

def main():
    """主函数"""
    print("🚀 Building Python services for Tauri Sidecar...")
    print(f"📂 Project root: {PROJECT_ROOT}")
    print(f"📦 Output directory: {BIN_DIR}")
    
    # 检查 PyInstaller
    if not check_pyinstaller():
        print("PyInstaller not found, installing...")
        if not install_pyinstaller():
            sys.exit(1)
    
    # 构建所有服务
    success_count = 0
    total_count = len(SERVICES)
    
    for script_path, binary_name in SERVICES.items():
        if build_service(script_path, binary_name):
            success_count += 1
    
    print(f"\n🎉 Build completed: {success_count}/{total_count} services built successfully")
    
    if success_count < total_count:
        print("⚠️ Some services failed to build. Check the logs above.")
        sys.exit(1)
    
    # 列出生成的二进制文件
    print(f"\n📋 Generated binaries in {BIN_DIR}:")
    if BIN_DIR.exists():
        for binary in sorted(BIN_DIR.glob("*")):
            if binary.is_file():
                size = binary.stat().st_size / (1024 * 1024)  # MB
                print(f"  📄 {binary.name} ({size:.1f} MB)")

if __name__ == "__main__":
    main()