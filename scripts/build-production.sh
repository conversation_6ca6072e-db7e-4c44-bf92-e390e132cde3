#!/bin/bash

# 生产环境完整构建脚本
# 自动完成所有构建步骤并打包应用

set -e

echo "🚀 开始生产环境构建..."

PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "📂 项目目录: $PROJECT_ROOT"

# 步骤 1: 构建 Python 服务包装器
echo
echo "1️⃣ 构建 Python 服务包装器..."
echo "🧹 清理旧的包装器..."
rm -f src-tauri/bin/counter-service*
rm -f src-tauri/bin/futu-adapter*
rm -f src-tauri/bin/huasheng-adapter*

echo "🔨 重新生成包装器..."
./scripts/build-python-binaries.sh

echo "✅ 验证生成的包装器..."
if [ -f "src-tauri/bin/counter-service" ]; then
    echo "📄 检查 counter-service 内容:"
    head -5 "src-tauri/bin/counter-service"
else
    echo "❌ counter-service 未生成"
    exit 1
fi

# 步骤 2: 安装前端依赖（如果需要）
echo
echo "2️⃣ 检查前端依赖..."
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    yarn install
else
    echo "✅ 前端依赖已存在"
fi

# 步骤 3: 构建前端资源
echo
echo "3️⃣ 构建前端资源..."
yarn build

# 步骤 4: 验证构建产物
echo
echo "4️⃣ 验证构建产物..."
if [ ! -d "dist" ]; then
    echo "❌ 前端构建失败，dist 目录不存在"
    exit 1
fi

echo "✅ 前端构建完成"

# 步骤 5: 构建 Tauri 应用
echo
echo "5️⃣ 构建 Tauri 应用..."
cd src-tauri

# 清理之前的构建
echo "🧹 清理之前的构建产物..."
cargo clean

# 构建 Release 版本
echo "🔨 构建 Release 版本..."
cargo build --release

echo "✅ Rust 后端构建完成"

# 步骤 6: 打包应用
echo
echo "6️⃣ 打包应用..."
cd "$PROJECT_ROOT"

# 在打包前再次验证包装脚本
echo "🔍 最终验证包装脚本..."
for script in counter-service futu-adapter huasheng-adapter; do
    if [ -f "src-tauri/bin/$script" ]; then
        echo "✅ $script 存在"
        # 检查脚本内容是否包含正确的逻辑
        if grep -q "macOS 应用包环境" "src-tauri/bin/$script"; then
            echo "  ✅ 包含 macOS 应用包环境检测"
        else
            echo "  ❌ 缺少 macOS 应用包环境检测"
            echo "  🔧 内容预览:"
            head -10 "src-tauri/bin/$script" | sed 's/^/    /'
        fi
    else
        echo "❌ $script 不存在"
        exit 1
    fi
done

echo "🚀 开始 Tauri 构建..."
yarn tauri build

# 步骤 6.5: 自动修复打包后的脚本（关键步骤）
echo
echo "6.5️⃣ 自动修复打包后的脚本..."

APP_PATH="src-tauri/target/release/bundle/macos/XX交易终端.app"
SCRIPTS=("counter-service" "futu-adapter" "huasheng-adapter")
COPY_SUCCESS=true

# 检查应用包是否存在
if [ ! -d "$APP_PATH" ]; then
    echo "❌ 构建失败：未找到应用包 $APP_PATH"
    echo "🔍 检查 Tauri 构建是否成功完成"
    exit 1
fi

echo "📱 找到应用包: $APP_PATH"

# 检查 MacOS 目录
MACOS_DIR="$APP_PATH/Contents/MacOS"
if [ ! -d "$MACOS_DIR" ]; then
    echo "❌ 应用包结构异常：未找到 MacOS 目录"
    echo "🔍 路径: $MACOS_DIR"
    exit 1
fi

# 检查 Resources 目录，如不存在则创建
RESOURCES_DIR="$APP_PATH/Contents/Resources"
if [ ! -d "$RESOURCES_DIR" ]; then
    echo "⚠️ Resources 目录不存在，正在创建..."
    mkdir -p "$RESOURCES_DIR"
    if [ ! -d "$RESOURCES_DIR" ]; then
        echo "❌ 无法创建 Resources 目录"
        exit 1
    fi
fi

echo "🔧 开始复制脚本到Resources目录..."

# 复制每个脚本并验证
for script in "${SCRIPTS[@]}"; do
    SRC_PATH="$MACOS_DIR/$script"
    DEST_PATH="$RESOURCES_DIR/$script"
    
    echo "  📋 处理脚本: $script"
    
    # 检查源文件是否存在
    if [ ! -f "$SRC_PATH" ]; then
        echo "  ❌ 源文件不存在: $SRC_PATH"
        COPY_SUCCESS=false
        continue
    fi
    
    # 检查源文件是否可执行
    if [ ! -x "$SRC_PATH" ]; then
        echo "  ⚠️ 源文件没有执行权限，正在修复..."
        chmod +x "$SRC_PATH"
    fi
    
    # 执行复制
    if cp "$SRC_PATH" "$DEST_PATH" 2>/dev/null; then
        # 设置执行权限
        chmod +x "$DEST_PATH"
        
        # 验证复制结果
        if [ -f "$DEST_PATH" ] && [ -x "$DEST_PATH" ]; then
            # 验证文件内容是否正确
            if grep -q "macOS 应用包环境" "$DEST_PATH"; then
                echo "  ✅ $script 复制成功并验证通过"
            else
                echo "  ⚠️ $script 复制成功但内容验证失败（可能缺少环境检测逻辑）"
            fi
        else
            echo "  ❌ $script 复制失败或权限设置失败"
            COPY_SUCCESS=false
        fi
    else
        echo "  ❌ 复制 $script 失败"
        COPY_SUCCESS=false
    fi
done

# 检查复制结果
if [ "$COPY_SUCCESS" = false ]; then
    echo
    echo "❌ 脚本修复过程中出现错误！"
    echo "🔍 问题诊断："
    echo "  1. 检查磁盘空间是否充足"
    echo "  2. 检查文件权限"
    echo "  3. 查看上述错误信息"
    echo
    echo "🛠️ 手动修复命令："
    echo "  cd \"$PROJECT_ROOT\""
    for script in "${SCRIPTS[@]}"; do
        echo "  cp \"$APP_PATH/Contents/MacOS/$script\" \"$APP_PATH/Contents/Resources/$script\""
        echo "  chmod +x \"$APP_PATH/Contents/Resources/$script\""
    done
    echo
    echo "⚠️ 构建继续进行，但应用可能无法正常启动服务"
    echo
else
    echo
    echo "✅ 所有脚本修复完成！"
fi

# 最终验证
echo "🔍 最终验证..."
ALL_VERIFIED=true

for script in "${SCRIPTS[@]}"; do
    DEST_PATH="$RESOURCES_DIR/$script"
    if [ -f "$DEST_PATH" ] && [ -x "$DEST_PATH" ]; then
        echo "  ✅ $script 在 Resources 目录中存在且可执行"
    else
        echo "  ❌ $script 在 Resources 目录中不存在或不可执行"
        ALL_VERIFIED=false
    fi
done

if [ "$ALL_VERIFIED" = true ]; then
    echo "🎉 脚本修复验证通过！应用应该可以正常启动服务"
    
    # 步骤 6.6: 运行功能测试确保应用可用
    echo
    echo "6.6️⃣ 运行功能测试验证应用可用性..."
    
    if [ -f "./testing/test_packaged_app.py" ]; then
        echo "🧪 运行自动化测试..."
        
        # 运行测试脚本，捕获输出
        if python3 "./testing/test_packaged_app.py" "$APP_PATH" 2>&1 | grep -q "应用结构完整性检查.*PASS"; then
            echo "✅ 功能测试通过！应用已经可以直接使用"
            
            # 显示使用说明
            echo
            echo "🚀 应用准备就绪！"
            echo "📱 启动方式："
            echo "  方法1: 双击应用图标"
            echo "  方法2: open \"$APP_PATH\""
            echo "  方法3: ./testing/automated-tests/quick-build.sh (开发测试)"
            echo
        else
            echo "⚠️ 功能测试未完全通过，但基本功能应该可用"
            echo "💡 建议手动验证: open \"$APP_PATH\""
        fi
    else
        echo "⚠️ 测试脚本不存在，跳过功能测试"
        echo "💡 手动验证: open \"$APP_PATH\""
    fi
else
    echo "⚠️ 脚本修复验证未通过，应用可能无法正常启动服务"
    echo "💡 建议运行测试命令验证: ./testing/test_packaged_app.py \"$APP_PATH\""
    
    # 即使验证未通过，也提供手动修复指导
    echo
    echo "🛠️ 如果应用无法启动服务，请运行以下命令修复："
    echo "APP_PATH=\"$APP_PATH\""
    for script in "${SCRIPTS[@]}"; do
        echo "cp \"\$APP_PATH/Contents/MacOS/$script\" \"\$APP_PATH/Contents/Resources/$script\""
        echo "chmod +x \"\$APP_PATH/Contents/Resources/$script\""
    done
fi

echo "----------------------------------------"

# 步骤 7: 显示构建结果
echo
echo "🎉 构建完成！"
echo "📋 构建产物位置："

BUNDLE_DIR="src-tauri/target/release/bundle"
APP_PATH=""

if [ -d "$BUNDLE_DIR" ]; then
    find "$BUNDLE_DIR" -name "*.app" -o -name "*.dmg" -o -name "*.exe" -o -name "*.msi" -o -name "*.deb" -o -name "*.rpm" -o -name "*.AppImage" | while read file; do
        size=$(du -h "$file" | cut -f1)
        echo "  📄 $file ($size)"
        
        # 如果是 macOS .app 文件，验证内部的包装脚本
        if [[ "$file" == *.app ]]; then
            APP_PATH="$file"
            echo "🔍 验证应用包内的脚本..."
            COUNTER_SCRIPT="$APP_PATH/Contents/MacOS/counter-service"
            if [ -f "$COUNTER_SCRIPT" ]; then
                if grep -q "macOS 应用包环境" "$COUNTER_SCRIPT"; then
                    echo "  ✅ 应用包内的 counter-service 脚本正确"
                else
                    echo "  ❌ 应用包内的 counter-service 脚本不正确"
                    echo "  🔧 实际内容:"
                    head -10 "$COUNTER_SCRIPT" | sed 's/^/      /'
                fi
            else
                echo "  ❌ 应用包内缺少 counter-service 脚本"
            fi
        fi
    done
else
    echo "⚠️ Bundle 目录不存在: $BUNDLE_DIR"
fi

echo
echo "📋 验证打包后的 Python 资源..."
if [ -n "$APP_PATH" ] && [ -d "$APP_PATH/Contents/Resources/_up_" ]; then
    echo "✅ Python 资源目录存在: $APP_PATH/Contents/Resources/_up_/"
    if [ -f "$APP_PATH/Contents/Resources/_up_/src-python/test_services/counter_service.py" ]; then
        echo "✅ counter_service.py 已正确打包"
    else
        echo "❌ counter_service.py 未找到"
    fi
else
    echo "❌ Python 资源目录不存在"
fi

# 显示二进制包装器
echo
echo "📋 Python 服务包装器："
if [ -d "src-tauri/bin" ]; then
    ls -la src-tauri/bin/
else
    echo "⚠️ 二进制目录不存在"
fi

echo
echo "✅ 生产构建完成！"