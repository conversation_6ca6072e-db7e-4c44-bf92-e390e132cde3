import { contextBridge, ipcRenderer, IpcRendererEvent } from 'electron';

// 定义暴露给渲染进程的 API
const electronAPI = {
    // 交易相关
    trading: {
        connect: (adapter: string, config: any) => 
            ipcRenderer.invoke('trading:connect', { adapter, config }),
        
        disconnect: (adapter: string) => 
            ipcRenderer.invoke('trading:disconnect', { adapter }),
        
        getStatus: (adapter: string) => 
            ipcRenderer.invoke('trading:getStatus', { adapter }),
        
        // 行情数据
        getQuote: (adapter: string, stockCode: string) =>
            ipcRenderer.invoke('market:getQuote', { adapter, stockCode }),
        
        getOrderBook: (adapter: string, stockCode: string) =>
            ipcRenderer.invoke('market:getOrderBook', { adapter, stockCode }),
        
        getTicker: (adapter: string, stockCode: string, count?: number) =>
            ipcRenderer.invoke('market:getTicker', { adapter, stockCode, count }),
        
        getBrokerQueue: (adapter: string, stockCode: string) =>
            ipcRenderer.invoke('market:getBrokerQueue', { adapter, stockCode }),
        
        // 实时订阅
        subscribe: (adapter: string, stockCode: string, dataTypes: string[]) =>
            ipcRenderer.invoke('realtime:subscribe', { adapter, stockCode, dataTypes }),
        
        unsubscribe: (adapter: string, stockCode: string, dataTypes: string[]) =>
            ipcRenderer.invoke('realtime:unsubscribe', { adapter, stockCode, dataTypes }),
        
        // 交易操作
        placeOrder: (adapter: string, order: any) =>
            ipcRenderer.invoke('trade:placeOrder', { adapter, order }),
        
        cancelOrder: (adapter: string, orderId: string) =>
            ipcRenderer.invoke('trade:cancelOrder', { adapter, orderId }),
        
        getPositions: (adapter: string, accountId?: string) =>
            ipcRenderer.invoke('trade:getPositions', { adapter, accountId }),
        
        getOrders: (adapter: string, filter?: any) =>
            ipcRenderer.invoke('trade:getOrders', { adapter, filter }),
        
        getAccounts: (adapter: string) =>
            ipcRenderer.invoke('trade:getAccounts', { adapter })
    },

    // 任务管理
    task: {
        create: (config: any) => 
            ipcRenderer.invoke('task:create', config),
        
        start: (taskId: string) => 
            ipcRenderer.invoke('task:start', { taskId }),
        
        stop: (taskId: string) => 
            ipcRenderer.invoke('task:stop', { taskId }),
        
        delete: (taskId: string) => 
            ipcRenderer.invoke('task:delete', { taskId }),
        
        getList: () => 
            ipcRenderer.invoke('task:getList'),
        
        getDetail: (taskId: string) => 
            ipcRenderer.invoke('task:getDetail', { taskId })
    },

    // 配置管理
    config: {
        get: (key: string) => 
            ipcRenderer.invoke('config:get', { key }),
        
        set: (key: string, value: any) => 
            ipcRenderer.invoke('config:set', { key, value }),
        
        delete: (key: string) => 
            ipcRenderer.invoke('config:delete', { key }),
        
        getAll: () => 
            ipcRenderer.invoke('config:getAll')
    },

    // 窗口控制
    window: {
        minimize: () => ipcRenderer.send('window:minimize'),
        maximize: () => ipcRenderer.send('window:maximize'),
        close: () => ipcRenderer.send('window:close'),
        toggleDevTools: () => ipcRenderer.send('window:toggleDevTools'),
        reload: () => ipcRenderer.send('window:reload')
    },

    // 事件监听
    on: (channel: string, callback: (event: IpcRendererEvent, ...args: any[]) => void) => {
        // 验证通道白名单
        const validChannels = [
            'event:realtimeData',
            'event:connectionStatus',
            'event:error',
            'event:taskUpdate',
            'event:orderUpdate',
            'event:positionUpdate'
        ];
        
        if (validChannels.includes(channel)) {
            ipcRenderer.on(channel, callback);
        } else {
            console.error(`Invalid channel: ${channel}`);
        }
    },

    // 移除事件监听
    removeListener: (channel: string, callback: (...args: any[]) => void) => {
        ipcRenderer.removeListener(channel, callback);
    },

    // 移除所有事件监听
    removeAllListeners: (channel: string) => {
        ipcRenderer.removeAllListeners(channel);
    }
};

// 暴露 API 到渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型定义导出（供 TypeScript 使用）
export type ElectronAPI = typeof electronAPI;