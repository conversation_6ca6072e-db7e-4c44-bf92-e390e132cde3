import { app, BrowserWindow, protocol, session } from 'electron';
import path from 'path';
import { WindowManager } from './windowManager';
import { registerIPCHandlers } from './ipc/handlers';
import { isDev } from './utils/environment';
import { createApplicationMenu } from './menu';

// 防止多个实例
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    // 禁用 GPU 加速（如果需要）
    // app.disableHardwareAcceleration();

    // 设置应用名称
    app.setName('量化交易终端');

    // 当第二个实例尝试启动时
    app.on('second-instance', () => {
        const mainWindow = WindowManager.getMainWindow();
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });

    // 应用准备就绪
    app.whenReady().then(async () => {
        // 配置安全策略
        configureSecurityPolicy();

        // 注册协议
        protocol.registerFileProtocol('app', (request, callback) => {
            const url = request.url.slice('app://'.length);
            callback({ path: path.normalize(`${__dirname}/${url}`) });
        });

        // 注册 IPC 处理器
        registerIPCHandlers();

        // 创建应用菜单
        createApplicationMenu();

        // 创建主窗口
        WindowManager.createMainWindow();

        // 开发环境下安装 DevTools
        if (isDev()) {
            try {
                const { default: installExtension, REACT_DEVELOPER_TOOLS } = await import('electron-devtools-installer');
                await installExtension(REACT_DEVELOPER_TOOLS);
                console.log('React DevTools installed');
            } catch (err) {
                console.error('Failed to install React DevTools:', err);
            }
        }
    });

    // 所有窗口关闭时
    app.on('window-all-closed', () => {
        // macOS 下不退出应用
        if (process.platform !== 'darwin') {
            app.quit();
        }
    });

    // 应用激活时
    app.on('activate', () => {
        // macOS 下如果没有窗口则创建一个
        if (BrowserWindow.getAllWindows().length === 0) {
            WindowManager.createMainWindow();
        }
    });

    // 应用退出前
    app.on('before-quit', () => {
        // 清理资源
        console.log('Application is quitting...');
    });

    // 渲染进程崩溃
    app.on('render-process-gone', (event, webContents, details) => {
        console.error('Render process gone:', details);
        // 可以选择重启应用或创建新窗口
    });
}

/**
 * 配置安全策略
 */
function configureSecurityPolicy(): void {
    // 设置 Content Security Policy
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
        callback({
            responseHeaders: {
                ...details.responseHeaders,
                'Content-Security-Policy': [
                    "default-src 'self'",
                    "script-src 'self' 'unsafe-inline'",
                    "style-src 'self' 'unsafe-inline'",
                    "img-src 'self' data: https:",
                    "connect-src 'self' http://localhost:* ws://localhost:*",
                    "font-src 'self'"
                ].join('; ')
            }
        });
    });

    // 阻止新窗口创建
    app.on('web-contents-created', (event, contents) => {
        // 使用 setWindowOpenHandler 替代已废弃的 new-window 事件
        contents.setWindowOpenHandler(() => {
            return { action: 'deny' };
        });

        // 禁用 webview
        contents.on('will-attach-webview', (event) => {
            event.preventDefault();
        });
    });

    // 限制导航
    app.on('web-contents-created', (event, contents) => {
        contents.on('will-navigate', (event, url) => {
            const parsedUrl = new URL(url);
            if (parsedUrl.origin !== 'http://localhost:1420' && parsedUrl.origin !== 'file://') {
                event.preventDefault();
                console.warn('Navigation blocked:', url);
            }
        });
    });
}