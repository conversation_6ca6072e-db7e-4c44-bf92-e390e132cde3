// IPC 通信类型定义

// API 响应类型
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 连接配置
export interface ConnectionConfig {
    host?: string;
    port?: number;
    apiKey?: string;
    apiSecret?: string;
    encryptKey?: string;
}

// 行情数据类型
export enum DataType {
    Quote = 'Quote',
    Ticker = 'Ticker',
    OrderBook = 'OrderBook',
    BrokerQueue = 'BrokerQueue'
}

// 报价数据
export interface Quote {
    stockCode: string;
    price: number;
    open: number;
    high: number;
    low: number;
    prevClose: number;
    volume: number;
    amount: number;
    change: number;
    changePercent: number;
    timestamp: number;
}

// 逐笔成交
export interface Ticker {
    stockCode: string;
    price: number;
    volume: number;
    direction: 'BUY' | 'SELL' | 'NEUTRAL';
    timestamp: number;
}

// 买卖盘
export interface OrderBook {
    stockCode: string;
    asks: OrderBookItem[];
    bids: OrderBookItem[];
    timestamp: number;
}

export interface OrderBookItem {
    price: number;
    volume: number;
    orderCount?: number;
}

// 经纪队列
export interface BrokerQueue {
    stockCode: string;
    askBrokers: BrokerItem[];
    bidBrokers: BrokerItem[];
    timestamp: number;
}

export interface BrokerItem {
    brokerId: string;
    brokerName: string;
    volume: number;
}

// 任务配置
export interface TaskConfig {
    stockCode: string;
    strategyType: string;
    strategyParams: Record<string, any>;
    riskControl?: RiskControlConfig;
}

// 风控配置
export interface RiskControlConfig {
    maxLoss?: number;
    maxPosition?: number;
    stopLossPercent?: number;
    takeProfitPercent?: number;
}

// 任务状态
export enum TaskStatus {
    CREATED = 'created',
    RUNNING = 'running',
    PAUSED = 'paused',
    STOPPED = 'stopped',
    ERROR = 'error'
}

// 任务信息
export interface TaskInfo {
    id: string;
    stockCode: string;
    strategyType: string;
    status: TaskStatus;
    position?: Position;
    profit?: number;
    createdAt: number;
    updatedAt: number;
}

// 持仓信息
export interface Position {
    stockCode: string;
    quantity: number;
    avgCost: number;
    currentPrice: number;
    profit: number;
    profitPercent: number;
}

// 订单信息
export interface Order {
    orderId: string;
    stockCode: string;
    orderType: 'BUY' | 'SELL';
    price: number;
    quantity: number;
    filledQuantity: number;
    status: OrderStatus;
    createdAt: number;
    updatedAt: number;
}

// 订单状态
export enum OrderStatus {
    PENDING = 'pending',
    SUBMITTED = 'submitted',
    PARTIAL_FILLED = 'partial_filled',
    FILLED = 'filled',
    CANCELLED = 'cancelled',
    REJECTED = 'rejected'
}