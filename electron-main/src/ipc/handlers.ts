import { ipcMain, IpcMainInvokeEvent } from 'electron';
import { WindowManager } from '../windowManager';

// API 响应类型
interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 错误处理装饰器
function handleError(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
        try {
            return await method.apply(this, args);
        } catch (error) {
            console.error(`Error in ${propertyName}:`, error);
            return {
                success: false,
                message: `操作失败: ${error instanceof Error ? error.message : '未知错误'}`,
                data: null,
                error: error instanceof Error ? error.stack : String(error)
            };
        }
    };
}

/**
 * 注册所有 IPC 处理器
 */
export function registerIPCHandlers(): void {
    // 注册交易相关处理器
    registerTradingHandlers();
    
    // 注册任务管理处理器
    registerTaskHandlers();
    
    // 注册配置管理处理器
    registerConfigHandlers();
    
    // 注册窗口控制处理器
    registerWindowHandlers();
    
    console.log('All IPC handlers registered');
}

/**
 * 注册交易相关处理器
 */
function registerTradingHandlers(): void {
    // 连接管理
    ipcMain.handle('trading:connect', async (event, args) => {
        console.log('Trading connect:', args);
        // TODO: 实现连接逻辑
        return createResponse(true, '连接成功', { connected: true });
    });

    ipcMain.handle('trading:disconnect', async (event, args) => {
        console.log('Trading disconnect:', args);
        // TODO: 实现断开连接逻辑
        return createResponse(true, '断开连接成功', null);
    });

    ipcMain.handle('trading:getStatus', async (event, args) => {
        console.log('Trading get status:', args);
        // TODO: 实现获取状态逻辑
        return createResponse(true, '获取状态成功', {
            adapter: args.adapter,
            connected: false,
            lastUpdate: Date.now()
        });
    });

    // 行情数据
    ipcMain.handle('market:getQuote', async (event, args) => {
        console.log('Market get quote:', args);
        // TODO: 实现获取报价逻辑
        return createResponse(true, '获取报价成功', {
            stockCode: args.stockCode,
            price: 420.5,
            change: 2.5,
            changePercent: 0.6,
            volume: 1000000,
            timestamp: Date.now()
        });
    });

    ipcMain.handle('market:getOrderBook', async (event, args) => {
        console.log('Market get order book:', args);
        // TODO: 实现获取买卖盘逻辑
        return createResponse(true, '获取买卖盘成功', {
            stockCode: args.stockCode,
            asks: [],
            bids: [],
            timestamp: Date.now()
        });
    });

    // 实时订阅
    ipcMain.handle('realtime:subscribe', async (event, args) => {
        console.log('Realtime subscribe:', args);
        // TODO: 实现订阅逻辑
        return createResponse(true, '订阅成功', { subscribed: true });
    });

    ipcMain.handle('realtime:unsubscribe', async (event, args) => {
        console.log('Realtime unsubscribe:', args);
        // TODO: 实现取消订阅逻辑
        return createResponse(true, '取消订阅成功', { unsubscribed: true });
    });
}

/**
 * 注册任务管理处理器
 */
function registerTaskHandlers(): void {
    ipcMain.handle('task:create', async (event, config) => {
        console.log('Task create:', config);
        // TODO: 实现创建任务逻辑
        const taskId = `task_${Date.now()}`;
        return createResponse(true, '任务创建成功', { taskId });
    });

    ipcMain.handle('task:start', async (event, args) => {
        console.log('Task start:', args);
        // TODO: 实现启动任务逻辑
        return createResponse(true, '任务启动成功', null);
    });

    ipcMain.handle('task:stop', async (event, args) => {
        console.log('Task stop:', args);
        // TODO: 实现停止任务逻辑
        return createResponse(true, '任务停止成功', null);
    });

    ipcMain.handle('task:getList', async (event) => {
        console.log('Task get list');
        // TODO: 实现获取任务列表逻辑
        return createResponse(true, '获取任务列表成功', []);
    });
}

/**
 * 注册配置管理处理器
 */
function registerConfigHandlers(): void {
    ipcMain.handle('config:get', async (event, args) => {
        console.log('Config get:', args);
        // TODO: 实现获取配置逻辑
        return createResponse(true, '获取配置成功', null);
    });

    ipcMain.handle('config:set', async (event, args) => {
        console.log('Config set:', args);
        // TODO: 实现设置配置逻辑
        return createResponse(true, '设置配置成功', null);
    });

    ipcMain.handle('config:getAll', async (event) => {
        console.log('Config get all');
        // TODO: 实现获取所有配置逻辑
        return createResponse(true, '获取所有配置成功', {});
    });
}

/**
 * 注册窗口控制处理器
 */
function registerWindowHandlers(): void {
    ipcMain.on('window:minimize', () => {
        WindowManager.minimize();
    });

    ipcMain.on('window:maximize', () => {
        WindowManager.toggleMaximize();
    });

    ipcMain.on('window:close', () => {
        WindowManager.close();
    });

    ipcMain.on('window:toggleDevTools', () => {
        WindowManager.toggleDevTools();
    });

    ipcMain.on('window:reload', () => {
        WindowManager.reload();
    });
}

/**
 * 创建标准响应
 */
function createResponse<T>(success: boolean, message: string, data: T): ApiResponse<T> {
    return {
        success,
        message,
        data
    };
}

/**
 * 发送事件到渲染进程
 */
export function sendToRenderer(channel: string, ...args: any[]): void {
    WindowManager.send(channel, ...args);
}

/**
 * 广播实时数据
 */
export function broadcastRealtimeData(adapter: string, data: any): void {
    sendToRenderer('event:realtimeData', {
        adapter,
        data,
        timestamp: Date.now()
    });
}

/**
 * 广播连接状态
 */
export function broadcastConnectionStatus(adapter: string, status: string): void {
    sendToRenderer('event:connectionStatus', {
        adapter,
        status,
        timestamp: Date.now()
    });
}

/**
 * 广播错误信息
 */
export function broadcastError(adapter: string, error: string): void {
    sendToRenderer('event:error', {
        adapter,
        error,
        timestamp: Date.now()
    });
}