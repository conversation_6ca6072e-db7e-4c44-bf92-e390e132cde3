# XX交易终端

基于 Tauri + React + Python 的现代化股票交易平台，专注于富途和华盛的行情数据和交易功能。

## 🚀 项目特点

-   **模块化架构**: 组件化前端 + 标准化通信 + 可扩展适配器
-   **类型安全**: 全栈 TypeScript + Rust 强类型系统
-   **实时数据**: 支持富途行情数据实时推送
-   **双平台支持**: 集成富途牛牛和华盛通
-   **高性能**: 基于 Rust 的 Tauri 框架，性能优异
-   **跨平台**: 支持 Windows、macOS、Linux
-   **易扩展**: 标准化适配器接口，易于添加新的交易平台

## 📁 项目结构

```
stock/
├── src/                    # React 前端源码 (模块化架构)
│   ├── components/         # 可复用组件
│   ├── pages/             # 页面组件
│   ├── hooks/             # 自定义 Hooks
│   ├── communication/     # 通信封装
│   ├── types/             # 类型定义
│   └── utils/             # 工具函数
├── src-tauri/             # Tauri 后端源码
│   └── src/
│       ├── communication/ # 通信模块
│       ├── commands.rs    # 命令定义
│       └── main.rs        # 应用入口
├── src-python/            # Python 适配器
│   └── adapters/          # 标准化适配器
│       ├── tauri_communication.py    # 通信基础类
│       ├── futu_adapter_enhanced.py  # 富途适配器
│       └── huasheng_adapter_enhanced.py # 华盛适配器
├── config/                # 配置文件
├── docs/                  # 项目文档
└── dist/                  # 构建输出
```

## 🛠️ 技术栈

### 前端

-   **Tauri**: 跨平台桌面应用框架
-   **React 18**: 现代化前端框架
-   **TypeScript**: 类型安全的 JavaScript
-   **Vite**: 快速构建工具
-   **Recharts**: 数据可视化图表库

### 后端

-   **Rust**: Tauri 后端语言
-   **Python**: 交易接口适配器
-   **富途 OpenAPI**: 富途牛牛行情和交易接口
-   **华盛 ProAPI**: 华盛通交易接口

## 📚 文档

所有项目文档都在 `docs/` 目录中统一管理：

### 📋 核心文档

-   [📖 文档索引](docs/文档索引.md) - 完整的文档导航和索引
-   [📋 项目详细说明](docs/项目详细说明.md) - 详细的项目介绍和使用说明

### 🏗️ 架构设计

-   [🔧 Tauri Sidecar 实时通信方案](docs/Tauri_Sidecar_实时通信方案设计文档.md) - 核心架构设计
-   [📡 进程间通信深度解析](<docs/进程间通信%20(IPC)%20深度解析：从理论到%20Tauri%20实践.md>) - IPC 技术详解

### 🔧 功能说明

-   [📈 富途牛牛与华盛通接口职责说明](docs/富途牛牛与华盛通接口职责说明.md) - 双平台接口职责分工详解

> 💡 **提示**: 查看 [文档索引](docs/文档索引.md) 获取完整的文档列表和分类

## 🚀 快速开始

### 🎯 一键构建部署（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd stock

# 一键构建
./scripts/build-production.sh
```

构建完成后应用即可直接使用，支持双击启动或命令行启动。

### ⚡ 开发模式

```bash
# 安装依赖
yarn install

# 启动开发模式
yarn tauri dev
```

### 📋 环境要求

> **前提条件**: 需要先安装基础工具才能使用一键构建

-   Node.js 18+
-   Rust 1.70+
-   Python 3.8+
-   uv (Python 包管理器)
-   富途 OpenD (用于富途功能)

详细的环境安装步骤请参考 [构建部署指南](docs/构建部署指南.md)。

## ⚙️ 配置

配置文件位于 `config/` 目录：

-   `development.json` - 开发环境配置
-   `production.json` - 生产环境配置
-   `user.json.example` - 用户配置模板

复制 `user.json.example` 为 `user.json` 并填入您的账号信息。

### Python 依赖管理

项目使用 `uv` 进行 Python 依赖管理，主要依赖包括：

-   **futu-api**: 富途 OpenAPI Python SDK
-   **pandas**: 数据处理和分析

所有依赖都在根目录的 `pyproject.toml` 中定义。

## 🔧 功能特性

### 富途牛牛 (行情数据)

-   ✅ 实时股价数据
-   ✅ 逐笔交易数据
-   ✅ 买卖盘数据
-   ✅ 经纪队列数据
-   ❌ 交易功能 (已移除)

### 华盛通 (交易功能)

-   ✅ 账户资金查询
-   ✅ 持仓查询
-   ✅ 委托下单
-   ✅ 撤单操作
-   ✅ 订单查询

## 🛠️ 故障排除

### 常见问题

如遇到以下问题，请参考详细的 [故障排除指南](docs/故障排除指南.md)：

- **"Sidecar binary not found" 错误**：使用 `./scripts/build-production.sh` 构建
- **"uv command not found" 错误**：检查 uv 安装或使用内置路径检测
- **Python 脚本路径错误**：验证应用包内的 Python 资源
- **构建脚本失败**：检查依赖安装和工具版本

### 获取帮助

详细的问题解决方案：
1. [故障排除指南](docs/故障排除指南.md) - 完整的问题诊断和解决方案
2. [构建部署指南](docs/构建部署指南.md) - 详细的构建流程说明
3. [开发日志](docs/开发日志.md) - 已知问题和解决记录

## 💬 反馈与建议

如有使用问题或功能建议，请通过以下方式联系：

## 📄 许可证

本项目为私有软件，保留所有权利。未经授权，不得复制、分发或修改本软件的任何部分。

## 📞 联系

如有问题，请通过 xxx xxxx xxxx 联系。
