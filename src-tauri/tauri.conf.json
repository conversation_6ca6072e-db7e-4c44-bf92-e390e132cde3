{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "build": {"beforeBuildCommand": "yarn build", "beforeDevCommand": "yarn dev", "devPath": "http://localhost:1420", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "XX交易终端", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "execute": true, "sidecar": true, "open": true, "scope": [{"name": "run-python-script", "cmd": "python3", "args": ["-u", {"validator": "\\S+"}]}]}, "window": {"all": false}}, "bundle": {"active": true, "targets": "all", "identifier": "com.futunn.trading-platform", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["../src-python/**/*", "../pyproject.toml", "../uv.lock"], "externalBin": ["bin/counter-service", "bin/futu-adapter", "bin/huasheng-adapter"], "copyright": "", "category": "DeveloperTool", "shortDescription": "", "longDescription": "", "deb": {"depends": []}, "macOS": {"frameworks": [], "minimumSystemVersion": "", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": null}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "updater": {"active": false}, "windows": [{"fullscreen": false, "resizable": true, "title": "XX交易终端", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}]}}