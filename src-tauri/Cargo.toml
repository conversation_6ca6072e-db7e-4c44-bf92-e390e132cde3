[package]
name = "futunn-trading-platform"
version = "0.1.0"
description = "XX交易终端 - <PERSON><PERSON> + React + Python Sidecar"
authors = ["futunn"]
license = ""
repository = ""
edition = "2021"
rust-version = "1.60"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5.0", features = ["shell-open", "shell-execute", "shell-sidecar"] }
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"
uuid = { version = "1.0", features = ["v4"] }

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]
