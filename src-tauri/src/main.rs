// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Manager;

mod types;
mod sidecar_manager;
mod multi_sidecar_manager;
mod communication;
mod multi_commands;

use multi_sidecar_manager::MultiSidecarManager;

// 引入多服务命令
use multi_commands::*;



fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            // 多服务管理
            start_service,
            start_service_by_user,
            stop_service,
            restart_service,
            get_service_status,
            get_all_services_status,
            send_service_command,
            start_auto_services,
            force_start_auto_services,
            stop_all_services,
            add_service_config,
            remove_service_config,

            // 富途和华盛专用接口
            start_futu_service,
            stop_futu_service,
            start_huasheng_service,
            stop_huasheng_service,
            send_futu_command,
            send_huasheng_command
        ])
        .setup(|app| {
            // 应用启动时的初始化逻辑
            println!("XX交易终端启动中...");

            let app_handle = app.handle();

            // 创建多服务管理器实例
            let multi_manager = MultiSidecarManager::new(app_handle.clone());

            // 将多服务管理器添加到应用状态
            app.manage(multi_manager);

            // 应用启动时自动启动配置为自动启动的服务
            let app_handle_clone = app_handle.clone();
            tauri::async_runtime::spawn(async move {
                if let Some(manager) = app_handle_clone.try_state::<MultiSidecarManager>() {
                    if let Err(e) = manager.start_auto_services().await {
                        eprintln!("Failed to start auto services on startup: {}", e);
                    } else {
                        println!("✅ Auto services started successfully");
                    }
                }
            });

            println!("✅ 应用初始化完成");

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}