use crate::multi_sidecar_manager::{MultiSidecarManager, ServiceStatus, ServiceConfig};
use serde_json::json;
use tauri::State;

/// 启动指定服务
#[tauri::command]
pub async fn start_service(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<String, String> {
    manager.start_service(&service_name).await?;
    Ok(format!("Service '{}' started successfully", service_name))
}

/// 用户手动启动指定服务
#[tauri::command]
pub async fn start_service_by_user(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<String, String> {
    manager.start_service_by_user(&service_name).await?;
    Ok(format!("Service '{}' started by user successfully", service_name))
}

/// 停止指定服务
#[tauri::command]
pub async fn stop_service(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<String, String> {
    manager.stop_service(&service_name).await?;
    Ok(format!("Service '{}' stopped successfully", service_name))
}

/// 重启指定服务
#[tauri::command]
pub async fn restart_service(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<String, String> {
    manager.restart_service(&service_name).await?;
    Ok(format!("Service '{}' restarted successfully", service_name))
}

/// 获取指定服务状态
#[tauri::command]
pub async fn get_service_status(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<ServiceStatus, String> {
    manager.get_service_status(&service_name).await
}

/// 获取所有服务状态
#[tauri::command]
pub async fn get_all_services_status(
    manager: State<'_, MultiSidecarManager>,
) -> Result<Vec<ServiceStatus>, String> {
    Ok(manager.get_all_services_status().await)
}

/// 向指定服务发送命令
#[tauri::command]
pub async fn send_service_command(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
    action: String,
    params: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    manager.send_command_to_service(&service_name, action, params).await
}

/// 启动所有自动启动服务
#[tauri::command]
pub async fn start_auto_services(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.start_auto_services().await?;
    Ok("Auto-start services initiated".to_string())
}

/// 强制启动所有自动启动服务（忽略用户停止状态）
#[tauri::command]
pub async fn force_start_auto_services(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.force_start_auto_services().await?;
    Ok("Force auto-start services initiated".to_string())
}

/// 停止所有服务
#[tauri::command]
pub async fn stop_all_services(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.stop_all_services().await?;
    Ok("All services stopped".to_string())
}

/// 添加新的服务配置
#[tauri::command]
pub async fn add_service_config(
    manager: State<'_, MultiSidecarManager>,
    name: String,
    script_path: String,
    description: String,
    auto_start: bool,
) -> Result<String, String> {
    let config = ServiceConfig {
        name: name.clone(),
        script_path,
        description,
        auto_start,
    };
    
    manager.add_service_config(config).await;
    Ok(format!("Service config '{}' added successfully", name))
}

/// 移除服务配置
#[tauri::command]
pub async fn remove_service_config(
    manager: State<'_, MultiSidecarManager>,
    service_name: String,
) -> Result<String, String> {
    manager.remove_service_config(&service_name).await?;
    Ok(format!("Service config '{}' removed successfully", service_name))
}

// 兼容性命令 - 保持与现有前端代码的兼容性

/// 启动 Sidecar（兼容性接口，默认启动计数器服务）
#[tauri::command]
pub async fn start_sidecar_compat(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.start_service("counter").await?;
    Ok("Sidecar started successfully".to_string())
}

/// 停止 Sidecar（兼容性接口，停止计数器服务）
#[tauri::command]
pub async fn stop_sidecar_compat(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.stop_service("counter").await?;
    Ok("Sidecar stopped successfully".to_string())
}

/// 发送 Sidecar 命令（兼容性接口，发送到计数器服务）
#[tauri::command]
pub async fn send_sidecar_command_compat(
    manager: State<'_, MultiSidecarManager>,
    action: String,
    params: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    manager.send_command_to_service("counter", action, params).await
}

/// Ping Sidecar（兼容性接口，ping 计数器服务）
#[tauri::command]
pub async fn ping_sidecar_compat(
    manager: State<'_, MultiSidecarManager>,
) -> Result<serde_json::Value, String> {
    manager.send_command_to_service("counter", "ping".to_string(), None).await
}

/// 获取 Sidecar 状态（兼容性接口，获取计数器服务状态）
#[tauri::command]
pub async fn sidecar_status_compat(
    manager: State<'_, MultiSidecarManager>,
) -> Result<serde_json::Value, String> {
    let status = manager.get_service_status("counter").await?;
    Ok(json!({
        "running": status.running,
        "service": status.name,
        "description": status.description
    }))
}

// 富途和华盛专用命令

/// 启动富途服务
#[tauri::command]
pub async fn start_futu_service(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.start_service("futu").await?;
    Ok("Futu service started successfully".to_string())
}

/// 停止富途服务
#[tauri::command]
pub async fn stop_futu_service(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.stop_service("futu").await?;
    Ok("Futu service stopped successfully".to_string())
}

/// 启动华盛服务
#[tauri::command]
pub async fn start_huasheng_service(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.start_service("huasheng").await?;
    Ok("Huasheng service started successfully".to_string())
}

/// 停止华盛服务
#[tauri::command]
pub async fn stop_huasheng_service(
    manager: State<'_, MultiSidecarManager>,
) -> Result<String, String> {
    manager.stop_service("huasheng").await?;
    Ok("Huasheng service stopped successfully".to_string())
}

/// 向富途服务发送命令
#[tauri::command]
pub async fn send_futu_command(
    manager: State<'_, MultiSidecarManager>,
    action: String,
    params: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    manager.send_command_to_service("futu", action, params).await
}

/// 向华盛服务发送命令
#[tauri::command]
pub async fn send_huasheng_command(
    manager: State<'_, MultiSidecarManager>,
    action: String,
    params: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    manager.send_command_to_service("huasheng", action, params).await
}
