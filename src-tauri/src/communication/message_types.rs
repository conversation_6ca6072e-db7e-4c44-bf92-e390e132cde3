// 消息类型定义
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Sidecar 命令结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SidecarCommand {
    pub id: String,
    pub action: String,
    pub params: Option<serde_json::Value>,
    pub timestamp: f64,
}

/// Sidecar 消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SidecarMessage {
    #[serde(rename = "type")]
    pub message_type: String,
    pub source: String,
    pub command_id: Option<String>,
    pub data: Option<serde_json::Value>,
    pub timestamp: f64,
    pub success: bool,
    pub error: Option<String>,
}

/// API 响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse {
    pub success: bool,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// 交易数据类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum TradingDataType {
    Quote,      // 报价数据
    Ticker,     // 逐笔数据
    OrderBook,  // 买卖盘数据
    Broker,     // 经纪队列数据
    Funds,      // 资金数据
    Position,   // 持仓数据
    Order,      // 订单数据
}

/// 股票代码结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StockCode {
    pub market: String,  // HK, US, SH, SZ
    pub code: String,    // 股票代码
}

impl StockCode {
    pub fn new(market: &str, code: &str) -> Self {
        Self {
            market: market.to_string(),
            code: code.to_string(),
        }
    }
    
    pub fn full_code(&self) -> String {
        format!("{}.{}", self.market, self.code)
    }
    
    pub fn from_full_code(full_code: &str) -> Result<Self, String> {
        let parts: Vec<&str> = full_code.split('.').collect();
        if parts.len() != 2 {
            return Err(format!("Invalid stock code format: {}", full_code));
        }
        
        Ok(Self::new(parts[0], parts[1]))
    }
}

/// 交易命令参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingCommandParams {
    pub stock_code: Option<String>,
    pub data_type: Option<TradingDataType>,
    pub count: Option<u32>,
    pub subscribe: Option<bool>,
    pub extra: Option<HashMap<String, serde_json::Value>>,
}

impl TradingCommandParams {
    pub fn new() -> Self {
        Self {
            stock_code: None,
            data_type: None,
            count: None,
            subscribe: None,
            extra: None,
        }
    }
    
    pub fn with_stock_code(mut self, stock_code: &str) -> Self {
        self.stock_code = Some(stock_code.to_string());
        self
    }
    
    pub fn with_data_type(mut self, data_type: TradingDataType) -> Self {
        self.data_type = Some(data_type);
        self
    }
    
    pub fn with_count(mut self, count: u32) -> Self {
        self.count = Some(count);
        self
    }
    
    pub fn with_subscribe(mut self, subscribe: bool) -> Self {
        self.subscribe = Some(subscribe);
        self
    }
    
    pub fn with_extra(mut self, key: &str, value: serde_json::Value) -> Self {
        if self.extra.is_none() {
            self.extra = Some(HashMap::new());
        }
        self.extra.as_mut().unwrap().insert(key.to_string(), value);
        self
    }
}

/// 实时数据事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealtimeDataEvent {
    pub source: String,
    pub data_type: String,
    pub stock_code: Option<String>,
    pub data: serde_json::Value,
    pub timestamp: f64,
}

/// 连接状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConnectionStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
}

/// 适配器状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterStatus {
    pub name: String,
    pub connection_status: ConnectionStatus,
    pub last_heartbeat: Option<f64>,
    pub error_count: u32,
    pub uptime: f64,
}

impl Default for TradingCommandParams {
    fn default() -> Self {
        Self::new()
    }
}
