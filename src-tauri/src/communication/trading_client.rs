// 交易客户端 - 专门处理交易相关的通信
use crate::communication::{SidecarClient, TradingCommandParams, TradingDataType};
use serde_json::json;
use tauri::AppHandle;

/// 交易客户端 - 封装交易相关的通信方法
pub struct TradingClient {
    sidecar: SidecarClient,
}

impl TradingClient {
    /// 创建新的交易客户端
    pub fn new(app_handle: AppHandle, adapter_name: &str, script_path: &str) -> Self {
        Self {
            sidecar: SidecarClient::new(app_handle, adapter_name, script_path),
        }
    }

    /// 启动交易适配器
    pub async fn start(&self) -> Result<(), String> {
        self.sidecar.start().await
    }

    /// 停止交易适配器
    pub async fn stop(&self) {
        self.sidecar.stop().await;
    }

    /// 连接到交易服务
    pub async fn connect(&self, params: Option<serde_json::Value>) -> Result<serde_json::Value, String> {
        let response = self.sidecar
            .send_command_with_response("connect".to_string(), params)
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Connection failed".to_string()))
        }
    }

    /// 断开交易服务连接
    pub async fn disconnect(&self) -> Result<serde_json::Value, String> {
        let response = self.sidecar
            .send_command_with_response("disconnect".to_string(), None)
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Disconnection failed".to_string()))
        }
    }

    /// 获取股票报价数据
    pub async fn get_quote(&self, stock_code: &str) -> Result<serde_json::Value, String> {
        let params = TradingCommandParams::new()
            .with_stock_code(stock_code)
            .with_data_type(TradingDataType::Quote);

        let response = self.sidecar
            .send_command_with_response("get_quote".to_string(), Some(json!(params)))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get quote".to_string()))
        }
    }

    /// 获取逐笔交易数据
    pub async fn get_ticker(&self, stock_code: &str, count: Option<u32>) -> Result<serde_json::Value, String> {
        let params = TradingCommandParams::new()
            .with_stock_code(stock_code)
            .with_data_type(TradingDataType::Ticker)
            .with_count(count.unwrap_or(20));

        let response = self.sidecar
            .send_command_with_response("get_ticker".to_string(), Some(json!(params)))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get ticker data".to_string()))
        }
    }

    /// 获取买卖盘数据
    pub async fn get_order_book(&self, stock_code: &str) -> Result<serde_json::Value, String> {
        let params = TradingCommandParams::new()
            .with_stock_code(stock_code)
            .with_data_type(TradingDataType::OrderBook);

        let response = self.sidecar
            .send_command_with_response("get_order_book".to_string(), Some(json!(params)))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get order book".to_string()))
        }
    }

    /// 获取经纪队列数据
    pub async fn get_broker_queue(&self, stock_code: &str) -> Result<serde_json::Value, String> {
        let params = TradingCommandParams::new()
            .with_stock_code(stock_code)
            .with_data_type(TradingDataType::Broker);

        let response = self.sidecar
            .send_command_with_response("get_broker_queue".to_string(), Some(json!(params)))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get broker queue".to_string()))
        }
    }

    /// 订阅实时数据
    pub async fn subscribe_realtime(&self, stock_code: &str, data_types: Vec<TradingDataType>) -> Result<serde_json::Value, String> {
        let params = json!({
            "stock_code": stock_code,
            "data_types": data_types,
            "subscribe": true
        });

        let response = self.sidecar
            .send_command_with_response("subscribe_realtime".to_string(), Some(params))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to subscribe".to_string()))
        }
    }

    /// 取消订阅实时数据
    pub async fn unsubscribe_realtime(&self, stock_code: &str, data_types: Vec<TradingDataType>) -> Result<serde_json::Value, String> {
        let params = json!({
            "stock_code": stock_code,
            "data_types": data_types,
            "subscribe": false
        });

        let response = self.sidecar
            .send_command_with_response("unsubscribe_realtime".to_string(), Some(params))
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to unsubscribe".to_string()))
        }
    }

    /// 获取资金信息
    pub async fn get_funds(&self) -> Result<serde_json::Value, String> {
        let response = self.sidecar
            .send_command_with_response("get_funds".to_string(), None)
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get funds".to_string()))
        }
    }

    /// 获取持仓信息
    pub async fn get_positions(&self) -> Result<serde_json::Value, String> {
        let response = self.sidecar
            .send_command_with_response("get_positions".to_string(), None)
            .await?;

        if response.success {
            Ok(response.data.unwrap_or(json!({})))
        } else {
            Err(response.error.unwrap_or("Failed to get positions".to_string()))
        }
    }

    /// 心跳检查
    pub async fn ping(&self) -> Result<(), String> {
        self.sidecar.ping().await
    }

    /// 获取适配器名称
    pub fn get_adapter_name(&self) -> &str {
        self.sidecar.get_adapter_name()
    }

    /// 获取底层 SidecarClient 引用（用于高级操作）
    pub fn get_sidecar(&self) -> &SidecarClient {
        &self.sidecar
    }
}
