// Sidecar 客户端 - 通用的 Python Sidecar 通信客户端
use crate::communication::message_types::*;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::process::{Child, ChildStdin, Command};
use tokio::sync::{oneshot, Mutex};
use tokio::time::timeout;
use uuid::Uuid;

/// Sidecar 客户端 - 管理与 Python Sidecar 的通信
pub struct SidecarClient {
    child: Arc<Mutex<Option<Child>>>,
    stdin: Arc<Mutex<Option<ChildStdin>>>,
    app_handle: AppHandle,
    pending_commands: Arc<Mutex<HashMap<String, oneshot::Sender<SidecarMessage>>>>,
    adapter_name: String,
    script_path: String,
}

impl SidecarClient {
    pub fn new(app_handle: AppHandle, adapter_name: &str, script_path: &str) -> Self {
        Self {
            child: Arc::new(Mutex::new(None)),
            stdin: Arc::new(Mutex::new(None)),
            app_handle,
            pending_commands: Arc::new(Mutex::new(HashMap::new())),
            adapter_name: adapter_name.to_string(),
            script_path: script_path.to_string(),
        }
    }

    /// 启动 Sidecar 进程
    pub async fn start(&self) -> Result<(), String> {
        // 停止现有进程
        self.stop().await;

        // 获取项目根目录
        let project_root = std::env::current_dir()
            .unwrap()
            .parent()
            .unwrap_or_else(|| std::path::Path::new("."))
            .to_path_buf();

        let script_path = project_root.join(&self.script_path);

        eprintln!("Starting {} adapter at: {:?}", self.adapter_name, script_path);

        // 启动 Python 进程
        let mut child = Command::new("uv")
            .arg("run")
            .arg("python")
            .arg(script_path)
            .current_dir(project_root)
            .stdin(std::process::Stdio::piped())
            .stdout(std::process::Stdio::piped())
            .stderr(std::process::Stdio::piped())
            .spawn()
            .map_err(|e| format!("Failed to start {} adapter: {}", self.adapter_name, e))?;

        // 获取 stdin 和 stdout
        let stdin = child.stdin.take().unwrap();
        let stdout = child.stdout.take().unwrap();
        let stderr = child.stderr.take().unwrap();

        // 存储 stdin 引用
        *self.stdin.lock().await = Some(stdin);

        // 启动 stdout 监听器
        let app_handle = self.app_handle.clone();
        let pending_commands = self.pending_commands.clone();
        let adapter_name_stdout = self.adapter_name.clone();

        tokio::spawn(async move {
            let reader = BufReader::new(stdout);
            let mut lines = reader.lines();

            while let Ok(Some(line)) = lines.next_line().await {
                if let Ok(message) = serde_json::from_str::<SidecarMessage>(&line) {
                    match message.message_type.as_str() {
                        "push" => {
                            // 实时数据推送到前端
                            let event_name = format!("sidecar-{}", message.source);
                            if let Err(e) = app_handle.emit_all(&event_name, &message) {
                                eprintln!("Failed to emit event {}: {}", event_name, e);
                            }
                        }
                        "response" => {
                            // 命令响应处理
                            if let Some(command_id) = &message.command_id {
                                let mut pending = pending_commands.lock().await;
                                if let Some(sender) = pending.remove(command_id) {
                                    let _ = sender.send(message);
                                }
                            }
                        }
                        _ => {
                            eprintln!("Unknown message type from {}: {}", adapter_name_stdout, message.message_type);
                        }
                    }
                } else {
                    eprintln!("Failed to parse message from {}: {}", adapter_name_stdout, line);
                }
            }
        });

        // 启动 stderr 监听器
        let adapter_name_stderr = self.adapter_name.clone();
        tokio::spawn(async move {
            let reader = BufReader::new(stderr);
            let mut lines = reader.lines();

            while let Ok(Some(line)) = lines.next_line().await {
                eprintln!("[{}]: {}", adapter_name_stderr, line);
            }
        });

        // 存储子进程引用
        *self.child.lock().await = Some(child);

        Ok(())
    }

    /// 停止 Sidecar 进程
    pub async fn stop(&self) {
        if let Some(mut child) = self.child.lock().await.take() {
            let _ = child.kill().await;
        }
        *self.stdin.lock().await = None;
    }

    /// 发送命令并等待响应
    pub async fn send_command_with_response(
        &self,
        action: String,
        params: Option<serde_json::Value>,
    ) -> Result<SidecarMessage, String> {
        let command = SidecarCommand {
            id: Uuid::new_v4().to_string(),
            action,
            params,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs_f64(),
        };

        let command_id = command.id.clone();

        // 创建响应接收器
        let (tx, rx) = oneshot::channel();
        {
            let mut pending = self.pending_commands.lock().await;
            pending.insert(command_id.clone(), tx);
        }

        // 发送命令
        self.send_command(command).await?;

        // 等待响应（带超时）
        match timeout(Duration::from_secs(10), rx).await {
            Ok(Ok(response)) => Ok(response),
            Ok(Err(_)) => Err("Response channel closed".to_string()),
            Err(_) => {
                // 清理超时的命令
                self.pending_commands.lock().await.remove(&command_id);
                Err("Command timeout".to_string())
            }
        }
    }

    /// 发送命令（不等待响应）
    async fn send_command(&self, command: SidecarCommand) -> Result<(), String> {
        let command_json = serde_json::to_string(&command)
            .map_err(|e| format!("Serialize error: {}", e))?;

        let mut stdin_guard = self.stdin.lock().await;
        if let Some(stdin) = stdin_guard.as_mut() {
            stdin
                .write_all(format!("{}\n", command_json).as_bytes())
                .await
                .map_err(|e| format!("Write error: {}", e))?;

            stdin
                .flush()
                .await
                .map_err(|e| format!("Flush error: {}", e))?;

            Ok(())
        } else {
            Err(format!("{} adapter not running", self.adapter_name))
        }
    }

    /// 心跳检查
    pub async fn ping(&self) -> Result<(), String> {
        let response = self
            .send_command_with_response("ping".to_string(), None)
            .await?;

        if response.success {
            Ok(())
        } else {
            Err(response.error.unwrap_or("Ping failed".to_string()))
        }
    }

    /// 获取适配器状态
    pub async fn get_status(&self) -> Result<AdapterStatus, String> {
        let response = self
            .send_command_with_response("get_status".to_string(), None)
            .await?;

        if response.success {
            // 解析状态数据
            Ok(AdapterStatus {
                name: self.adapter_name.clone(),
                connection_status: ConnectionStatus::Connected,
                last_heartbeat: Some(response.timestamp),
                error_count: 0,
                uptime: response.timestamp,
            })
        } else {
            Err(response.error.unwrap_or("Failed to get status".to_string()))
        }
    }

    /// 获取适配器名称
    pub fn get_adapter_name(&self) -> &str {
        &self.adapter_name
    }
}
