use std::collections::{HashMap, HashSet};
use std::sync::Arc;
use tokio::sync::Mutex;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;

use crate::sidecar_manager::SidecarManager;

/// 服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub name: String,
    pub script_path: String,
    pub description: String,
    pub auto_start: bool,
}

/// 服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatus {
    pub name: String,
    pub running: bool,
    pub description: String,
    pub last_started: Option<String>,
    pub last_error: Option<String>,
}

/// 多服务 Sidecar 管理器
/// 
/// 负责管理多个 Python 服务的生命周期
/// - 富途牛牛服务
/// - 华盛通服务
/// - 其他自定义服务
#[derive(Debug)]
pub struct MultiSidecarManager {
    services: Arc<Mutex<HashMap<String, SidecarManager>>>,
    configs: Arc<Mutex<HashMap<String, ServiceConfig>>>,
    app_handle: AppHandle,
    // 新增：跟踪用户手动停止的服务，防止自动重启
    user_stopped_services: Arc<Mutex<HashSet<String>>>,
}

impl MultiSidecarManager {
    pub fn new(app_handle: AppHandle) -> Self {
        let mut configs = HashMap::new();
        
        // 预定义服务配置
        configs.insert("futu".to_string(), ServiceConfig {
            name: "futu".to_string(),
            script_path: "src-python/adapters/futu_adapter_enhanced.py".to_string(),
            description: "富途牛牛交易服务".to_string(),
            auto_start: false,
        });
        
        configs.insert("huasheng".to_string(), ServiceConfig {
            name: "huasheng".to_string(),
            script_path: "src-python/adapters/huasheng_adapter_enhanced.py".to_string(),
            description: "华盛通交易服务".to_string(),
            auto_start: false,
        });
        
        // 测试服务（后续会删除）
        configs.insert("counter".to_string(), ServiceConfig {
            name: "counter".to_string(),
            script_path: "src-python/test_services/counter_service.py".to_string(),
            description: "计数器测试服务".to_string(),
            auto_start: true,  // 设置为自动启动来测试修复效果
        });
        
        Self {
            services: Arc::new(Mutex::new(HashMap::new())),
            configs: Arc::new(Mutex::new(configs)),
            app_handle,
            user_stopped_services: Arc::new(Mutex::new(HashSet::new())),
        }
    }
    
    /// 启动指定服务
    pub async fn start_service(&self, service_name: &str) -> Result<(), String> {
        self.start_service_internal(service_name, false).await
    }
    
    /// 用户手动启动服务
    pub async fn start_service_by_user(&self, service_name: &str) -> Result<(), String> {
        self.start_service_internal(service_name, true).await
    }
    
    /// 启动指定服务（内部实现）
    async fn start_service_internal(&self, service_name: &str, started_by_user: bool) -> Result<(), String> {
        println!("🔍 start_service called for: {} (by_user={})", service_name, started_by_user);
        
        let config = {
            let configs = self.configs.lock().await;
            configs.get(service_name)
                .ok_or_else(|| format!("Service '{}' not found", service_name))?
                .clone()
        };
        
        let mut services = self.services.lock().await;
        
        // 检查服务是否已经存在且正在运行
        if let Some(existing_service) = services.get(service_name) {
            if existing_service.is_running().await {
                println!("🔍 Service '{}' already running, skipping", service_name);
                return Ok(());  // 已经在运行
            }
        }
        
        // 创建新的 SidecarManager 实例
        let mut sidecar = SidecarManager::new(self.app_handle.clone());
        sidecar.set_script_path(&config.script_path).await;

        // 启动服务
        sidecar.start().await?;
        
        // 存储到服务列表
        services.insert(service_name.to_string(), sidecar);
        
        // 重要：只有用户手动启动时，才从用户停止列表中移除
        if started_by_user {
            let removed = self.user_stopped_services.lock().await.remove(service_name);
            if removed {
                println!("🔍 User manually started '{}', removed from user-stopped list", service_name);
            }
        }
        
        println!("✅ Service '{}' started successfully", service_name);
        Ok(())
    }
    
    /// 停止指定服务
    /// 
    /// stop_by_user: true 表示用户手动停止，false 表示系统停止
    pub async fn stop_service_internal(&self, service_name: &str, stop_by_user: bool) -> Result<(), String> {
        let mut services = self.services.lock().await;
        
        if let Some(service) = services.get(service_name) {
            service.stop().await;
            services.remove(service_name);
            
            // 如果是用户手动停止，添加到用户停止列表，防止自动重启
            if stop_by_user {
                self.user_stopped_services.lock().await.insert(service_name.to_string());
                println!("🛑 Service '{}' stopped by user (will not auto-restart)", service_name);
            } else {
                println!("🛑 Service '{}' stopped by system", service_name);
            }
            
            Ok(())
        } else {
            Err(format!("Service '{}' not found or not running", service_name))
        }
    }
    
    /// 停止指定服务（用户手动停止）
    pub async fn stop_service(&self, service_name: &str) -> Result<(), String> {
        println!("🔍 User requested to stop service: {}", service_name);
        self.stop_service_internal(service_name, true).await
    }
    
    /// 重启指定服务
    pub async fn restart_service(&self, service_name: &str) -> Result<(), String> {
        // 重启时不标记为用户停止，允许后续自动启动
        self.stop_service_internal(service_name, false).await.ok(); // 忽略停止错误
        self.start_service(service_name).await
    }
    
    /// 获取指定服务的状态
    pub async fn get_service_status(&self, service_name: &str) -> Result<ServiceStatus, String> {
        let configs = self.configs.lock().await;
        let services = self.services.lock().await;
        
        let config = configs.get(service_name)
            .ok_or_else(|| format!("Service '{}' not found", service_name))?;
        
        let running = if let Some(service) = services.get(service_name) {
            service.is_running().await
        } else {
            false
        };
        
        Ok(ServiceStatus {
            name: config.name.clone(),
            running,
            description: config.description.clone(),
            last_started: None, // TODO: 实现时间戳记录
            last_error: None,   // TODO: 实现错误记录
        })
    }
    
    /// 获取所有服务的状态
    pub async fn get_all_services_status(&self) -> Vec<ServiceStatus> {
        let configs = self.configs.lock().await;
        let mut statuses = Vec::new();
        
        for service_name in configs.keys() {
            if let Ok(status) = self.get_service_status(service_name).await {
                statuses.push(status);
            }
        }
        
        statuses
    }
    
    /// 向指定服务发送命令
    pub async fn send_command_to_service(
        &self,
        service_name: &str,
        action: String,
        params: Option<serde_json::Value>,
    ) -> Result<serde_json::Value, String> {
        let services = self.services.lock().await;
        
        let service = services.get(service_name)
            .ok_or_else(|| format!("Service '{}' not found or not running", service_name))?;
        
        service.send_command_with_response(action, params).await
            .map(|response| response.data.unwrap_or(serde_json::json!({})))
    }
    
    /// 启动所有配置为自动启动的服务
    /// 
    /// 修改逻辑：跳过用户手动停止的服务
    pub async fn start_auto_services(&self) -> Result<(), String> {
        return self.start_auto_services_internal(false).await;
    }
    
    /// 启动所有配置为自动启动的服务（内部实现）
    /// 
    /// force_all: 是否强制启动所有自动启动服务（忽略用户停止状态）
    async fn start_auto_services_internal(&self, force_all: bool) -> Result<(), String> {
        println!("🔍 start_auto_services called (force_all={})", force_all);
        let configs = self.configs.lock().await;
        let user_stopped = self.user_stopped_services.lock().await;
        
        println!("🔍 User stopped services: {:?}", user_stopped.iter().collect::<Vec<_>>());
        
        let auto_start_services: Vec<String> = configs
            .values()
            .filter(|config| {
                let should_start = config.auto_start && (force_all || !user_stopped.contains(&config.name));
                println!("🔍 Service '{}': auto_start={}, user_stopped={}, force_all={}, should_start={}", 
                    config.name, config.auto_start, user_stopped.contains(&config.name), force_all, should_start);
                should_start
            })
            .map(|config| config.name.clone())
            .collect();
        
        drop(configs); // 释放锁
        drop(user_stopped); // 释放锁
        
        for service_name in auto_start_services {
            if let Err(e) = self.start_service(&service_name).await {
                eprintln!("Failed to auto-start service '{}': {}", service_name, e);
            } else {
                println!("✅ Auto-started service '{}'", service_name);
            }
        }
        
        Ok(())
    }
    
    /// 强制启动所有自动启动服务（忽略用户停止状态）
    pub async fn force_start_auto_services(&self) -> Result<(), String> {
        self.start_auto_services_internal(true).await
    }
    
    /// 停止所有服务
    pub async fn stop_all_services(&self) -> Result<(), String> {
        let service_names: Vec<String> = {
            let services = self.services.lock().await;
            services.keys().cloned().collect()
        };
        
        for service_name in service_names {
            // 系统停止所有服务时，不标记为用户停止
            self.stop_service_internal(&service_name, false).await.ok(); // 忽略错误
        }
        
        Ok(())
    }
    
    /// 添加新的服务配置
    pub async fn add_service_config(&self, config: ServiceConfig) {
        let mut configs = self.configs.lock().await;
        configs.insert(config.name.clone(), config);
    }
    
    /// 移除服务配置
    pub async fn remove_service_config(&self, service_name: &str) -> Result<(), String> {
        // 先停止服务（系统停止）
        self.stop_service_internal(service_name, false).await.ok();
        
        // 从用户停止列表中移除
        self.user_stopped_services.lock().await.remove(service_name);
        
        // 移除配置
        let mut configs = self.configs.lock().await;
        configs.remove(service_name)
            .ok_or_else(|| format!("Service '{}' not found", service_name))?;
        
        Ok(())
    }
    
    /// 清除用户停止状态（用于测试或重置）
    pub async fn clear_user_stopped_status(&self, service_name: &str) {
        self.user_stopped_services.lock().await.remove(service_name);
        println!("🔄 Cleared user-stopped status for service '{}'", service_name);
    }
    
    /// 获取用户停止的服务列表（用于调试）
    pub async fn get_user_stopped_services(&self) -> Vec<String> {
        self.user_stopped_services.lock().await.iter().cloned().collect()
    }
}

impl Clone for MultiSidecarManager {
    fn clone(&self) -> Self {
        Self {
            services: Arc::clone(&self.services),
            configs: Arc::clone(&self.configs),
            app_handle: self.app_handle.clone(),
            user_stopped_services: Arc::clone(&self.user_stopped_services),
        }
    }
}
