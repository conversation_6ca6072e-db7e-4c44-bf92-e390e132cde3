use std::collections::HashMap;
use std::process::Stdio;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};

use serde::{Deserialize, Serialize};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::process::{Child, ChildStdin};
use tokio::sync::{Mutex, oneshot};
use tokio::time::timeout;
use tokio_util::sync::CancellationToken;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SidecarMessage {
    #[serde(rename = "type")]
    pub message_type: String,
    pub source: String,
    pub command_id: Option<String>,
    pub data: Option<serde_json::Value>,
    pub timestamp: f64,
    pub success: bool,
    pub error: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SidecarCommand {
    pub id: String,
    pub action: String,
    pub params: Option<serde_json::Value>,
    pub timestamp: f64,
}

impl SidecarCommand {
    pub fn new(action: String, params: Option<serde_json::Value>) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            action,
            params,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs_f64(),
        }
    }
}

/// Sidecar 管理器
///
/// 设计说明：
/// - 使用组合而非继承的方式管理状态
/// - 通过方法提供通用的重复启动检查
/// - 支持动态脚本路径，可管理不同的 Python 服务
/// - 支持条件编译：开发模式使用 uv run，生产模式使用预构建的包装脚本
/// - 可以轻松扩展为 trait 以支持多种 Sidecar 类型
#[derive(Debug)]
pub struct SidecarManager {
    child: Arc<Mutex<Option<Child>>>,
    stdin: Arc<Mutex<Option<ChildStdin>>>,
    app_handle: AppHandle,
    pending_commands: Arc<Mutex<HashMap<String, oneshot::Sender<SidecarMessage>>>>,
    script_path: Arc<Mutex<Option<String>>>,  // 动态脚本路径
    heartbeat_cancel_token: Arc<Mutex<Option<CancellationToken>>>,  // 心跳任务取消令牌
}

impl SidecarManager {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            child: Arc::new(Mutex::new(None)),
            stdin: Arc::new(Mutex::new(None)),
            app_handle,
            pending_commands: Arc::new(Mutex::new(HashMap::new())),
            script_path: Arc::new(Mutex::new(None)),
            heartbeat_cancel_token: Arc::new(Mutex::new(None)),
        }
    }

    /// 设置要启动的 Python 脚本路径
    pub async fn set_script_path(&mut self, path: &str) {
        *self.script_path.lock().await = Some(path.to_string());
    }

    pub async fn is_running(&self) -> bool {
        self.child.lock().await.is_some()
    }

    pub async fn start(&self) -> Result<(), String> {
        // 检查是否已经在运行
        if self.is_running().await {
            return Ok(());
        }

        // 停止现有进程（如果有的话）
        self.stop().await;

        // 根据编译模式选择启动方式
        #[cfg(debug_assertions)]
        {
            // 开发模式：使用 Command 方式，支持热更新
            self.start_dev_mode().await
        }
        
        #[cfg(not(debug_assertions))]
        {
            // 生产模式：使用真正的 Sidecar
            self.start_sidecar_mode().await
        }
    }

    /// 开发模式启动 - 使用 Command 方式
    #[cfg(debug_assertions)]
    async fn start_dev_mode(&self) -> Result<(), String> {
        eprintln!("🔧 Starting in DEVELOPMENT mode using Command");
        
        // 获取项目根目录（从 src-tauri 向上一级）
        let project_root = std::env::current_dir()
            .unwrap()
            .parent()
            .unwrap_or_else(|| std::path::Path::new("."))
            .to_path_buf();

        // 获取动态脚本路径
        let script_relative_path = {
            let script_path_guard = self.script_path.lock().await;
            script_path_guard.clone()
                .unwrap_or_else(|| "src-python/test_services/counter_service.py".to_string())
        };

        let script_path = project_root.join(script_relative_path);

        eprintln!("🐍 Starting Python script: {:?}", script_path);
        eprintln!("📂 Working directory: {:?}", project_root);

        let mut child = tokio::process::Command::new("uv")
            .arg("run")
            .arg("python")
            .arg(script_path)
            .current_dir(project_root)
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|e| format!("Failed to start development sidecar: {}", e))?;

        self.setup_child_process(child).await
    }

    /// 生产模式启动 - 使用包装的可执行脚本
    #[cfg(not(debug_assertions))]
    async fn start_sidecar_mode(&self) -> Result<(), String> {
        eprintln!("🚀 Starting in PRODUCTION mode using packaged executable");

        let script_relative_path = {
            let script_path_guard = self.script_path.lock().await;
            script_path_guard.clone()
                .unwrap_or_else(|| "src-python/test_services/counter_service.py".to_string())
        };

        let wrapper_script_name = self.get_sidecar_binary_name(&script_relative_path);
        eprintln!("📦 Starting wrapper script: {}", wrapper_script_name);

        // --- 核心修复开始：获取可靠的资源目录路径 ---
        let resources_dir = self.app_handle.path_resolver()
            .resource_dir()
            .ok_or("Failed to resolve resource directory. The app bundle might be corrupted.")?;
        
        eprintln!("📂 Resolved resources directory: {:?}", resources_dir);

        // 确定 sidecar 二进制文件的绝对路径
        // 在 macOS 中，sidecar 二进制文件与主可执行文件位于不同的目录。
        // 主可执行文件: Contents/MacOS/app_name
        // Sidecar (资源): Contents/Resources/sidecar_name
        let sidecar_path = resources_dir.join(&wrapper_script_name);
        eprintln!("🔍 Looking for sidecar binary at: {:?}", sidecar_path);

        if !sidecar_path.exists() {
            // 提供更详细的调试信息
            let resources_contents: Vec<String> = std::fs::read_dir(&resources_dir)
                .map(|entries| entries.filter_map(Result::ok).map(|e| e.file_name().to_string_lossy().into_owned()).collect())
                .unwrap_or_else(|err| vec![format!("Error reading resources dir: {}", err)]);
            return Err(format!(
                "Sidecar binary not found at {:?}. Contents of resources dir: {:?}",
                sidecar_path, resources_contents
            ));
        }
        
        eprintln!("✅ Sidecar binary found. Preparing to launch...");

        // 启动子进程，并进行关键配置
        let mut child = tokio::process::Command::new(&sidecar_path)
            // 关键修复 A: 将工作目录设置为资源目录
            // 这使得 Python 脚本中的相对路径（如 "src-python/..."）可以被正确解析
            .current_dir(&resources_dir)
            // 关键修复 B: 注入环境变量，将资源目录路径传递给 Python
            // 这使得 Python 脚本可以绝对可靠地定位其依赖项
            .env("APP_RESOURCE_DIR", &resources_dir)
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|e| format!("Failed to start production sidecar '{}': {}", wrapper_script_name, e))?;

        // --- 核心修复结束 ---

        eprintln!("🎉 Sidecar process started successfully in production mode.");
        eprintln!("   - Working Directory: {:?}", resources_dir);
        eprintln!("   - Environment: APP_RESOURCE_DIR={:?}", resources_dir);

        self.setup_child_process(child).await
    }

    /// 根据脚本路径获取对应的 sidecar 二进制名称
    #[cfg(not(debug_assertions))]
    fn get_sidecar_binary_name(&self, script_path: &str) -> String {
        // 注意：这里返回的是在生产模式下使用的二进制包装脚本名称
        match script_path {
            path if path.contains("futu_adapter") => "futu-adapter".to_string(),
            path if path.contains("huasheng_adapter") => "huasheng-adapter".to_string(),
            path if path.contains("counter_service") => "counter-service".to_string(),
            _ => "counter-service".to_string(), // 默认使用 counter-service
        }
    }

    /// 通用的子进程设置逻辑
    async fn setup_child_process(&self, mut child: tokio::process::Child) -> Result<(), String> {
        // 获取 stdin, stdout, stderr
        let stdin = child.stdin.take().ok_or("Failed to get stdin")?;
        let stdout = child.stdout.take().ok_or("Failed to get stdout")?;
        let stderr = child.stderr.take().ok_or("Failed to get stderr")?;

        // 存储 stdin 引用
        *self.stdin.lock().await = Some(stdin);

        // 启动 stdout 监听器
        let app_handle = self.app_handle.clone();
        let pending_commands = self.pending_commands.clone();
        
        tokio::spawn(async move {
            let reader = BufReader::new(stdout);
            let mut lines = reader.lines();

            while let Ok(Some(line)) = lines.next_line().await {
                if let Ok(message) = serde_json::from_str::<SidecarMessage>(&line) {
                    match message.message_type.as_str() {
                        "push" => {
                            // 实时数据推送到前端
                            if let Err(e) = app_handle.emit_all("sidecar-message", &message) {
                                eprintln!("Failed to emit sidecar-message: {}", e);
                            }
                        }
                        "response" => {
                            // 命令响应处理
                            if let Some(command_id) = &message.command_id {
                                let mut pending = pending_commands.lock().await;
                                if let Some(sender) = pending.remove(command_id) {
                                    let _ = sender.send(message);
                                }
                            }
                        }
                        _ => {
                            eprintln!("Unknown message type: {}", message.message_type);
                        }
                    }
                } else {
                    eprintln!("Failed to parse message: {}", line);
                }
            }
        });

        // 启动 stderr 监听器
        tokio::spawn(async move {
            let reader = BufReader::new(stderr);
            let mut lines = reader.lines();

            while let Ok(Some(line)) = lines.next_line().await {
                eprintln!("[Sidecar]: {}", line);
            }
        });

        // 存储子进程引用
        *self.child.lock().await = Some(child);

        // 创建心跳检查任务的取消令牌
        let cancel_token = CancellationToken::new();
        *self.heartbeat_cancel_token.lock().await = Some(cancel_token.clone());

        // 发送心跳检查
        tokio::spawn({
            let manager = self.clone();
            async move {
                loop {
                    tokio::select! {
                        _ = cancel_token.cancelled() => {
                            eprintln!("Heartbeat task cancelled for service");
                            break;
                        }
                        _ = tokio::time::sleep(Duration::from_secs(30)) => {
                            if let Err(e) = manager.ping().await {
                                eprintln!("Sidecar ping failed: {}", e);
                                // 注意：这里不实现自动重启，避免意外重启
                            }
                        }
                    }
                }
            }
        });

        Ok(())
    }

    pub async fn stop(&self) {
        // 首先取消心跳任务
        if let Some(cancel_token) = self.heartbeat_cancel_token.lock().await.take() {
            cancel_token.cancel();
            eprintln!("Heartbeat task cancelled");
        }

        // 尝试优雅地停止服务
        if self.is_running().await {
            eprintln!("Sending stop command to sidecar...");
            if let Err(e) = self.send_command_with_response("stop".to_string(), None).await {
                eprintln!("Failed to send stop command: {}", e);
            } else {
                eprintln!("Stop command sent, waiting for graceful shutdown...");
                // 等待一段时间让服务优雅关闭
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        }

        // 然后强制停止子进程（如果还在运行）
        if let Some(mut child) = self.child.lock().await.take() {
            let _ = child.kill().await;
            eprintln!("Sidecar process killed");
        }
        *self.stdin.lock().await = None;
    }

    pub async fn send_command_with_response(
        &self,
        action: String,
        params: Option<serde_json::Value>,
    ) -> Result<SidecarMessage, String> {
        let command = SidecarCommand::new(action, params);
        let command_id = command.id.clone();

        // 创建响应接收器
        let (tx, rx) = oneshot::channel();
        {
            let mut pending = self.pending_commands.lock().await;
            pending.insert(command_id.clone(), tx);
        }

        // 发送命令
        self.send_command(command).await?;

        // 等待响应（带超时）
        match timeout(Duration::from_secs(10), rx).await {
            Ok(Ok(response)) => Ok(response),
            Ok(Err(_)) => Err("Response channel closed".to_string()),
            Err(_) => {
                // 清理超时的命令
                self.pending_commands.lock().await.remove(&command_id);
                Err("Command timeout".to_string())
            }
        }
    }

    async fn send_command(&self, command: SidecarCommand) -> Result<(), String> {
        let command_json = serde_json::to_string(&command)
            .map_err(|e| format!("Serialize error: {}", e))?;

        let mut stdin_guard = self.stdin.lock().await;
        if let Some(stdin) = stdin_guard.as_mut() {
            stdin
                .write_all(format!("{}\n", command_json).as_bytes())
                .await
                .map_err(|e| format!("Write error: {}", e))?;

            stdin
                .flush()
                .await
                .map_err(|e| format!("Flush error: {}", e))?;

            Ok(())
        } else {
            Err("Sidecar not running".to_string())
        }
    }

    pub async fn ping(&self) -> Result<(), String> {
        let response = self
            .send_command_with_response("ping".to_string(), None)
            .await?;

        if response.success {
            Ok(())
        } else {
            Err(response.error.unwrap_or("Ping failed".to_string()))
        }
    }
}

// 实现 Clone trait 以支持在异步任务中使用
impl Clone for SidecarManager {
    fn clone(&self) -> Self {
        Self {
            child: self.child.clone(),
            stdin: self.stdin.clone(),
            app_handle: self.app_handle.clone(),
            pending_commands: self.pending_commands.clone(),
            script_path: self.script_path.clone(),
            heartbeat_cancel_token: self.heartbeat_cancel_token.clone(),
        }
    }
}
