import { StrategyTemplate, RiskConditionTemplate, LiquidationTemplate, SellStrategyTemplate, SellConditionTemplate, SellExecutionTemplate } from '../types';
import { brokerManager } from '../services/brokerManager';

// 获取动态经纪商选项
const getBrokerOptions = () => brokerManager.getBrokerOptions();

// 策略模板定义
export const STRATEGY_TEMPLATES: StrategyTemplate[] = [
    {
        id: 'strategy_a_big_order_monitor',
        name: '策略A: 大单监控',
        description: '监控指定经纪商的大额买盘行为，当满足条件时自动买入',
        configSchema: [
            {
                name: 'monitorThreshold',
                label: '监控买盘阈值(股)',
                type: 'number',
                required: true,
                defaultValue: 10000,
                placeholder: '输入买盘阈值数量',
                validation: { min: 1000, max: 1000000 },
                helpText: '当买盘数量超过此阈值时触发监控'
            },
            {
                name: 'priceCondition',
                label: '价格条件',
                type: 'number',
                required: false,
                defaultValue: null,
                placeholder: '输入价格条件（可选）',
                validation: { min: 0.001, step: 0.001 },
                helpText: '可选：设置价格条件，留空则不限制价格'
            },
            {
                name: 'durationSeconds',
                label: '持续时间(秒)',
                type: 'number',
                required: false,
                defaultValue: 0,
                placeholder: '输入持续时间（0表示不限制）',
                validation: { min: 0, max: 300 },
                helpText: '大单必须持续存在的最短时间，可选'
            },
            {
                name: 'targetBrokers',
                label: '目标经纪商列表',
                type: 'multiselect',
                required: true,
                placeholder: '请选择要监控的经纪商',
                options: getBrokerOptions(),
                defaultValue: [],
                helpText: '选择要监控的经纪商'
            },
            {
                name: 'orderSize',
                label: '单笔买入股数',
                type: 'number',
                required: true,
                defaultValue: 1000,
                placeholder: '输入单笔买入数量',
                validation: { min: 100, max: 100000 },
                helpText: '触发条件时的单笔买入数量'
            }
        ],
        defaultConfig: {
            monitorThreshold: 10000,
            priceCondition: null,
            durationSeconds: 0,
            targetBrokers: [],
            orderSize: 1000
        }
    },
    {
        id: 'strategy_b_breakout_chase',
        name: '策略B: 突破追涨',
        description: '监控价格突破并伴随放量，在回踩确认后追涨买入',
        configSchema: [
            {
                name: 'breakoutPeriod',
                label: '突破周期(日)',
                type: 'number',
                required: true,
                defaultValue: 20,
                placeholder: '输入突破周期天数',
                validation: { min: 5, max: 60 },
                helpText: '计算突破的历史周期天数'
            },
            {
                name: 'volumeMultiplier',
                label: '成交量放大倍数',
                type: 'number',
                required: true,
                defaultValue: 2.0,
                placeholder: '输入成交量放大倍数',
                validation: { min: 1.2, max: 5.0 },
                helpText: '突破时成交量需放大的倍数'
            },
            {
                name: 'pullbackPercent',
                label: '回踩幅度(%)',
                type: 'number',
                required: true,
                defaultValue: 3.0,
                placeholder: '输入回踩幅度百分比',
                validation: { min: 1.0, max: 10.0 },
                helpText: '突破后允许回踩的最大幅度'
            },
            {
                name: 'confirmationMinutes',
                label: '确认时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 5,
                placeholder: '输入确认时间',
                validation: { min: 1, max: 30 },
                helpText: '突破确认的最短持续时间'
            }
        ],
        defaultConfig: {
            breakoutPeriod: 20,
            volumeMultiplier: 2.0,
            pullbackPercent: 3.0,
            confirmationMinutes: 5
        }
    },
    {
        id: 'strategy_c_volume_surge',
        name: '策略C: 异常放量',
        description: '监控异常放量信号，结合价格行为判断买入时机',
        configSchema: [
            {
                name: 'volumeRatio',
                label: '放量倍数',
                type: 'number',
                required: true,
                defaultValue: 3.0,
                placeholder: '输入放量倍数',
                validation: { min: 1.5, max: 10.0 },
                helpText: '相对平均成交量的放大倍数'
            },
            {
                name: 'priceChangeMin',
                label: '最小价格变动(%)',
                type: 'number',
                required: true,
                defaultValue: 2.0,
                placeholder: '输入最小价格变动百分比',
                validation: { min: 0.5, max: 10.0 },
                helpText: '伴随放量的最小价格上涨幅度'
            },
            {
                name: 'monitorWindow',
                label: '监控窗口(分钟)',
                type: 'number',
                required: true,
                defaultValue: 15,
                placeholder: '输入监控窗口时间',
                validation: { min: 5, max: 60 },
                helpText: '放量监控的时间窗口'
            }
        ],
        defaultConfig: {
            volumeRatio: 3.0,
            priceChangeMin: 2.0,
            monitorWindow: 15
        }
    },
    {
        id: 'strategy_d_big_order_follow',
        name: '策略D: 大单跟随',
        description: '监控指定经纪商的超大单行为，触发后自动跟随买入，并根据后续大单活动决定卖出时机',
        configSchema: [
            {
                name: 'buyTriggerThreshold',
                label: '买入触发阈值(股)',
                type: 'number',
                required: true,
                defaultValue: 6000000,
                placeholder: '输入买入触发阈值',
                validation: { min: 1000000, max: 50000000 },
                helpText: '当单笔成交量达到此阈值时触发买入'
            },
            {
                name: 'targetBrokers',
                label: '目标经纪商列表',
                type: 'multiselect',
                required: true,
                placeholder: '请选择要监控的经纪商',
                options: getBrokerOptions(),
                defaultValue: [],
                helpText: '选择要监控的经纪商'
            },
            {
                name: 'priceCondition',
                label: '价格条件',
                type: 'number',
                required: false,
                defaultValue: null,
                placeholder: '输入价格条件（可选）',
                validation: { min: 0.001, step: 0.001 },
                helpText: '可选：设置价格条件，留空则不限制价格'
            },
            {
                name: 'orderSize',
                label: '单笔买入股数',
                type: 'number',
                required: true,
                defaultValue: 1000,
                placeholder: '输入单笔买入数量',
                validation: { min: 100, max: 10000000 },
                helpText: '触发条件时的单笔买入数量'
            },
            {
                name: 'requiresSellStrategy',
                label: '需要卖出策略',
                type: 'checkbox',
                required: false,
                defaultValue: true,
                helpText: '大单跟随策略必须配置卖出策略'
            }
        ],
        defaultConfig: {
            buyTriggerThreshold: 6000000,
            targetBrokers: [],
            priceCondition: null,
            orderSize: 1000,
            requiresSellStrategy: true
        }
    }
];

// 风控条件模板定义
export const RISK_CONDITION_TEMPLATES: RiskConditionTemplate[] = [
    {
        id: 'fast_stop_loss',
        name: '快速止损',
        description: 'T0专用：当持仓亏损达到设定比例时立即触发清仓',
        type: 'fast_stop_loss',
        configFields: [
            {
                name: 'lossPercentage',
                label: '止损比例(%)',
                type: 'number',
                required: true,
                defaultValue: 3.0,
                placeholder: '输入止损比例',
                validation: { min: 1.0, max: 10.0, step: 0.1 },
                helpText: '基于买入价格的亏损比例，建议2-5%'
            },
            {
                name: 'basedOnBuyPrice',
                label: '计算基准',
                type: 'select',
                required: true,
                options: [
                    { label: '基于买入价', value: true },
                    { label: '基于当前成本', value: false }
                ],
                defaultValue: true
            },
            {
                name: 'responseTimeMs',
                label: '响应时间要求(毫秒)',
                type: 'number',
                required: true,
                defaultValue: 1000,
                placeholder: '输入响应时间',
                validation: { min: 500, max: 5000 },
                helpText: '触发后的响应时间要求，建议1000ms'
            }
        ],
        defaultParams: {
            lossPercentage: 3.0,
            basedOnBuyPrice: true,
            responseTimeMs: 1000
        }
    },
    {
        id: 'fast_take_profit',
        name: '快速止盈',
        description: 'T0专用：当持仓盈利达到目标时及时止盈锁定收益',
        type: 'fast_take_profit',
        configFields: [
            {
                name: 'profitPercentage',
                label: '止盈比例(%)',
                type: 'number',
                required: true,
                defaultValue: 2.0,
                placeholder: '输入止盈比例',
                validation: { min: 0.5, max: 20.0, step: 0.1 },
                helpText: '基于买入价格的盈利比例，建议1-5%'
            },
            {
                name: 'basedOnBuyPrice',
                label: '计算基准',
                type: 'select',
                required: true,
                options: [
                    { label: '基于买入价', value: true },
                    { label: '基于当前成本', value: false }
                ],
                defaultValue: true
            },
            {
                name: 'enableTrailing',
                label: '启用移动止盈',
                type: 'select',
                required: false,
                options: [
                    { label: '启用', value: true },
                    { label: '禁用', value: false }
                ],
                defaultValue: false,
                helpText: '启用后将跟踪最高价进行移动止盈'
            },
            {
                name: 'trailingPercentage',
                label: '移动止盈回撤比例(%)',
                type: 'number',
                required: false,
                defaultValue: 1.0,
                placeholder: '输入回撤比例',
                validation: { min: 0.2, max: 3.0, step: 0.1 },
                helpText: '从最高点回撤的比例，仅在启用移动止盈时有效'
            }
        ],
        defaultParams: {
            profitPercentage: 2.0,
            basedOnBuyPrice: true,
            enableTrailing: false,
            trailingPercentage: 1.0
        }
    },
    {
        id: 'time_limit',
        name: '时间限制',
        description: 'T0专用：设置最大持仓时间，避免长时间持仓风险',
        type: 'time_limit',
        configFields: [
            {
                name: 'maxHoldingMinutes',
                label: '最大持仓时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 60,
                placeholder: '输入最大持仓时间',
                validation: { min: 5, max: 240 },
                helpText: 'T0交易建议30-120分钟'
            },
            {
                name: 'warningMinutesBefore',
                label: '提前预警时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 10,
                placeholder: '输入预警时间',
                validation: { min: 1, max: 30 },
                helpText: '到期前多久开始预警提示'
            },
            {
                name: 'forceExecution',
                label: '强制执行',
                type: 'select',
                required: true,
                options: [
                    { label: '强制清仓', value: true },
                    { label: '仅警告', value: false }
                ],
                defaultValue: true,
                helpText: '到期后是否强制清仓'
            }
        ],
        defaultParams: {
            maxHoldingMinutes: 60,
            warningMinutesBefore: 10,
            forceExecution: true
        }
    },
    {
        id: 'price_deviation',
        name: '价格偏离度',
        description: 'T0专用：监控股价相对买入价的偏离程度',
        type: 'price_deviation',
        configFields: [
            {
                name: 'upwardDeviationPercent',
                label: '向上偏离阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 5.0,
                placeholder: '输入向上偏离阈值',
                validation: { min: 1.0, max: 15.0, step: 0.1 },
                helpText: '价格向上偏离买入价的百分比阈值'
            },
            {
                name: 'downwardDeviationPercent',
                label: '向下偏离阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 3.0,
                placeholder: '输入向下偏离阈值',
                validation: { min: 1.0, max: 10.0, step: 0.1 },
                helpText: '价格向下偏离买入价的百分比阈值'
            },
            {
                name: 'deviationMethod',
                label: '计算方式',
                type: 'select',
                required: true,
                options: [
                    { label: '百分比', value: 'percentage' },
                    { label: 'Tick数', value: 'ticks' }
                ],
                defaultValue: 'percentage'
            },
            {
                name: 'tickSize',
                label: 'Tick大小',
                type: 'number',
                required: false,
                defaultValue: 0.001,
                placeholder: '输入tick大小',
                validation: { min: 0.001, max: 1.0, step: 0.001 },
                helpText: '仅在选择Tick数方式时需要设置'
            }
        ],
        defaultParams: {
            upwardDeviationPercent: 5.0,
            downwardDeviationPercent: 3.0,
            deviationMethod: 'percentage',
            tickSize: 0.001
        }
    },
    {
        id: 'volume_shrinkage',
        name: '成交量萎缩',
        description: 'T0专用：监控成交量萎缩情况，防范流动性风险',
        type: 'volume_shrinkage',
        configFields: [
            {
                name: 'timeWindowMinutes',
                label: '监控时间窗口(分钟)',
                type: 'number',
                required: true,
                defaultValue: 10,
                placeholder: '输入监控时间窗口',
                validation: { min: 1, max: 30 },
                helpText: '计算平均成交量的时间窗口'
            },
            {
                name: 'shrinkageRatio',
                label: '萎缩比例阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 50.0,
                placeholder: '输入萎缩比例阈值',
                validation: { min: 20.0, max: 80.0, step: 5.0 },
                helpText: '成交量低于正常水平的百分比'
            },
            {
                name: 'comparisonPeriod',
                label: '比较基准',
                type: 'select',
                required: true,
                options: [
                    { label: '当日平均', value: 'session_avg' },
                    { label: '近期平均', value: 'recent_avg' }
                ],
                defaultValue: 'session_avg'
            },
            {
                name: 'minimumVolume',
                label: '最小成交量要求',
                type: 'number',
                required: true,
                defaultValue: 10000,
                placeholder: '输入最小成交量',
                validation: { min: 1000, max: 1000000 },
                helpText: '触发条件的最小成交量门槛'
            }
        ],
        defaultParams: {
            timeWindowMinutes: 10,
            shrinkageRatio: 50.0,
            comparisonPeriod: 'session_avg',
            minimumVolume: 10000
        }
    },
    {
        id: 'volatility',
        name: '市场波动率',
        description: 'T0专用：监控短期价格波动率异常情况',
        type: 'volatility',
        configFields: [
            {
                name: 'calculationPeriodMinutes',
                label: '计算周期(分钟)',
                type: 'number',
                required: true,
                defaultValue: 10,
                placeholder: '输入计算周期',
                validation: { min: 5, max: 30 },
                helpText: '波动率计算的时间周期'
            },
            {
                name: 'volatilityMethod',
                label: '计算方法',
                type: 'select',
                required: true,
                options: [
                    { label: '标准差', value: 'std_dev' },
                    { label: 'ATR', value: 'atr' }
                ],
                defaultValue: 'std_dev'
            },
            {
                name: 'threshold',
                label: '波动率阈值',
                type: 'number',
                required: true,
                defaultValue: 2.0,
                placeholder: '输入波动率阈值',
                validation: { min: 1.0, max: 5.0, step: 0.1 },
                helpText: '触发清仓的波动率倍数'
            },
            {
                name: 'triggerDirection',
                label: '触发方向',
                type: 'select',
                required: true,
                options: [
                    { label: '波动率过高', value: 'high' },
                    { label: '波动率过低', value: 'low' },
                    { label: '双向监控', value: 'both' }
                ],
                defaultValue: 'high'
            }
        ],
        defaultParams: {
            calculationPeriodMinutes: 10,
            volatilityMethod: 'std_dev',
            threshold: 2.0,
            triggerDirection: 'high'
        }
    },
    {
        id: 'orderbook_anomaly',
        name: '盘口异常',
        description: 'T0专用：监控买卖盘口异常变化',
        type: 'orderbook_anomaly',
        configFields: [
            {
                name: 'spreadThresholdPercent',
                label: '价差异常阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 0.5,
                placeholder: '输入价差阈值',
                validation: { min: 0.1, max: 2.0, step: 0.1 },
                helpText: '买卖价差异常扩大的百分比阈值'
            },
            {
                name: 'depthThresholdPercent',
                label: '深度不足阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 30.0,
                placeholder: '输入深度阈值',
                validation: { min: 10.0, max: 70.0, step: 5.0 },
                helpText: '盘口深度不足正常水平的百分比'
            },
            {
                name: 'levelCount',
                label: '监控档数',
                type: 'number',
                required: true,
                defaultValue: 5,
                placeholder: '输入监控档数',
                validation: { min: 3, max: 10 },
                helpText: '监控买卖盘的档数'
            },
            {
                name: 'anomalyDurationSeconds',
                label: '异常持续时间(秒)',
                type: 'number',
                required: true,
                defaultValue: 30,
                placeholder: '输入持续时间',
                validation: { min: 10, max: 120 },
                helpText: '异常状态持续多久后触发'
            }
        ],
        defaultParams: {
            spreadThresholdPercent: 0.5,
            depthThresholdPercent: 30.0,
            levelCount: 5,
            anomalyDurationSeconds: 30
        }
    },
    {
        id: 'trailing_stop',
        name: '追踪止损',
        description: 'T0专用：跟踪最高价设置动态止损点',
        type: 'trailing_stop',
        configFields: [
            {
                name: 'trailingPercent',
                label: '追踪回撤比例(%)',
                type: 'number',
                required: true,
                defaultValue: 1.5,
                placeholder: '输入回撤比例',
                validation: { min: 0.5, max: 5.0, step: 0.1 },
                helpText: '从最高价回撤的百分比'
            },
            {
                name: 'activationProfitPercent',
                label: '激活盈利比例(%)',
                type: 'number',
                required: true,
                defaultValue: 1.0,
                placeholder: '输入激活比例',
                validation: { min: 0.5, max: 5.0, step: 0.1 },
                helpText: '达到多少盈利后激活追踪止损'
            },
            {
                name: 'updateFrequencyMs',
                label: '更新频率(毫秒)',
                type: 'number',
                required: true,
                defaultValue: 1000,
                placeholder: '输入更新频率',
                validation: { min: 500, max: 5000 },
                helpText: '最高价更新的频率'
            }
        ],
        defaultParams: {
            trailingPercent: 1.5,
            activationProfitPercent: 1.0,
            updateFrequencyMs: 1000
        }
    },
    {
        id: 'time_based_risk',
        name: '分时段风控',
        description: 'T0专用：在不同时段应用不同的风控策略',
        type: 'time_based_risk',
        configFields: [
            {
                name: 'autoForceCloseBefore',
                label: '强制清仓时间',
                type: 'text',
                required: true,
                defaultValue: '15:30',
                placeholder: '如：15:30',
                validation: { pattern: '^\\d{2}:\\d{2}$' },
                helpText: '每日强制清仓的时间点'
            },
            {
                name: 'openingPeriodMinutes',
                label: '开盘敏感期(分钟)',
                type: 'number',
                required: true,
                defaultValue: 30,
                placeholder: '输入开盘敏感期',
                validation: { min: 10, max: 60 },
                helpText: '开盘后多长时间内使用保守策略'
            },
            {
                name: 'closingPeriodMinutes',
                label: '收盘敏感期(分钟)',
                type: 'number',
                required: true,
                defaultValue: 30,
                placeholder: '输入收盘敏感期',
                validation: { min: 10, max: 60 },
                helpText: '收盘前多长时间开始收紧风控'
            }
        ],
        defaultParams: {
            autoForceCloseBefore: '15:30',
            openingPeriodMinutes: 30,
            closingPeriodMinutes: 30,
            timeRules: []
        }
    },
    {
        id: 'consecutive_loss_circuit_breaker',
        name: '连续亏损熔断',
        description: 'T0专用：连续亏损达到次数后暂停交易',
        type: 'consecutive_loss_circuit_breaker',
        configFields: [
            {
                name: 'maxConsecutiveLosses',
                label: '最大连续亏损次数',
                type: 'number',
                required: true,
                defaultValue: 3,
                placeholder: '输入最大次数',
                validation: { min: 2, max: 10 },
                helpText: '连续亏损多少次后触发熔断'
            },
            {
                name: 'lossThresholdPercent',
                label: '亏损认定阈值(%)',
                type: 'number',
                required: true,
                defaultValue: 1.0,
                placeholder: '输入亏损阈值',
                validation: { min: 0.5, max: 5.0, step: 0.1 },
                helpText: '单次亏损超过多少认定为一次亏损'
            },
            {
                name: 'circuitBreakerDurationMinutes',
                label: '熔断持续时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 30,
                placeholder: '输入熔断时间',
                validation: { min: 10, max: 120 },
                helpText: '熔断后暂停交易的时间'
            },
            {
                name: 'autoResetEnabled',
                label: '自动解除熔断',
                type: 'select',
                required: true,
                options: [
                    { label: '自动解除', value: true },
                    { label: '手动解除', value: false }
                ],
                defaultValue: true,
                helpText: '是否在熔断时间结束后自动解除'
            }
        ],
        defaultParams: {
            maxConsecutiveLosses: 3,
            lossThresholdPercent: 1.0,
            circuitBreakerDurationMinutes: 30,
            autoResetEnabled: true,
            currentLossStreak: 0
        }
    }
];

// 清仓策略模板定义
export const LIQUIDATION_TEMPLATES: LiquidationTemplate[] = [
    {
        id: 'immediate_market',
        name: '即时市价单 (T0专用)',
        description: '超快速清仓，保证即时成交，适合紧急风控场景，响应时间<1秒',
        type: 'immediate_market',
        configFields: [
            {
                name: 'maxSlippageBps',
                label: '最大滑点限制(基点)',
                type: 'number',
                required: false,
                defaultValue: 20,
                placeholder: '输入最大滑点',
                validation: { min: 5, max: 100 },
                helpText: '允许的最大滑点，超过则报警(1基点=0.01%)'
            }
        ],
        defaultParams: {
            maxSlippageBps: 20
        }
    },
    {
        id: 'smart_execution',
        name: '智能限价单 (T0推荐)',
        description: '平衡速度与成本，智能选择最优价格，响应时间<2秒',
        type: 'smart_execution',
        configFields: [
            {
                name: 'aggressiveness',
                label: '激进程度',
                type: 'select',
                required: true,
                options: [
                    { label: '保守 (更少滑点)', value: 'low' },
                    { label: '平衡 (推荐)', value: 'medium' },
                    { label: '激进 (更快成交)', value: 'high' }
                ],
                defaultValue: 'medium'
            },
            {
                name: 'maxSlippageBps',
                label: '最大滑点容忍(基点)',
                type: 'number',
                required: true,
                defaultValue: 15,
                placeholder: '输入最大滑点',
                validation: { min: 5, max: 50 },
                helpText: '超过此滑点将转为市价单'
            },
            {
                name: 'timeoutSeconds',
                label: '超时时间(秒)',
                type: 'number',
                required: true,
                defaultValue: 10,
                validation: { min: 3, max: 30 },
                helpText: '未完全成交的超时时间'
            },
            {
                name: 'fallbackToMarket',
                label: '超时后处理',
                type: 'select',
                required: true,
                options: [
                    { label: '转为市价单', value: true },
                    { label: '仅撤单', value: false }
                ],
                defaultValue: true
            }
        ],
        defaultParams: {
            aggressiveness: 'medium',
            maxSlippageBps: 15,
            timeoutSeconds: 10,
            fallbackToMarket: true
        }
    },
    {
        id: 'sliced_execution',
        name: '分片执行 (大单专用)',
        description: '大单分片执行，减少市场冲击，适合大额T0清仓',
        type: 'sliced_execution',
        configFields: [
            {
                name: 'sliceCount',
                label: '分片数量',
                type: 'number',
                required: true,
                defaultValue: 5,
                validation: { min: 2, max: 20 },
                helpText: '将大单分成几片执行'
            },
            {
                name: 'intervalSeconds',
                label: '分片间隔(秒)',
                type: 'number',
                required: true,
                defaultValue: 3,
                validation: { min: 1, max: 30 },
                helpText: '每片之间的时间间隔'
            },
            {
                name: 'adaptiveSlicing',
                label: '自适应分片',
                type: 'select',
                required: true,
                options: [
                    { label: '启用 (根据市场调整)', value: true },
                    { label: '禁用 (固定分片)', value: false }
                ],
                defaultValue: true,
                helpText: '根据市场流动性自动调整分片大小'
            },
            {
                name: 'maxExecutionMinutes',
                label: '最大执行时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 5,
                validation: { min: 1, max: 15 },
                helpText: '分片执行的最大总时间'
            }
        ],
        defaultParams: {
            sliceCount: 5,
            intervalSeconds: 3,
            adaptiveSlicing: true,
            maxExecutionMinutes: 5
        }
    },
    {
        id: 'market_order',
        name: '标准市价单',
        description: '传统市价单清仓方式，速度快但滑点较大',
        type: 'market',
        configFields: [],
        defaultParams: {}
    },
    {
        id: 'limit_optimized',
        name: '优化限价单',
        description: '传统限价单策略，适合对成本敏感的场景',
        type: 'limit_optimized',
        configFields: [
            {
                name: 'basePrice',
                label: '基础价格',
                type: 'select',
                required: true,
                options: [
                    { label: '买一价', value: 'bid1' },
                    { label: '卖一价', value: 'ask1' },
                    { label: '最新价', value: 'last' }
                ],
                defaultValue: 'ask1'
            },
            {
                name: 'direction',
                label: '价格偏移方向',
                type: 'select',
                required: true,
                options: [
                    { label: '向上', value: 'up' },
                    { label: '向下', value: 'down' }
                ],
                defaultValue: 'down'
            },
            {
                name: 'priceOffset',
                label: '价格偏移(ticks)',
                type: 'number',
                required: true,
                defaultValue: 3,
                validation: { min: 1, max: 10 },
                helpText: '相对基础价格的偏移档数'
            },
            {
                name: 'timeoutSeconds',
                label: '超时时间(秒)',
                type: 'number',
                required: true,
                defaultValue: 20,
                validation: { min: 5, max: 120 },
                helpText: '未完全成交的超时时间'
            },
            {
                name: 'timeoutAction',
                label: '超时后动作',
                type: 'select',
                required: true,
                options: [
                    { label: '撤单并转为市价单', value: 'cancel_and_market' },
                    { label: '仅撤单', value: 'cancel_only' }
                ],
                defaultValue: 'cancel_and_market'
            }
        ],
        defaultParams: {
            basePrice: 'ask1',
            direction: 'down',
            priceOffset: 3,
            timeoutSeconds: 20,
            timeoutAction: 'cancel_and_market'
        }
    }
];

// 获取策略模板
export const getStrategyTemplate = (id: string): StrategyTemplate | undefined => {
    return STRATEGY_TEMPLATES.find(template => template.id === id);
};

// 获取风控条件模板
export const getRiskConditionTemplate = (id: string): RiskConditionTemplate | undefined => {
    return RISK_CONDITION_TEMPLATES.find(template => template.id === id);
};

// 获取清仓策略模板
export const getLiquidationTemplate = (id: string): LiquidationTemplate | undefined => {
    return LIQUIDATION_TEMPLATES.find(template => template.id === id);
};

// ========================= 卖出策略模板系统 =========================

// 卖出条件模板定义
export const SELL_CONDITION_TEMPLATES: SellConditionTemplate[] = [
    {
        id: 'fixed_volume_sell',
        name: '固定成交量触发',
        description: '当监控到指定成交量的单笔交易时触发卖出',
        type: 'fixed_volume',
        configFields: [
            {
                name: 'volumeThreshold',
                label: '触发成交量阈值(股)',
                type: 'number',
                required: true,
                defaultValue: 5000000,
                placeholder: '输入成交量阈值',
                validation: { min: 100000, max: 100000000 },
                helpText: '当单笔成交量达到此阈值时触发卖出'
            },
            {
                name: 'direction',
                label: '监控方向',
                type: 'select',
                required: true,
                options: [
                    { label: '买盘', value: 'buy' },
                    { label: '卖盘', value: 'sell' },
                    { label: '双向', value: 'both' }
                ],
                defaultValue: 'sell',
                helpText: '监控买盘还是卖盘的成交量'
            },
            {
                name: 'brokerFilter',
                label: '经纪商筛选',
                type: 'multiselect',
                required: false,
                placeholder: '选择特定经纪商（可选）',
                options: getBrokerOptions(),
                defaultValue: [],
                helpText: '可选：仅监控指定经纪商的交易'
            }
        ],
        defaultParams: {
            volumeThreshold: 5000000,
            direction: 'sell',
            brokerFilter: []
        }
    },
    {
        id: 'dynamic_percentage_sell',
        name: '动态百分比触发',
        description: '基于买入触发单量的百分比来设置卖出触发条件',
        type: 'dynamic_percentage',
        configFields: [
            {
                name: 'percentage',
                label: '触发百分比(%)',
                type: 'number',
                required: true,
                defaultValue: 80,
                placeholder: '输入百分比',
                validation: { min: 10, max: 200, step: 5 },
                helpText: '基于买入触发单量的百分比，如80%表示4800万(6000万的80%)'
            },
            {
                name: 'direction',
                label: '监控方向',
                type: 'select',
                required: true,
                options: [
                    { label: '买盘', value: 'buy' },
                    { label: '卖盘', value: 'sell' },
                    { label: '双向', value: 'both' }
                ],
                defaultValue: 'sell',
                helpText: '监控买盘还是卖盘的成交量'
            },
            {
                name: 'brokerFilter',
                label: '经纪商筛选',
                type: 'multiselect',
                required: false,
                placeholder: '选择特定经纪商（可选）',
                options: getBrokerOptions(),
                defaultValue: [],
                helpText: '可选：仅监控指定经纪商的交易'
            }
        ],
        defaultParams: {
            percentage: 80,
            direction: 'sell',
            brokerFilter: []
        }
    },
    {
        id: 'price_target_sell',
        name: '价格目标触发',
        description: '当价格达到指定目标时触发卖出',
        type: 'price_target',
        configFields: [
            {
                name: 'targetPrice',
                label: '目标价格',
                type: 'number',
                required: true,
                defaultValue: 0,
                placeholder: '输入目标价格',
                validation: { min: 0.001, step: 0.001 },
                helpText: '达到此价格时触发卖出'
            },
            {
                name: 'triggerType',
                label: '触发类型',
                type: 'select',
                required: true,
                options: [
                    { label: '触及', value: 'touch' },
                    { label: '突破', value: 'break' }
                ],
                defaultValue: 'touch',
                helpText: '价格触及还是突破目标价格'
            },
            {
                name: 'direction',
                label: '方向',
                type: 'select',
                required: true,
                options: [
                    { label: '高于', value: 'above' },
                    { label: '低于', value: 'below' }
                ],
                defaultValue: 'above',
                helpText: '高于还是低于目标价格'
            }
        ],
        defaultParams: {
            targetPrice: 0,
            triggerType: 'touch',
            direction: 'above'
        }
    },
    {
        id: 'time_based_sell',
        name: '时间基础触发',
        description: '基于持仓时间来触发卖出',
        type: 'time_based',
        configFields: [
            {
                name: 'holdingMinutes',
                label: '持仓时间(分钟)',
                type: 'number',
                required: true,
                defaultValue: 30,
                placeholder: '输入持仓时间',
                validation: { min: 1, max: 240 },
                helpText: '持仓超过此时间后触发卖出'
            },
            {
                name: 'autoTrigger',
                label: '自动触发',
                type: 'select',
                required: true,
                options: [
                    { label: '自动触发', value: true },
                    { label: '仅提醒', value: false }
                ],
                defaultValue: true,
                helpText: '是否自动触发卖出或仅提醒'
            }
        ],
        defaultParams: {
            holdingMinutes: 30,
            autoTrigger: true
        }
    }
];

// 卖出执行模板定义
export const SELL_EXECUTION_TEMPLATES: SellExecutionTemplate[] = [
    {
        id: 'priority_levels_execution',
        name: '优先档位执行',
        description: '按档位优先级执行卖出，适合精确控制成交价格',
        type: 'priority_levels',
        configFields: [
            {
                name: 'basePriceSource',
                label: '基准价格来源',
                type: 'select',
                required: true,
                options: [
                    { label: '触发单价格', value: 'trigger_order_price' },
                    { label: '当前卖一价', value: 'current_ask' },
                    { label: '当前买一价', value: 'current_bid' }
                ],
                defaultValue: 'trigger_order_price',
                helpText: '计算档位的基准价格'
            },
            {
                name: 'levelCount',
                label: '尝试档位数量',
                type: 'number',
                required: true,
                defaultValue: 3,
                placeholder: '输入档位数量',
                validation: { min: 1, max: 5 },
                helpText: '最多尝试几个档位，建议3个档位'
            },
            {
                name: 'tickDirection',
                label: '档位方向',
                type: 'select',
                required: true,
                options: [
                    { label: '向上(更高价格)', value: 'up' },
                    { label: '向下(更低价格)', value: 'down' }
                ],
                defaultValue: 'down',
                helpText: '卖出时通常选择向下(更低价格)以提高成交概率'
            },
            {
                name: 'levelTimeout',
                label: '每档位超时时间(秒)',
                type: 'number',
                required: true,
                defaultValue: 10,
                placeholder: '输入超时时间',
                validation: { min: 3, max: 60 },
                helpText: '每个档位等待成交的最长时间'
            },
            {
                name: 'onAllLevelsFailed',
                label: '所有档位失败时',
                type: 'select',
                required: true,
                options: [
                    { label: '人工处理提醒', value: 'alert_manual' },
                    { label: '转为市价单', value: 'market_order' },
                    { label: '触发风控清仓', value: 'risk_control' }
                ],
                defaultValue: 'alert_manual',
                helpText: '所有档位都无法成交时的处理方式'
            }
        ],
        defaultParams: {
            basePriceSource: 'trigger_order_price',
            levelCount: 3,
            tickDirection: 'down',
            levelTimeout: 10,
            onAllLevelsFailed: 'alert_manual'
        }
    },
    {
        id: 'market_order_execution',
        name: '市价单执行',
        description: '立即市价卖出，保证成交但可能有滑点',
        type: 'market_order',
        configFields: [
            {
                name: 'maxSlippageBps',
                label: '最大滑点限制(基点)',
                type: 'number',
                required: false,
                defaultValue: 30,
                placeholder: '输入最大滑点',
                validation: { min: 5, max: 100 },
                helpText: '允许的最大滑点，超过则报警(1基点=0.01%)'
            }
        ],
        defaultParams: {
            maxSlippageBps: 30
        }
    },
    {
        id: 'smart_execution',
        name: '智能执行',
        description: '根据市场状况智能选择最优执行方式',
        type: 'smart_execution',
        configFields: [
            {
                name: 'aggressiveness',
                label: '激进程度',
                type: 'select',
                required: true,
                options: [
                    { label: '保守(更少滑点)', value: 'low' },
                    { label: '平衡(推荐)', value: 'medium' },
                    { label: '激进(更快成交)', value: 'high' }
                ],
                defaultValue: 'medium',
                helpText: '平衡成交速度和滑点成本'
            },
            {
                name: 'maxSlippageBps',
                label: '最大滑点容忍(基点)',
                type: 'number',
                required: true,
                defaultValue: 20,
                placeholder: '输入最大滑点',
                validation: { min: 5, max: 50 },
                helpText: '超过此滑点将转为市价单'
            },
            {
                name: 'timeoutSeconds',
                label: '超时时间(秒)',
                type: 'number',
                required: true,
                defaultValue: 15,
                placeholder: '输入超时时间',
                validation: { min: 5, max: 60 },
                helpText: '未完全成交的超时时间'
            }
        ],
        defaultParams: {
            aggressiveness: 'medium',
            maxSlippageBps: 20,
            timeoutSeconds: 15
        }
    }
];

// 卖出策略模板定义
export const SELL_STRATEGY_TEMPLATES: SellStrategyTemplate[] = [
    {
        id: 'big_order_follow_sell',
        name: '大单跟随卖出策略',
        description: '专为大单跟随策略设计的卖出方案，支持固定和动态触发条件',
        conditionTemplates: [
            SELL_CONDITION_TEMPLATES.find(t => t.id === 'fixed_volume_sell')!,
            SELL_CONDITION_TEMPLATES.find(t => t.id === 'dynamic_percentage_sell')!,
            SELL_CONDITION_TEMPLATES.find(t => t.id === 'price_target_sell')!
        ],
        executionTemplates: [
            SELL_EXECUTION_TEMPLATES.find(t => t.id === 'priority_levels_execution')!,
            SELL_EXECUTION_TEMPLATES.find(t => t.id === 'smart_execution')!
        ],
        defaultConfig: {
            enabled: true,
            conditions: [
                {
                    type: 'fixed_volume',
                    params: {
                        volumeThreshold: 5000000,
                        direction: 'sell',
                        brokerFilter: []
                    }
                }
            ],
            execution: {
                type: 'priority_levels',
                params: {
                    basePriceSource: 'trigger_order_price',
                    levelCount: 3,
                    tickDirection: 'down',
                    levelTimeout: 10,
                    onAllLevelsFailed: 'alert_manual'
                }
            }
        }
    },
    {
        id: 'general_sell_strategy',
        name: '通用卖出策略',
        description: '适用于各种策略的通用卖出方案，提供多种触发和执行选项',
        conditionTemplates: SELL_CONDITION_TEMPLATES,
        executionTemplates: SELL_EXECUTION_TEMPLATES,
        defaultConfig: {
            enabled: false,
            conditions: [
                {
                    type: 'time_based',
                    params: {
                        holdingMinutes: 60,
                        autoTrigger: true
                    }
                }
            ],
            execution: {
                type: 'smart_execution',
                params: {
                    aggressiveness: 'medium',
                    maxSlippageBps: 20,
                    timeoutSeconds: 15
                }
            }
        }
    }
];

// 获取卖出条件模板
export const getSellConditionTemplate = (id: string): SellConditionTemplate | undefined => {
    return SELL_CONDITION_TEMPLATES.find(template => template.id === id);
};

// 获取卖出执行模板
export const getSellExecutionTemplate = (id: string): SellExecutionTemplate | undefined => {
    return SELL_EXECUTION_TEMPLATES.find(template => template.id === id);
};

// 获取卖出策略模板
export const getSellStrategyTemplate = (id: string): SellStrategyTemplate | undefined => {
    return SELL_STRATEGY_TEMPLATES.find(template => template.id === id);
};