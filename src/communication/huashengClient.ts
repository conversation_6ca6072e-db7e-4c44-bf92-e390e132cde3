// 华盛客户端 - 专门处理华盛交易的通信
import { TradingClientBase, ApiResponse, TradingDataType } from './tradingClient';

/**
 * 华盛交易客户端
 */
export class HuashengClient extends TradingClientBase {
    constructor() {
        super('huasheng');
    }

    /**
     * 获取资金信息
     */
    async getFunds(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_funds');
            console.log('华盛资金查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛资金查询失败:', error);
            return {
                success: false,
                message: `华盛资金查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取持仓信息
     */
    async getPositions(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_positions');
            console.log('华盛持仓查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛持仓查询失败:', error);
            return {
                success: false,
                message: `华盛持仓查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取账户信息
     */
    async getAccountInfo(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_account_info');
            console.log('华盛账户信息查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛账户信息查询失败:', error);
            return {
                success: false,
                message: `华盛账户信息查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 下单
     */
    async placeOrder(params: {
        stockCode: string;
        orderType: 'BUY' | 'SELL';
        quantity: number;
        price?: number;
        orderMode?: 'LIMIT' | 'MARKET';
    }): Promise<ApiResponse> {
        try {
            const result = await this.invoke('place_order', params);
            console.log('华盛下单结果:', result);
            return result;
        } catch (error) {
            console.error('华盛下单失败:', error);
            return {
                success: false,
                message: `华盛下单失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取订单列表
     */
    async getOrders(status?: string): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_orders', { status });
            console.log('华盛订单查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛订单查询失败:', error);
            return {
                success: false,
                message: `华盛订单查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 撤单
     */
    async cancelOrder(orderId: string): Promise<ApiResponse> {
        try {
            const result = await this.invoke('cancel_order', { orderId });
            console.log('华盛撤单结果:', result);
            return result;
        } catch (error) {
            console.error('华盛撤单失败:', error);
            return {
                success: false,
                message: `华盛撤单失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取交易历史
     */
    async getTradeHistory(startDate?: string, endDate?: string): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_trade_history', { startDate, endDate });
            console.log('华盛交易历史查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛交易历史查询失败:', error);
            return {
                success: false,
                message: `华盛交易历史查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取市场数据
     */
    async getMarketData(market: string = 'HK'): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_market_data', { market });
            console.log('华盛市场数据查询结果:', result);
            return result;
        } catch (error) {
            console.error('华盛市场数据查询失败:', error);
            return {
                success: false,
                message: `华盛市场数据查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 执行交易演示
     */
    async runTradingDemo(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('run_trading_demo');
            console.log('华盛交易演示结果:', result);
            return result;
        } catch (error) {
            console.error('华盛交易演示失败:', error);
            return {
                success: false,
                message: `华盛交易演示失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 订阅华盛特定的实时数据
     */
    async subscribeHuashengData(stockCodes: string[], dataTypes: TradingDataType[]): Promise<ApiResponse> {
        try {
            const result = await this.invoke('subscribe_huasheng_data', { stockCodes, dataTypes });
            console.log('华盛实时数据订阅结果:', result);
            return result;
        } catch (error) {
            console.error('华盛实时数据订阅失败:', error);
            return {
                success: false,
                message: `华盛实时数据订阅失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 取消华盛实时数据订阅
     */
    async unsubscribeHuashengData(stockCodes: string[], dataTypes: TradingDataType[]): Promise<ApiResponse> {
        try {
            const result = await this.invoke('unsubscribe_huasheng_data', { stockCodes, dataTypes });
            console.log('华盛取消实时数据订阅结果:', result);
            return result;
        } catch (error) {
            console.error('华盛取消实时数据订阅失败:', error);
            return {
                success: false,
                message: `华盛取消实时数据订阅失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 内部调用方法
     */
    private async invoke(action: string, params?: any): Promise<ApiResponse> {
        const { invoke } = await import("@tauri-apps/api/tauri");
        return await invoke<ApiResponse>("send_huasheng_command", {
            action: action,
            params: params || null
        });
    }
}

// 导出华盛客户端实例
export const huashengClient = new HuashengClient();
