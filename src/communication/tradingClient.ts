// 交易客户端 - 前端与 Tauri 交易适配器的通信封装
import { invoke } from "@tauri-apps/api/tauri";
import { listen, UnlistenFn } from "@tauri-apps/api/event";

// 基础响应接口
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
}

// 交易数据类型
export enum TradingDataType {
    Quote = "Quote",
    Ticker = "Ticker",
    OrderBook = "OrderBook",
    Broker = "Broker",
    Funds = "Funds",
    Position = "Position",
    Order = "Order"
}

// 股票代码结构
export interface StockCode {
    market: string; // HK, US, SH, SZ
    code: string; // 股票代码
}

// 交易命令参数
export interface TradingCommandParams {
    stock_code?: string;
    data_type?: TradingDataType;
    count?: number;
    subscribe?: boolean;
    extra?: Record<string, any>;
}

// 实时数据事件
export interface RealtimeDataEvent {
    source: string;
    data_type: string;
    stock_code?: string;
    data: any;
    timestamp: number;
}

// 连接状态
export enum ConnectionStatus {
    Disconnected = "Disconnected",
    Connecting = "Connecting",
    Connected = "Connected",
    Error = "Error"
}

// 适配器状态
export interface AdapterStatus {
    name: string;
    connection_status: ConnectionStatus;
    last_heartbeat?: number;
    error_count: number;
    uptime: number;
}

/**
 * 交易客户端基类 - 提供与交易适配器通信的标准接口
 */
export abstract class TradingClientBase {
    protected adapterName: string;
    protected eventListeners: Map<string, UnlistenFn> = new Map();

    constructor(adapterName: string) {
        this.adapterName = adapterName;
    }

    /**
     * 获取适配器脚本路径
     */
    protected getScriptPath(): string {
        return `src-python/adapters/${this.adapterName}_adapter_enhanced.py`;
    }

    /**
     * 发送命令到适配器
     */
    protected async sendCommand(action: string, params?: any): Promise<ApiResponse> {
        try {
            const result = await invoke<ApiResponse>("send_service_command", {
                serviceName: this.adapterName,
                action: action,
                params: params || {}
            });
            console.log(`${this.adapterName} ${action} 结果:`, result);
            return result;
        } catch (error) {
            console.error(`${this.adapterName} ${action} 失败:`, error);
            return {
                success: false,
                message: `${this.adapterName} ${action} 失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 启动交易适配器
     */
    async start(): Promise<ApiResponse> {
        try {
            // 使用新的多服务管理命令
            const result = await invoke<ApiResponse>("start_service", {
                serviceName: this.adapterName
            });
            console.log(`${this.adapterName} 适配器启动结果:`, result);
            return result;
        } catch (error) {
            console.error(`${this.adapterName} 适配器启动失败:`, error);
            return {
                success: false,
                message: `${this.adapterName} 适配器启动失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 停止交易适配器
     */
    async stop(): Promise<ApiResponse> {
        try {
            // 清理事件监听器
            for (const [eventName, unlisten] of this.eventListeners) {
                unlisten();
                console.log(`Unregistered event listener: ${eventName}`);
            }
            this.eventListeners.clear();

            const result = await invoke<ApiResponse>("stop_service", {
                serviceName: this.adapterName
            });
            console.log(`${this.adapterName} 适配器停止结果:`, result);
            return result;
        } catch (error) {
            console.error(`${this.adapterName} 适配器停止失败:`, error);
            return {
                success: false,
                message: `${this.adapterName} 适配器停止失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 连接到交易服务
     */
    async connect(params?: any): Promise<ApiResponse> {
        return await this.sendCommand("connect", params);
    }

    /**
     * 断开交易服务连接
     */
    async disconnect(): Promise<ApiResponse> {
        return await this.sendCommand("disconnect");
    }

    /**
     * 获取股票报价数据
     */
    async getQuote(stockCode: string): Promise<ApiResponse> {
        return await this.sendCommand("get_quote", { stock_code: stockCode });
    }

    /**
     * 获取逐笔交易数据
     */
    async getTicker(stockCode: string, count: number = 20): Promise<ApiResponse> {
        return await this.sendCommand("get_ticker", { stock_code: stockCode, count });
    }

    /**
     * 获取买卖盘数据
     */
    async getOrderBook(stockCode: string): Promise<ApiResponse> {
        return await this.sendCommand("get_order_book", { stock_code: stockCode });
    }

    /**
     * 获取经纪队列数据
     */
    async getBrokerQueue(stockCode: string): Promise<ApiResponse> {
        return await this.sendCommand("get_broker_queue", { stock_code: stockCode });
    }

    /**
     * 订阅实时数据
     */
    async subscribeRealtime(stockCode: string, dataTypes: TradingDataType[]): Promise<ApiResponse> {
        return await this.sendCommand("subscribe_realtime", {
            stock_code: stockCode,
            data_types: dataTypes
        });
    }

    /**
     * 取消订阅实时数据
     */
    async unsubscribeRealtime(stockCode: string, dataTypes: TradingDataType[]): Promise<ApiResponse> {
        return await this.sendCommand("unsubscribe_realtime", {
            stock_code: stockCode,
            data_types: dataTypes
        });
    }

    /**
     * 监听实时数据推送
     */
    async onRealtimeData(callback: (event: RealtimeDataEvent) => void): Promise<void> {
        const eventName = `sidecar-${this.adapterName}`;

        if (this.eventListeners.has(eventName)) {
            console.warn(`Event listener for ${eventName} already exists`);
            return;
        }

        try {
            const unlisten = await listen<RealtimeDataEvent>(eventName, (event) => {
                callback(event.payload);
            });

            this.eventListeners.set(eventName, unlisten);
            console.log(`Registered event listener: ${eventName}`);
        } catch (error) {
            console.error(`Failed to register event listener for ${eventName}:`, error);
        }
    }

    /**
     * 心跳检查
     */
    async ping(): Promise<ApiResponse> {
        return await this.sendCommand("ping");
    }

    /**
     * 获取适配器状态
     */
    async getStatus(): Promise<ApiResponse<AdapterStatus>> {
        return (await this.sendCommand("get_status")) as ApiResponse<AdapterStatus>;
    }

    /**
     * 获取适配器名称
     */
    getAdapterName(): string {
        return this.adapterName;
    }
}
