// 富途客户端 - 专门处理富途交易的通信
import { TradingClientBase, ApiResponse, TradingDataType } from './tradingClient';

/**
 * 富途交易客户端
 */
export class FutuClient extends TradingClientBase {
    constructor() {
        super('futu');
    }

    /**
     * 获取资金信息
     */
    async getFunds(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_funds');
            console.log('富途资金查询结果:', result);
            return result;
        } catch (error) {
            console.error('富途资金查询失败:', error);
            return {
                success: false,
                message: `富途资金查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取持仓信息
     */
    async getPositions(): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_positions');
            console.log('富途持仓查询结果:', result);
            return result;
        } catch (error) {
            console.error('富途持仓查询失败:', error);
            return {
                success: false,
                message: `富途持仓查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取历史K线数据
     */
    async getKlineData(stockCode: string, period: string = '1D', count: number = 100): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_kline', { stockCode, period, count });
            console.log('富途K线数据查询结果:', result);
            return result;
        } catch (error) {
            console.error('富途K线数据查询失败:', error);
            return {
                success: false,
                message: `富途K线数据查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 下单
     */
    async placeOrder(params: {
        stockCode: string;
        orderType: 'BUY' | 'SELL';
        quantity: number;
        price?: number;
        orderMode?: 'LIMIT' | 'MARKET';
    }): Promise<ApiResponse> {
        try {
            const result = await this.invoke('place_order', params);
            console.log('富途下单结果:', result);
            return result;
        } catch (error) {
            console.error('富途下单失败:', error);
            return {
                success: false,
                message: `富途下单失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取订单列表
     */
    async getOrders(status?: string): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_orders', { status });
            console.log('富途订单查询结果:', result);
            return result;
        } catch (error) {
            console.error('富途订单查询失败:', error);
            return {
                success: false,
                message: `富途订单查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 撤单
     */
    async cancelOrder(orderId: string): Promise<ApiResponse> {
        try {
            const result = await this.invoke('cancel_order', { orderId });
            console.log('富途撤单结果:', result);
            return result;
        } catch (error) {
            console.error('富途撤单失败:', error);
            return {
                success: false,
                message: `富途撤单失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 获取市场状态
     */
    async getMarketState(market: string = 'HK'): Promise<ApiResponse> {
        try {
            const result = await this.invoke('get_market_state', { market });
            console.log('富途市场状态查询结果:', result);
            return result;
        } catch (error) {
            console.error('富途市场状态查询失败:', error);
            return {
                success: false,
                message: `富途市场状态查询失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 订阅富途特定的实时数据
     */
    async subscribeFutuData(stockCodes: string[], dataTypes: TradingDataType[]): Promise<ApiResponse> {
        try {
            const result = await this.invoke('subscribe_futu_data', { stockCodes, dataTypes });
            console.log('富途实时数据订阅结果:', result);
            return result;
        } catch (error) {
            console.error('富途实时数据订阅失败:', error);
            return {
                success: false,
                message: `富途实时数据订阅失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 取消富途实时数据订阅
     */
    async unsubscribeFutuData(stockCodes: string[], dataTypes: TradingDataType[]): Promise<ApiResponse> {
        try {
            const result = await this.invoke('unsubscribe_futu_data', { stockCodes, dataTypes });
            console.log('富途取消实时数据订阅结果:', result);
            return result;
        } catch (error) {
            console.error('富途取消实时数据订阅失败:', error);
            return {
                success: false,
                message: `富途取消实时数据订阅失败: ${error}`,
                data: null
            };
        }
    }

    /**
     * 内部调用方法
     */
    private async invoke(action: string, params?: any): Promise<ApiResponse> {
        const { invoke } = await import("@tauri-apps/api/tauri");
        return await invoke<ApiResponse>("send_futu_command", {
            action: action,
            params: params || null
        });
    }
}

// 导出富途客户端实例
export const futuClient = new FutuClient();
