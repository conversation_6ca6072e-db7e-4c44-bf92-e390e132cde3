// 通信模块统一导出 - 新的标准化交易通信接口

// 交易客户端基类
export { TradingClientBase } from "./tradingClient";
export { TradingDataType } from "./tradingClient";
export { ConnectionStatus } from "./tradingClient";
export type { ApiResponse, StockCode, TradingCommandParams, RealtimeDataEvent, AdapterStatus } from "./tradingClient";

// 富途交易客户端
export { FutuClient, futuClient } from "./futuClient";

// 华盛交易客户端
export { HuashengClient, huashengClient } from "./huashengClient";

// 计数器测试客户端
export { CounterClient, counterClient } from "./counterClient";
export type { CounterData, CounterResponse } from "./counterClient";
