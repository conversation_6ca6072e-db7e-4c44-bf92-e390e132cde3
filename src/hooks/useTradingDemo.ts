// 交易演示逻辑 Hook
import { useState, useEffect } from 'react';
import { futuClient, huashengClient, TradingDataType, RealtimeDataEvent } from '../communication';

export const useTradingDemo = () => {
    const [futuConnected, setFutuConnected] = useState(false);
    const [huashengConnected, setHuashengConnected] = useState(false);
    const [loading, setLoading] = useState(false);
    const [quote, setQuote] = useState<any>(null);
    const [funds, setFunds] = useState<any>(null);
    const [realtimeData, setRealtimeData] = useState<RealtimeDataEvent[]>([]);

    useEffect(() => {
        // 设置实时数据监听
        const setupRealtimeListeners = async () => {
            // 监听富途实时数据
            await futuClient.onRealtimeData((event) => {
                console.log('富途实时数据:', event);
                setRealtimeData(prev => [...prev.slice(-9), event]); // 保留最新10条
            });

            // 监听华盛实时数据
            await huashengClient.onRealtimeData((event) => {
                console.log('华盛实时数据:', event);
                setRealtimeData(prev => [...prev.slice(-9), event]); // 保留最新10条
            });
        };

        setupRealtimeListeners();

        // 清理函数
        return () => {
            futuClient.stop();
            huashengClient.stop();
        };
    }, []);

    // 启动富途适配器
    const startFutuAdapter = async () => {
        setLoading(true);
        try {
            const result = await futuClient.start();
            if (result.success) {
                setFutuConnected(true);
                console.log('富途适配器启动成功');
            } else {
                console.error('富途适配器启动失败:', result.message);
            }
        } catch (error) {
            console.error('富途适配器启动异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 启动华盛适配器
    const startHuashengAdapter = async () => {
        setLoading(true);
        try {
            const result = await huashengClient.start();
            if (result.success) {
                setHuashengConnected(true);
                console.log('华盛适配器启动成功');
            } else {
                console.error('华盛适配器启动失败:', result.message);
            }
        } catch (error) {
            console.error('华盛适配器启动异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 连接富途服务
    const connectFutu = async () => {
        if (!futuConnected) return;
        
        setLoading(true);
        try {
            const result = await futuClient.connect();
            console.log('富途连接结果:', result);
        } catch (error) {
            console.error('富途连接失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 连接华盛服务
    const connectHuasheng = async () => {
        if (!huashengConnected) return;
        
        setLoading(true);
        try {
            const result = await huashengClient.connect();
            console.log('华盛连接结果:', result);
        } catch (error) {
            console.error('华盛连接失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取富途报价
    const getFutuQuote = async () => {
        if (!futuConnected) return;
        
        setLoading(true);
        try {
            const result = await futuClient.getQuote('HK.00700');
            if (result.success) {
                setQuote(result.data);
                console.log('富途报价:', result.data);
            }
        } catch (error) {
            console.error('获取富途报价失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取华盛资金
    const getHuashengFunds = async () => {
        if (!huashengConnected) return;
        
        setLoading(true);
        try {
            const result = await huashengClient.getFunds();
            if (result.success) {
                setFunds(result.data);
                console.log('华盛资金:', result.data);
            }
        } catch (error) {
            console.error('获取华盛资金失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 订阅富途实时数据
    const subscribeFutuRealtime = async () => {
        if (!futuConnected) return;
        
        try {
            const result = await futuClient.subscribeRealtime('HK.00700', [
                TradingDataType.Quote,
                TradingDataType.Ticker
            ]);
            console.log('富途实时数据订阅结果:', result);
        } catch (error) {
            console.error('富途实时数据订阅失败:', error);
        }
    };

    // 富途下单演示
    const placeFutuOrder = async () => {
        if (!futuConnected) return;
        
        try {
            const result = await futuClient.placeOrder({
                stockCode: 'HK.00700',
                orderType: 'BUY',
                quantity: 100,
                price: 420.00,
                orderMode: 'LIMIT'
            });
            console.log('富途下单结果:', result);
        } catch (error) {
            console.error('富途下单失败:', error);
        }
    };

    return {
        // 状态
        futuConnected,
        huashengConnected,
        loading,
        quote,
        funds,
        realtimeData,
        
        // 操作函数
        startFutuAdapter,
        startHuashengAdapter,
        connectFutu,
        connectHuasheng,
        getFutuQuote,
        getHuashengFunds,
        subscribeFutuRealtime,
        placeFutuOrder
    };
};
