import { useState, useEffect, useCallback } from 'react';
import { listen } from '@tauri-apps/api/event';
import { Task, GlobalStatus, StrategyConfig, RiskConfig, ConnectionStatus, LogEntry, LogLevel, LogCategory } from '../types';
import { futuClient, huashengClient } from '../communication';
import { isTauriEnvironment, safeTauriCall } from '../utils/environment';

interface UseDashboardReturn {
    // 状态
    tasks: Task[];
    globalStatus: GlobalStatus;
    isLoading: boolean;
    selectedTask: Task | null;
    showConfigModal: boolean;
    showDetailsModal: boolean;
    
    // 操作函数
    startAllTasks: () => Promise<void>;
    stopAllTasks: () => Promise<void>;
    addTask: (taskConfig: Partial<Task>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
    toggleTask: (taskId: string) => Promise<void>;
    liquidateTask: (taskId: string) => Promise<void>;
    copyTask: (taskId: string) => Promise<void>;
    getTaskLogs: (taskId: string) => Promise<LogEntry[]>;
    
    // Modal 控制
    openConfigModal: (task?: Task) => void;
    closeConfigModal: () => void;
    openDetailsModal: (taskId: string) => void;
    closeDetailsModal: () => void;
    
    // 配置相关
    saveTaskConfig: (taskConfig: Partial<Task>) => Promise<void>;
    editTask: (taskId: string) => void;
}

export const useDashboard = (): UseDashboardReturn => {
    // 核心状态
    const [tasks, setTasks] = useState<Task[]>([]);
    const [globalStatus, setGlobalStatus] = useState<GlobalStatus>({
        totalTasks: 0,
        runningTasks: 0,
        totalMarketValue: 0,
        totalPnL: 0,
        connections: {
            market: 'disconnected' as ConnectionStatus,
            trading: 'disconnected' as ConnectionStatus
        }
    });
    const [isLoading, setIsLoading] = useState(false);
    
    // Modal 状态
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [showConfigModal, setShowConfigModal] = useState(false);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    // 生成示例数据
    const generateMockTasks = (): Task[] => [
        {
            id: 'task-1',
            name: '腾讯控股大单跟随策略',
            stockCode: 'HK.00700',
            stockName: '腾讯控股',
            strategyName: '策略A: 大单监控',
            status: 'running',
            position: 500,
            avgCost: 415.5,
            pnl: 8750.50,
            createdAt: new Date('2025-07-22T09:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_a_big_order_monitor',
                params: {
                    monitorThreshold: 8000000,  // 800万港币大单监控
                    durationSeconds: 300,       // 持续5分钟
                    targetBrokers: ['高盛', '摩根士丹利', '瑞银', '中信建投'], // 主力经纪商
                    orderSize: 500,              // 每次买入500股
                    priceCondition: 420.0,       // 价格条件：420港币以下
                    requiresSellStrategy: true   // 需要卖出策略
                },
                // 卖出策略配置
                sellStrategy: {
                    enabled: true,
                    conditions: [
                        {
                            id: 'sell-1',
                            type: 'profit_target',
                            params: {
                                profitPercent: 5.0,      // 盈利5%卖出
                                sellRatio: 0.5           // 卖出50%仓位
                            }
                        },
                        {
                            id: 'sell-2',
                            type: 'time_exit',
                            params: {
                                holdingMinutes: 240,     // 持仓4小时后卖出
                                sellRatio: 1.0           // 全部卖出
                            }
                        }
                    ],
                    execution: {
                        type: 'priority_levels',
                        params: {
                            priorityLevels: [
                                { priceLevel: 'ask1', quantity: 200 },    // 卖一价卖200股
                                { priceLevel: 'ask2', quantity: 300 }     // 卖二价卖300股
                            ],
                            maxSlippageBps: 10
                        }
                    }
                }
            },
            riskConfig: {
                triggerLogic: 'any',  // 满足任一风控条件即触发
                conditions: [
                    {
                        id: 'risk-1',
                        type: 'fast_stop_loss',
                        params: { 
                            lossPercentage: 3.5      // 快速止损：亏损3.5%
                        }
                    },
                    {
                        id: 'risk-2', 
                        type: 'fast_take_profit',
                        params: {
                            profitPercentage: 8.0    // 快速止盈：盈利8%
                        }
                    },
                    {
                        id: 'risk-3',
                        type: 'time_limit',
                        params: {
                            maxHoldingMinutes: 300   // 最大持仓5小时
                        }
                    },
                    {
                        id: 'risk-4',
                        type: 'price_deviation',
                        params: {
                            priceDeviationPercent: 2.0  // 价格偏离2%
                        }
                    }
                ],
                liquidationStrategy: {
                    type: 'smart_execution',      // 智能限价单
                    params: {
                        aggressiveness: 'medium',  // 平衡模式
                        maxSlippageBps: 15         // 最大滑点15基点
                    }
                }
            }
        },
        {
            id: 'task-2',
            name: '小米突破策略',
            stockCode: 'HK.01810',
            stockName: '小米集团',
            strategyName: '策略B: 突破追涨',
            status: 'paused',
            position: 0,
            pnl: 0,
            createdAt: new Date('2025-07-22T10:15:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_b_breakout_chase',
                params: {
                    breakoutPeriod: 20,
                    volumeMultiplier: 2.5,
                    pullbackPercent: 3.0
                }
            },
            riskConfig: {
                triggerLogic: 'all',
                conditions: [],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-3',
            name: '阿里巴巴反转策略',
            stockCode: 'HK.09988',
            stockName: '阿里巴巴',
            strategyName: '策略C: 反转交易',
            status: 'running',
            position: 150,
            avgCost: 92.3,
            pnl: -1250.75,
            createdAt: new Date('2025-07-22T11:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_c_reversal',
                params: {
                    oversoldLevel: 20,
                    rebounds: 2,
                    volumeFilter: true
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-2',
                        type: 'stop_loss',
                        params: { stopPrice: 85.0 }
                    }
                ],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-4',
            name: '美团动量策略',
            stockCode: 'HK.03690',
            stockName: '美团',
            strategyName: '策略D: 动量追踪',
            status: 'running',
            position: 300,
            avgCost: 168.9,
            pnl: 5680.50,
            createdAt: new Date('2025-07-22T11:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_d_momentum',
                params: {
                    momentumPeriod: 15,
                    strengthThreshold: 0.7,
                    entrySignal: 'ma_cross'
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-3',
                        type: 'time_exit',
                        params: { exitTime: '15:00' }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'ask1',
                        priceOffset: 2,
                        direction: 'up',
                        timeoutSeconds: 30,
                        timeoutAction: 'market'
                    }
                }
            }
        },
        {
            id: 'task-5',
            name: '比亚迪均值回归',
            stockCode: 'HK.01211',
            stockName: '比亚迪股份',
            strategyName: '策略E: 均值回归',
            status: 'stopped',
            position: 0,
            pnl: 0,
            createdAt: new Date('2025-07-22T12:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_e_mean_reversion',
                params: {
                    lookbackPeriod: 30,
                    deviationThreshold: 2.0,
                    holdingPeriod: 5
                }
            },
            riskConfig: {
                triggerLogic: 'all',
                conditions: [],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-6',
            name: '建设银行价值策略',
            stockCode: 'HK.00939',
            stockName: '建设银行',
            strategyName: '策略F: 价值投资',
            status: 'running',
            position: 500,
            avgCost: 5.85,
            pnl: 785.20,
            createdAt: new Date('2025-07-22T12:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_f_value',
                params: {
                    peThreshold: 8.0,
                    dividendYield: 5.5,
                    bookValueRatio: 0.7
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-4',
                        type: 'volume_spike',
                        params: { spikeMultiple: 3.0 }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'mid',
                        priceOffset: 1,
                        direction: 'neutral',
                        timeoutSeconds: 60,
                        timeoutAction: 'market'
                    }
                }
            }
        },
        {
            id: 'task-7',
            name: '快手短线策略',
            stockCode: 'HK.01024',
            stockName: '快手科技',
            strategyName: '策略G: 短线交易',
            status: 'error',
            position: 0,
            pnl: 0,
            createdAt: new Date('2025-07-22T13:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_g_scalping',
                params: {
                    scalpPeriod: 5,
                    targetSpread: 0.02,
                    maxHoldingTime: 300
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-5',
                        type: 'connection_lost',
                        params: { timeoutSeconds: 30 }
                    }
                ],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-8',
            name: '中国移动网格策略',
            stockCode: 'HK.00941',
            stockName: '中国移动',
            strategyName: '策略H: 网格交易',
            status: 'running',
            position: 400,
            avgCost: 58.2,
            pnl: 2340.80,
            createdAt: new Date('2025-07-22T13:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_h_grid',
                params: {
                    gridLevels: 10,
                    gridSpacing: 1.0,
                    baseQuantity: 100
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-6',
                        type: 'drawdown',
                        params: { maxDrawdown: 10.0 }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'last',
                        priceOffset: 0.5,
                        direction: 'down',
                        timeoutSeconds: 45,
                        timeoutAction: 'cancel_and_limit'
                    }
                }
            }
        },
        {
            id: 'task-9',
            name: '汇丰控股分红策略',
            stockCode: 'HK.00005',
            stockName: '汇丰控股',
            strategyName: '策略I: 分红捕获',
            status: 'paused',
            position: 800,
            avgCost: 42.1,
            pnl: 1560.00,
            createdAt: new Date('2025-07-22T14:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_i_dividend_capture',
                params: {
                    exDividendDate: '2025-08-15',
                    holdDays: 3,
                    targetYield: 6.0
                }
            },
            riskConfig: {
                triggerLogic: 'all',
                conditions: [
                    {
                        id: 'risk-7',
                        type: 'ex_date_close',
                        params: { daysAfter: 1 }
                    }
                ],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-10',
            name: '中石化配对交易',
            stockCode: 'HK.00386',
            stockName: '中国石化',
            strategyName: '策略J: 配对交易',
            status: 'running',
            position: 600,
            avgCost: 4.35,
            pnl: -890.25,
            createdAt: new Date('2025-07-22T14:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_j_pairs_trading',
                params: {
                    pairStock: 'HK.00857',
                    correlation: 0.85,
                    spreadThreshold: 2.5
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-8',
                        type: 'correlation_break',
                        params: { minCorrelation: 0.6 }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'bid1',
                        priceOffset: 1,
                        direction: 'down',
                        timeoutSeconds: 25,
                        timeoutAction: 'market'
                    }
                }
            }
        },
        {
            id: 'task-11',
            name: '药明生物趋势策略',
            stockCode: 'HK.02269',
            stockName: '药明生物',
            strategyName: '策略K: 趋势跟踪',
            status: 'running',
            position: 120,
            avgCost: 78.5,
            pnl: 4250.40,
            createdAt: new Date('2025-07-22T15:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_k_trend_following',
                params: {
                    trendPeriod: 50,
                    confirmationPeriod: 10,
                    trendStrength: 0.8
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-9',
                        type: 'trend_reversal',
                        params: { reversalSignal: 'ma_cross_down' }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'ask1',
                        priceOffset: 3,
                        direction: 'up',
                        timeoutSeconds: 15,
                        timeoutAction: 'market'
                    }
                }
            }
        },
        {
            id: 'task-12',
            name: '中国平安保险策略',
            stockCode: 'HK.02318',
            stockName: '中国平安',
            strategyName: '策略L: 保险轮动',
            status: 'paused',
            position: 250,
            avgCost: 45.8,
            pnl: -625.50,
            createdAt: new Date('2025-07-22T15:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_l_sector_rotation',
                params: {
                    sectorIndex: 'insurance',
                    rotationSignal: 'relative_strength',
                    rebalanceFreq: 'weekly'
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-10',
                        type: 'sector_weakness',
                        params: { underperformDays: 5 }
                    }
                ],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-13',
            name: '理想汽车新能源',
            stockCode: 'HK.02015',
            stockName: '理想汽车',
            strategyName: '策略M: 新能源概念',
            status: 'stopped',
            position: 0,
            pnl: 0,
            createdAt: new Date('2025-07-22T16:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_m_theme_trading',
                params: {
                    themeIndex: 'new_energy',
                    momentumFilter: true,
                    newsWeight: 0.3
                }
            },
            riskConfig: {
                triggerLogic: 'all',
                conditions: [],
                liquidationStrategy: {
                    type: 'market',
                    params: {}
                }
            }
        },
        {
            id: 'task-14',
            name: '海尔智家白马策略',
            stockCode: 'HK.06690',
            stockName: '海尔智家',
            strategyName: '策略N: 白马蓝筹',
            status: 'running',
            position: 180,
            avgCost: 28.4,
            pnl: 890.70,
            createdAt: new Date('2025-07-22T16:30:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_n_blue_chip',
                params: {
                    qualityScore: 8.5,
                    stabilityPeriod: 36,
                    growthRate: 15.0
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-11',
                        type: 'quality_downgrade',
                        params: { minScore: 7.0 }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'mid',
                        priceOffset: 0,
                        direction: 'neutral',
                        timeoutSeconds: 40,
                        timeoutAction: 'cancel_and_market'
                    }
                }
            }
        },
        {
            id: 'task-15',
            name: '舜宇光学科技成长',
            stockCode: 'HK.02382',
            stockName: '舜宇光学科技',
            strategyName: '策略O: 科技成长',
            status: 'running',
            position: 90,
            avgCost: 112.8,
            pnl: -1450.80,
            createdAt: new Date('2025-07-22T17:00:00'),
            updatedAt: new Date(),
            strategyConfig: {
                strategyType: 'strategy_o_growth',
                params: {
                    growthMetrics: ['revenue', 'earnings', 'margins'],
                    innovationIndex: 8.2,
                    competitiveAdvantage: true
                }
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'risk-12',
                        type: 'growth_slowdown',
                        params: { quarterDecline: 2 }
                    }
                ],
                liquidationStrategy: {
                    type: 'limit_optimized',
                    params: {
                        basePrice: 'bid1',
                        priceOffset: 2,
                        direction: 'down',
                        timeoutSeconds: 35,
                        timeoutAction: 'market'
                    }
                }
            }
        }
    ];

    // 初始化数据
    const fetchTasks = useCallback(async () => {
        setIsLoading(true);
        try {
            // 模拟 API 调用延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            const mockTasks = generateMockTasks();
            setTasks(mockTasks);
            
            // 更新全局状态
            updateGlobalStatus(mockTasks);
        } catch (error) {
            console.error('获取任务列表失败:', error);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 更新全局状态
    const updateGlobalStatus = (taskList: Task[]) => {
        const runningTasks = taskList.filter(task => task.status === 'running').length;
        const totalPnL = taskList.reduce((sum, task) => sum + task.pnl, 0);
        const totalMarketValue = taskList.reduce((sum, task) => {
            if (task.position > 0 && task.avgCost) {
                return sum + (task.position * task.avgCost + task.pnl);
            }
            return sum;
        }, 0);

        setGlobalStatus(prev => ({
            ...prev,
            totalTasks: taskList.length,
            runningTasks,
            totalMarketValue,
            totalPnL,
            // 连接状态将通过实时事件更新
        }));
    };

    // 任务操作函数
    const startAllTasks = async () => {
        setIsLoading(true);
        try {
            const updatedTasks = tasks.map(task => 
                task.status === 'paused' || task.status === 'stopped' 
                    ? { ...task, status: 'running' as const, updatedAt: new Date() }
                    : task
            );
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            console.log('已启动所有任务');
        } catch (error) {
            console.error('启动所有任务失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const stopAllTasks = async () => {
        setIsLoading(true);
        try {
            const updatedTasks = tasks.map(task => 
                task.status === 'running' 
                    ? { ...task, status: 'paused' as const, updatedAt: new Date() }
                    : task
            );
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            console.log('已暂停所有任务');
        } catch (error) {
            console.error('停止所有任务失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const toggleTask = async (taskId: string) => {
        try {
            const updatedTasks = tasks.map(task => {
                if (task.id === taskId) {
                    let newStatus: Task['status'];
                    if (task.status === 'running') {
                        newStatus = 'paused';
                    } else if (task.status === 'paused' || task.status === 'stopped' || task.status === 'liquidated') {
                        newStatus = 'running';
                    } else {
                        // error状态不能切换
                        return task;
                    }
                    return { ...task, status: newStatus, updatedAt: new Date() };
                }
                return task;
            });
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            
            const task = tasks.find(t => t.id === taskId);
            const newStatus = updatedTasks.find(t => t.id === taskId)?.status;
            console.log(`任务 ${task?.name} 已${newStatus === 'running' ? '启动' : '暂停'}`);
        } catch (error) {
            console.error('切换任务状态失败:', error);
        }
    };

    const addTask = async (taskConfig: Partial<Task>) => {
        setIsLoading(true);
        try {
            const newTask: Task = {
                id: `task-${Date.now()}`,
                name: taskConfig.name || '未命名任务',
                stockCode: taskConfig.stockCode || '',
                stockName: taskConfig.stockName || '',
                strategyName: taskConfig.strategyName || '',
                status: 'stopped',
                position: 0,
                pnl: 0,
                createdAt: new Date(),
                updatedAt: new Date(),
                strategyConfig: taskConfig.strategyConfig || { strategyType: '', params: {} },
                riskConfig: taskConfig.riskConfig || {
                    triggerLogic: 'any',
                    conditions: [],
                    liquidationStrategy: { type: 'market', params: {} }
                },
                ...taskConfig
            };
            
            const updatedTasks = [...tasks, newTask];
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            console.log('已添加新任务:', newTask.name);
        } catch (error) {
            console.error('添加任务失败:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const updateTask = async (taskId: string, updates: Partial<Task>) => {
        try {
            const updatedTasks = tasks.map(task =>
                task.id === taskId 
                    ? { ...task, ...updates, updatedAt: new Date() }
                    : task
            );
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            console.log('已更新任务:', taskId);
        } catch (error) {
            console.error('更新任务失败:', error);
        }
    };

    const liquidateTask = async (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (!task) {
            throw new Error('任务不存在');
        }
        
        if (task.status !== 'running') {
            throw new Error('只有运行中的任务可以清仓');
        }
        
        if (task.position <= 0) {
            throw new Error('该任务没有持仓');
        }

        setIsLoading(true);
        try {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 模拟清仓API调用
            // const result = await tradingClient.liquidatePosition(task.stockCode, task.position);
            
            const updatedTasks = tasks.map(t =>
                t.id === taskId 
                    ? { 
                        ...t, 
                        status: 'liquidated' as const,
                        position: 0, 
                        avgCost: undefined,
                        pnl: 0,
                        updatedAt: new Date() 
                    }
                    : t
            );
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            
            // 显示成功通知（在实际应用中可以使用toast组件）
            console.log(`✅ 任务 ${task.name} 已成功清仓`);
            return { success: true, message: '清仓成功' };
        } catch (error) {
            console.error('清仓失败:', error);
            const errorMessage = error instanceof Error ? error.message : '清仓失败，请重试';
            throw new Error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const deleteTask = async (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (!task) {
            throw new Error('任务不存在或已被删除');
        }
        
        if (task.status === 'running') {
            throw new Error('无法删除运行中的任务，请先停止任务');
        }
        
        setIsLoading(true);
        try {
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            const updatedTasks = tasks.filter(t => t.id !== taskId);
            setTasks(updatedTasks);
            updateGlobalStatus(updatedTasks);
            
            console.log(`✅ 任务 ${task.name} 已删除`);
            return { success: true, message: '删除成功' };
        } catch (error) {
            console.error('删除任务失败:', error);
            const errorMessage = error instanceof Error ? error.message : '删除失败，请重试';
            throw new Error(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    const copyTask = async (taskId: string) => {
        const sourceTask = tasks.find(t => t.id === taskId);
        if (!sourceTask) {
            throw new Error('源任务不存在');
        }
        
        // 生成唯一的任务名称
        const generateUniqueName = (baseName: string): string => {
            const copyName = `${baseName}_副本`;
            const existingNames = tasks.map(t => t.name);
            
            if (!existingNames.includes(copyName)) {
                return copyName;
            }
            
            // 如果已存在，添加数字后缀
            let counter = 1;
            let uniqueName = `${copyName}${counter}`;
            while (existingNames.includes(uniqueName)) {
                counter++;
                uniqueName = `${copyName}${counter}`;
            }
            return uniqueName;
        };
        
        // 创建复制的任务配置（排除运行时状态）
        const copiedTask: Partial<Task> = {
            name: generateUniqueName(sourceTask.name),
            stockCode: sourceTask.stockCode,
            stockName: sourceTask.stockName,
            strategyName: sourceTask.strategyName,
            strategyConfig: JSON.parse(JSON.stringify(sourceTask.strategyConfig)), // 深拷贝
            riskConfig: JSON.parse(JSON.stringify(sourceTask.riskConfig)), // 深拷贝
            // 不复制运行时状态
            status: 'stopped',
            position: 0,
            pnl: 0,
            avgCost: undefined
        };
        
        // 打开配置模态框，预填充复制的数据
        setSelectedTask({ ...sourceTask, ...copiedTask, id: 'temp-copy' } as Task);
        setShowConfigModal(true);
        
        console.log(`已准备复制任务: ${sourceTask.name} -> ${copiedTask.name}`);
    };

    const getTaskLogs = async (taskId: string): Promise<LogEntry[]> => {
        const task = tasks.find(t => t.id === taskId);
        if (!task) {
            throw new Error('任务不存在');
        }

        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 生成模拟日志数据
        const generateMockLogs = (task: Task): LogEntry[] => {
            const logs: LogEntry[] = [];
            const now = new Date();
            const levels: LogLevel[] = ['info', 'warning', 'error', 'debug'];
            const categories: LogCategory[] = ['strategy', 'trading', 'system'];

            // 根据任务状态生成不同类型的日志
            const logTemplates = {
                info: [
                    `任务 ${task.name} 启动成功`,
                    `策略参数加载完成: ${task.strategyName}`,
                    `股票代码验证通过: ${task.stockCode}`,
                    `风控规则已激活`,
                    `监控目标价格: ${task.avgCost || 'N/A'}`,
                    `持仓数量更新: ${task.position} 股`,
                    `实时PnL计算: ${task.pnl.toFixed(2)} HKD`,
                    '行情数据连接正常',
                    '交易接口连接正常'
                ],
                warning: [
                    '价格波动超过预期范围',
                    '市场流动性不足，调整订单策略',
                    '网络延迟较高，可能影响交易执行',
                    '目标经纪商队列数据缺失',
                    '成交量异常偏低',
                    '风控条件即将触发'
                ],
                error: [
                    '交易接口连接失败',
                    '订单提交被拒绝',
                    '行情数据推送中断',
                    '策略执行异常终止',
                    '风控清仓执行失败',
                    '账户余额不足'
                ],
                debug: [
                    `策略参数: monitorThreshold=${JSON.stringify(task.strategyConfig?.params)}`,
                    `风控条件检查完成`,
                    `订单簿深度分析中`,
                    `技术指标计算完成`,
                    `经纪商队列更新`,
                    `价格变动检测: ±0.5%`
                ]
            };

            // 生成60条日志记录（最近24小时）
            for (let i = 0; i < 60; i++) {
                const level = levels[Math.floor(Math.random() * levels.length)];
                const category = categories[Math.floor(Math.random() * categories.length)];
                const templates = logTemplates[level];
                const message = templates[Math.floor(Math.random() * templates.length)];
                
                // 创建时间戳（最近24小时内的随机时间）
                const timestamp = new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000);
                
                const logEntry: LogEntry = {
                    id: `log-${taskId}-${i}`,
                    timestamp,
                    level,
                    category,
                    message,
                    taskId,
                    taskName: task.name
                };

                // 为部分日志添加详细信息
                if (Math.random() < 0.3) {
                    logEntry.details = {
                        taskId: task.id,
                        stockCode: task.stockCode,
                        currentPrice: task.avgCost ? (task.avgCost + (Math.random() - 0.5) * 10).toFixed(2) : null,
                        position: task.position,
                        pnl: task.pnl.toFixed(2)
                    };
                }

                logs.push(logEntry);
            }

            // 按时间戳排序（最新的在前）
            return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        };

        const mockLogs = generateMockLogs(task);
        console.log(`获取任务 ${task.name} 的日志，共 ${mockLogs.length} 条记录`);
        return mockLogs;
    };

    // Modal 控制函数
    const openConfigModal = (task?: Task) => {
        setSelectedTask(task || null);
        setShowConfigModal(true);
    };

    const closeConfigModal = () => {
        setSelectedTask(null);
        setShowConfigModal(false);
    };

    const openDetailsModal = (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            setSelectedTask(task);
            setShowDetailsModal(true);
        }
    };

    const closeDetailsModal = () => {
        setSelectedTask(null);
        setShowDetailsModal(false);
    };

    const editTask = (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            if (task.status === 'running') {
                // 任务正在运行中，不能编辑
                alert('请先暂停任务后再进行编辑');
                return;
            }
            openConfigModal(task);
        }
    };

    const saveTaskConfig = async (taskConfig: Partial<Task>) => {
        setIsLoading(true);
        try {
            if (selectedTask && selectedTask.id !== 'temp-copy') {
                // 编辑现有任务
                await updateTask(selectedTask.id, taskConfig);
                console.log(`任务 ${taskConfig.name || selectedTask.name} 已更新`);
            } else {
                // 添加新任务（包括复制的任务）
                await addTask(taskConfig);
                if (selectedTask?.id === 'temp-copy') {
                    console.log(`任务复制成功: ${taskConfig.name}`);
                }
            }
            closeConfigModal();
        } catch (error) {
            console.error('保存任务配置失败:', error);
            alert('保存失败，请重试');
        } finally {
            setIsLoading(false);
        }
    };

    // 监听连接状态变化
    useEffect(() => {
        const setupEventListeners = async () => {
            if (!isTauriEnvironment()) {
                console.debug('当前在浏览器环境中，设置模拟连接状态');
                // 在非Tauri环境下，设置模拟连接状态
                setGlobalStatus(prev => ({
                    ...prev,
                    connections: {
                        market: 'connected',
                        trading: 'connected'
                    }
                }));
                return;
            }

            try {
                await listen('sidecar-message', (event: any) => {
                    const message = event.payload;
                    
                    if (message.type === 'push') {
                        // 处理实时数据推送，更新任务状态
                        if (message.source === 'futu' && message.data.type === 'connection_status') {
                            setGlobalStatus(prev => ({
                                ...prev,
                                connections: {
                                    ...prev.connections,
                                    market: message.data.connected ? 'connected' : 'disconnected'
                                }
                            }));
                        }
                        
                        if (message.source === 'huasheng' && message.data.type === 'connection_status') {
                            setGlobalStatus(prev => ({
                                ...prev,
                                connections: {
                                    ...prev.connections,
                                    trading: message.data.connected ? 'connected' : 'disconnected'
                                }
                            }));
                        }
                    }
                });
            } catch (error) {
                console.error('设置事件监听器失败:', error);
                // 在错误情况下，也设置模拟连接状态
                setGlobalStatus(prev => ({
                    ...prev,
                    connections: {
                        market: 'connected',
                        trading: 'connected'
                    }
                }));
            }
        };

        setupEventListeners();
        fetchTasks();
    }, [fetchTasks]);

    // 定期更新任务 PnL（模拟实时数据）
    useEffect(() => {
        const interval = setInterval(() => {
            setTasks(prevTasks => {
                const updatedTasks = prevTasks.map(task => {
                    if (task.status === 'running' && task.position > 0) {
                        // 模拟价格波动：±0.5% 的随机变化
                        const priceChange = (Math.random() - 0.5) * 0.01 * (task.avgCost || 100);
                        const newPnL = task.pnl + (priceChange * task.position);
                        return { ...task, pnl: newPnL };
                    }
                    return task;
                });
                
                updateGlobalStatus(updatedTasks);
                return updatedTasks;
            });
        }, 2000); // 每2秒更新一次

        return () => clearInterval(interval);
    }, []);

    return {
        // 状态
        tasks,
        globalStatus,
        isLoading,
        selectedTask,
        showConfigModal,
        showDetailsModal,
        
        // 操作函数
        startAllTasks,
        stopAllTasks,
        addTask,
        updateTask,
        deleteTask,
        toggleTask,
        liquidateTask,
        copyTask,
        getTaskLogs,
        
        // Modal 控制
        openConfigModal,
        closeConfigModal,
        openDetailsModal,
        closeDetailsModal,
        
        // 配置相关
        saveTaskConfig,
        editTask
    };
};