/**
 * 环境检测工具
 * 用于判断当前运行环境，防止在浏览器中调用Tauri API
 */

/**
 * 检查当前是否在Tauri环境中运行
 * @returns {boolean} 如果在Tauri环境中返回true，否则返回false
 */
export function isTauriEnvironment(): boolean {
    return typeof window !== 'undefined' && 
           typeof window.__TAURI_IPC__ !== 'undefined' &&
           typeof window.__TAURI_IPC__ === 'function';
}

/**
 * 检查当前是否在浏览器开发环境中
 * @returns {boolean} 如果在浏览器环境中返回true
 */
export function isBrowserEnvironment(): boolean {
    return typeof window !== 'undefined' && 
           typeof window.__TAURI_IPC__ === 'undefined';
}

/**
 * 安全地调用Tauri API
 * @param fn 需要调用的异步函数
 * @param fallback 当不在Tauri环境时的回调值
 * @returns Promise<T> 
 */
export async function safeTauriCall<T>(
    fn: () => Promise<T>, 
    fallback: T | null = null
): Promise<T | null> {
    if (isTauriEnvironment()) {
        try {
            return await fn();
        } catch (error) {
            console.warn('Tauri API调用失败:', error);
            return fallback;
        }
    } else {
        console.debug('当前在浏览器环境中，跳过Tauri API调用');
        return fallback;
    }
}

/**
 * 打印环境信息
 */
export function logEnvironmentInfo(): void {
    if (isTauriEnvironment()) {
        console.log('🖥️ 当前运行在Tauri桌面应用环境中');
    } else if (isBrowserEnvironment()) {
        console.log('🌐 当前运行在浏览器环境中');
    } else {
        console.log('❓ 未知的运行环境');
    }
}