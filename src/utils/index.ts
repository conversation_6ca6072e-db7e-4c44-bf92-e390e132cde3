// 工具函数

/**
 * 格式化时间戳为本地时间字符串
 */
export const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleTimeString();
};

/**
 * 格式化金额显示
 */
export const formatCurrency = (amount: number, currency: string = 'HKD'): string => {
    return new Intl.NumberFormat('zh-HK', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount);
};

/**
 * 格式化百分比显示
 */
export const formatPercentage = (value: number): string => {
    return `${(value >= 0 ? '+' : '')}${value.toFixed(2)}%`;
};

/**
 * 获取股票代码的显示名称
 */
export const getStockDisplayName = (stockCode: string): string => {
    const stockNames: Record<string, string> = {
        'HK.00700': '腾讯控股',
        'HK.00941': '中国移动',
        'HK.00005': '汇丰控股',
        'HK.00388': '香港交易所',
        'HK.01299': '友邦保险'
    };
    
    return stockNames[stockCode] || stockCode;
};

/**
 * 延迟函数
 */
export const delay = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
};
