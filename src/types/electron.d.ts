// Electron API 类型定义

export interface ElectronAPI {
    trading: {
        connect: (adapter: string, config: any) => Promise<ApiResponse>;
        disconnect: (adapter: string) => Promise<ApiResponse>;
        getStatus: (adapter: string) => Promise<ApiResponse>;
        getQuote: (adapter: string, stockCode: string) => Promise<ApiResponse<Quote>>;
        getOrderBook: (adapter: string, stockCode: string) => Promise<ApiResponse<OrderBook>>;
        getTicker: (adapter: string, stockCode: string, count?: number) => Promise<ApiResponse<Ticker[]>>;
        getBrokerQueue: (adapter: string, stockCode: string) => Promise<ApiResponse<BrokerQueue>>;
        subscribe: (adapter: string, stockCode: string, dataTypes: string[]) => Promise<ApiResponse>;
        unsubscribe: (adapter: string, stockCode: string, dataTypes: string[]) => Promise<ApiResponse>;
        placeOrder: (adapter: string, order: any) => Promise<ApiResponse>;
        cancelOrder: (adapter: string, orderId: string) => Promise<ApiResponse>;
        getPositions: (adapter: string, accountId?: string) => Promise<ApiResponse<Position[]>>;
        getOrders: (adapter: string, filter?: any) => Promise<ApiResponse<Order[]>>;
        getAccounts: (adapter: string) => Promise<ApiResponse<Account[]>>;
    };
    task: {
        create: (config: any) => Promise<ApiResponse>;
        start: (taskId: string) => Promise<ApiResponse>;
        stop: (taskId: string) => Promise<ApiResponse>;
        delete: (taskId: string) => Promise<ApiResponse>;
        getList: () => Promise<ApiResponse>;
        getDetail: (taskId: string) => Promise<ApiResponse>;
    };
    config: {
        get: (key: string) => Promise<ApiResponse>;
        set: (key: string, value: any) => Promise<ApiResponse>;
        delete: (key: string) => Promise<ApiResponse>;
        getAll: () => Promise<ApiResponse>;
    };
    window: {
        minimize: () => void;
        maximize: () => void;
        close: () => void;
        toggleDevTools: () => void;
        reload: () => void;
    };
    on: (channel: string, callback: (event: any, ...args: any[]) => void) => void;
    removeListener: (channel: string, callback: (...args: any[]) => void) => void;
    removeAllListeners: (channel: string) => void;
}

declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}

// 类型导入
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

export interface Quote {
    stockCode: string;
    price: number;
    change: number;
    changePercent: number;
    volume: number;
    timestamp: number;
}

export interface Ticker {
    stockCode: string;
    price: number;
    volume: number;
    direction: 'BUY' | 'SELL' | 'NEUTRAL';
    timestamp: number;
}

export interface OrderBook {
    stockCode: string;
    asks: OrderBookItem[];
    bids: OrderBookItem[];
    timestamp: number;
}

export interface OrderBookItem {
    price: number;
    volume: number;
}

export interface BrokerQueue {
    stockCode: string;
    askBrokers: BrokerItem[];
    bidBrokers: BrokerItem[];
    timestamp: number;
}

export interface BrokerItem {
    brokerId: string;
    brokerName: string;
    volume: number;
}

export interface Position {
    stockCode: string;
    quantity: number;
    avgCost: number;
    currentPrice: number;
    profit: number;
    profitPercent: number;
}

export interface Order {
    orderId: string;
    stockCode: string;
    orderType: 'BUY' | 'SELL';
    price: number;
    quantity: number;
    status: string;
}

export interface Account {
    accountId: string;
    accountName: string;
    cash: number;
    marketValue: number;
    totalValue: number;
}