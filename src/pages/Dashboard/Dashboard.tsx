import React, { useState } from 'react';
import { useDashboard } from '../../hooks/useDashboard';
import { GlobalControls } from '../../components/Dashboard/GlobalControls';
import { TaskList } from '../../components/Dashboard/TaskList';
import { TaskConfigModal } from '../../components/Dashboard/TaskConfigModal';
import { TaskDetailsModal } from '../../components/Dashboard/TaskDetailsModal';
import { TaskLogModal } from '../../components/Dashboard/TaskLogModal';
import { LoadingOverlay } from '../../components';
import { LogEntry } from '../../types';

export const Dashboard: React.FC = () => {
    // 日志模态框状态
    const [showLogModal, setShowLogModal] = useState(false);
    const [currentTaskLogs, setCurrentTaskLogs] = useState<LogEntry[]>([]);
    const [logModalTask, setLogModalTask] = useState<any>(null);

    const {
        // 状态
        tasks,
        globalStatus,
        isLoading,
        selectedTask,
        showConfigModal,
        showDetailsModal,
        
        // 操作函数
        startAllTasks,
        stopAllTasks,
        addTask,
        updateTask,
        deleteTask,
        toggleTask,
        liquidateTask,
        copyTask,
        getTaskLogs,
        
        // Modal 控制
        openConfigModal,
        closeConfigModal,
        openDetailsModal,
        closeDetailsModal,
        
        // 配置相关
        saveTaskConfig,
        editTask
    } = useDashboard();

    // 处理查看日志
    const handleViewLogs = async (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (!task) return;

        try {
            const logs = await getTaskLogs(taskId);
            setCurrentTaskLogs(logs);
            setLogModalTask(task);
            setShowLogModal(true);
        } catch (error) {
            console.error('获取任务日志失败:', error);
            alert('获取日志失败，请重试');
        }
    };

    // 关闭日志模态框
    const handleCloseLogModal = () => {
        setShowLogModal(false);
        setCurrentTaskLogs([]);
        setLogModalTask(null);
    };

    return (
        <div className="dashboard">
            {/* 全局控制区 */}
            <GlobalControls
                globalStatus={globalStatus}
                onAddTask={openConfigModal}
                onStartAll={startAllTasks}
                onStopAll={stopAllTasks}
                isLoading={isLoading}
            />

            {/* 任务列表区 */}
            <div className="dashboard-content">
                <TaskList
                    tasks={tasks}
                    onToggleTask={toggleTask}
                    onShowDetails={openDetailsModal}
                    onEditTask={editTask}
                    onDeleteTask={deleteTask}
                    onLiquidateTask={liquidateTask}
                    onCopyTask={copyTask}
                    onViewLogs={handleViewLogs}
                />
            </div>

            {/* 任务配置模态窗口 */}
            {showConfigModal && (
                <TaskConfigModal
                    task={selectedTask}
                    onSave={saveTaskConfig}
                    onClose={closeConfigModal}
                />
            )}

            {/* 任务详细信息模态窗口 */}
            {showDetailsModal && selectedTask && (
                <TaskDetailsModal
                    task={selectedTask}
                    onClose={closeDetailsModal}
                />
            )}

            {/* 任务日志模态窗口 */}
            {showLogModal && logModalTask && (
                <TaskLogModal
                    task={logModalTask}
                    logs={currentTaskLogs}
                    onClose={handleCloseLogModal}
                />
            )}

            {/* 加载遮罩 */}
            <LoadingOverlay loading={isLoading} />
        </div>
    );
};