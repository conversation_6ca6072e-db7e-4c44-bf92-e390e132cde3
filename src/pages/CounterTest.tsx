import React, { useState, useEffect } from 'react';
import { counterClient, CounterData, CounterResponse } from '@/communication';

export const CounterTest: React.FC = () => {
    const [isStarted, setIsStarted] = useState(false);
    const [counterData, setCounterData] = useState<CounterData | null>(null);
    const [status, setStatus] = useState<string>('未启动');
    const [loading, setLoading] = useState(false);
    const [logs, setLogs] = useState<string[]>([]);


    const addLog = (message: string) => {
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
    };

    useEffect(() => {
        // 添加计数器数据监听器
        const handleCounterData = (data: CounterData) => {
            setCounterData(data);
            addLog(`计数器更新: ${data.counter}`);
        };

        counterClient.addListener(handleCounterData);

        // 检查服务状态（仅检查，不自动启动）
        const checkStatus = async () => {
            try {
                const status = await counterClient.getStatus();
                if (status && status.running) {
                    setIsStarted(true);
                    setStatus('运行中');
                } else {
                    setIsStarted(false);
                    setStatus('未启动');
                }
            } catch (error) {
                console.error('检查服务状态失败:', error);
                setStatus('检查失败');
            }
        };

        // 初始化：仅检查状态，不自动启动
        const initializeService = async () => {
            try {
                const status = await counterClient.getStatus();
                if (status && status.running) {
                    setIsStarted(true);
                    setStatus('运行中');
                    addLog('发现服务已在运行');
                } else {
                    setIsStarted(false);
                    setStatus('未启动');
                    addLog('服务未启动，请手动启动');
                }
            } catch (error) {
                addLog(`状态检查失败: ${error}`);
                setStatus('状态检查失败');
            }
        };

        initializeService();

        // 定期检查状态（仅更新状态，不自动启动）
        const statusInterval = setInterval(checkStatus, 3000); // 每3秒检查一次状态

        return () => {
            counterClient.removeListener(handleCounterData);
            clearInterval(statusInterval);
        };
    }, []);

    const handleStart = async () => {
        setLoading(true);
        try {
            addLog('正在启动计数器服务...');
            await counterClient.start();

            // 启动后等待一下再检查状态
            setTimeout(async () => {
                try {
                    const status = await counterClient.getStatus();
                    if (status && status.running) {
                        setIsStarted(true);
                        setStatus('运行中');
                        addLog('计数器服务启动成功');
                    } else {
                        addLog('服务启动但状态检查失败');
                        setStatus('状态未知');
                    }
                } catch (error) {
                    addLog(`状态检查失败: ${error}`);
                }
            }, 1000);
        } catch (error) {
            addLog(`启动失败: ${error}`);
            setStatus('启动失败');
        } finally {
            setLoading(false);
        }
    };

    const handleStop = async () => {
        setLoading(true);
        try {
            addLog('正在停止计数器服务...');
            await counterClient.stop();
            setIsStarted(false);
            setStatus('已停止');
            setCounterData(null);
            // 重要：停止后不要重置 hasAutoStarted，防止自动重启
            addLog('计数器服务已停止');
        } catch (error) {
            addLog(`停止失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handlePing = async () => {
        setLoading(true);
        try {
            const response: CounterResponse = await counterClient.ping();
            addLog(`Ping 响应: ${JSON.stringify(response)}`);
        } catch (error) {
            addLog(`Ping 失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleGetCounter = async () => {
        setLoading(true);
        try {
            const response: CounterResponse = await counterClient.getCounter();
            addLog(`获取计数器: ${JSON.stringify(response)}`);
        } catch (error) {
            addLog(`获取计数器失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleResetCounter = async () => {
        setLoading(true);
        try {
            const response: CounterResponse = await counterClient.resetCounter();
            addLog(`重置计数器: ${JSON.stringify(response)}`);
        } catch (error) {
            addLog(`重置计数器失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleGetStatus = async () => {
        setLoading(true);
        try {
            const response = await counterClient.getStatus();
            addLog(`服务状态: ${JSON.stringify(response)}`);
        } catch (error) {
            addLog(`获取状态失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-6 max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">计数器服务测试</h1>
            
            {/* 状态显示 */}
            <div className="bg-gray-100 p-4 rounded-lg mb-6">
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <span className="font-semibold">服务状态: </span>
                        <span className={`px-2 py-1 rounded text-sm ${
                            isStarted ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                            {status}
                        </span>
                    </div>
                    <div>
                        <span className="font-semibold">当前计数: </span>
                        <span className="text-xl font-mono">
                            {counterData ? counterData.counter : '--'}
                        </span>
                    </div>
                </div>
                {counterData && (
                    <div className="mt-2 text-sm text-gray-600">
                        最后更新: {new Date(counterData.timestamp * 1000).toLocaleTimeString()}
                    </div>
                )}
            </div>

            {/* 控制按钮 */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                <button
                    onClick={handleStart}
                    disabled={loading || isStarted}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {loading ? '启动中...' : '启动服务'}
                </button>
                
                <button
                    onClick={handleStop}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {loading ? '停止中...' : '停止服务'}
                </button>
                
                <button
                    onClick={handlePing}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Ping 测试
                </button>
                
                <button
                    onClick={handleGetCounter}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    获取计数器
                </button>
                
                <button
                    onClick={handleResetCounter}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    重置计数器
                </button>
                
                <button
                    onClick={handleGetStatus}
                    disabled={loading}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    获取状态
                </button>
            </div>

            {/* 日志显示 */}
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
                <div className="mb-2 font-semibold">操作日志:</div>
                <div className="h-48 overflow-y-auto">
                    {logs.length === 0 ? (
                        <div className="text-gray-500">暂无日志...</div>
                    ) : (
                        logs.map((log, index) => (
                            <div key={index} className="mb-1">
                                {log}
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};
