import React from 'react';
import { GlobalStatus } from '../../types';

interface GlobalControlsProps {
    globalStatus: GlobalStatus;
    onAddTask: () => void;
    onStartAll: () => void;
    onStopAll: () => void;
    isLoading: boolean;
}

export const GlobalControls: React.FC<GlobalControlsProps> = ({
    globalStatus,
    onAddTask,
    onStartAll,
    onStopAll,
    isLoading
}) => {
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    const getConnectionIndicator = (status: string) => {
        const className = `connection-indicator ${status}`;
        return <span className={className}></span>;
    };

    return (
        <div className="global-controls">
            {/* 左侧：操作按钮 */}
            <div className="controls-left">
                <button 
                    className="btn btn-primary"
                    onClick={onAddTask}
                    disabled={isLoading}
                >
                    + 添加新任务
                </button>
                
                <div className="control-group">
                    <button 
                        className="btn btn-success"
                        onClick={onStartAll}
                        disabled={isLoading}
                    >
                        ▶ 全部启动
                    </button>
                    <button 
                        className="btn btn-warning"
                        onClick={onStopAll}
                        disabled={isLoading}
                    >
                        ❚❚ 全部暂停
                    </button>
                </div>
            </div>

            {/* 中间：全局状态概览 */}
            <div className="global-stats">
                <div className="stat-item">
                    <span className="stat-label">总任务数</span>
                    <span className="stat-value">{globalStatus.totalTasks}</span>
                </div>
                <div className="stat-item">
                    <span className="stat-label">运行中</span>
                    <span className="stat-value running">{globalStatus.runningTasks}</span>
                </div>
                <div className="stat-item">
                    <span className="stat-label">总持仓市值</span>
                    <span className="stat-value currency">{formatCurrency(globalStatus.totalMarketValue)}</span>
                </div>
                <div className="stat-item">
                    <span className="stat-label">总浮动盈亏</span>
                    <span className={`stat-value currency ${globalStatus.totalPnL >= 0 ? 'profit' : 'loss'}`}>
                        {formatCurrency(globalStatus.totalPnL)}
                    </span>
                </div>
            </div>

            {/* 右侧：连接状态 */}
            <div className="connection-status">
                <div className="connection-item">
                    {getConnectionIndicator(globalStatus.connections.market)}
                    <span className="connection-label">行情</span>
                </div>
                <div className="connection-item">
                    {getConnectionIndicator(globalStatus.connections.trading)}
                    <span className="connection-label">交易</span>
                </div>
            </div>
        </div>
    );
};