import { useEffect, useCallback, useState } from 'react';

// 键盘快捷键配置
export interface KeyboardShortcut {
    key: string;
    ctrlKey?: boolean;
    altKey?: boolean;
    shiftKey?: boolean;
    metaKey?: boolean;
    description: string;
    handler: () => void;
}

// 键盘快捷键Hook
export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // 忽略输入框中的按键
            if (
                event.target instanceof HTMLInputElement ||
                event.target instanceof HTMLTextAreaElement ||
                event.target instanceof HTMLSelectElement
            ) {
                return;
            }

            shortcuts.forEach(shortcut => {
                const matchesKey = event.key.toLowerCase() === shortcut.key.toLowerCase();
                const matchesCtrl = shortcut.ctrlKey ? event.ctrlKey : true;
                const matchesAlt = shortcut.altKey ? event.altKey : true;
                const matchesShift = shortcut.shiftKey ? event.shiftKey : true;
                const matchesMeta = shortcut.metaKey ? event.metaKey : true;

                if (matchesKey && matchesCtrl && matchesAlt && matchesShift && matchesMeta) {
                    event.preventDefault();
                    shortcut.handler();
                }
            });
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [shortcuts]);
};

// 焦点管理Hook
export const useFocusManagement = () => {
    const [focusableElements, setFocusableElements] = useState<HTMLElement[]>([]);
    const [currentFocusIndex, setCurrentFocusIndex] = useState(0);

    const updateFocusableElements = useCallback((container: HTMLElement) => {
        const elements = container.querySelectorAll<HTMLElement>(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        setFocusableElements(Array.from(elements));
    }, []);

    const focusNext = useCallback(() => {
        if (focusableElements.length === 0) return;
        
        const nextIndex = (currentFocusIndex + 1) % focusableElements.length;
        focusableElements[nextIndex]?.focus();
        setCurrentFocusIndex(nextIndex);
    }, [focusableElements, currentFocusIndex]);

    const focusPrevious = useCallback(() => {
        if (focusableElements.length === 0) return;
        
        const prevIndex = currentFocusIndex === 0 
            ? focusableElements.length - 1 
            : currentFocusIndex - 1;
        focusableElements[prevIndex]?.focus();
        setCurrentFocusIndex(prevIndex);
    }, [focusableElements, currentFocusIndex]);

    const focusFirst = useCallback(() => {
        focusableElements[0]?.focus();
        setCurrentFocusIndex(0);
    }, [focusableElements]);

    const focusLast = useCallback(() => {
        const lastIndex = focusableElements.length - 1;
        focusableElements[lastIndex]?.focus();
        setCurrentFocusIndex(lastIndex);
    }, [focusableElements]);

    return {
        updateFocusableElements,
        focusNext,
        focusPrevious,
        focusFirst,
        focusLast
    };
};

// 焦点陷阱Hook（用于模态框）
export const useFocusTrap = (isActive: boolean = true) => {
    useEffect(() => {
        if (!isActive) return;

        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key !== 'Tab') return;

            const focusableElements = document.querySelectorAll<HTMLElement>(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            const focusableArray = Array.from(focusableElements);
            const firstElement = focusableArray[0];
            const lastElement = focusableArray[focusableArray.length - 1];

            if (event.shiftKey && document.activeElement === firstElement) {
                event.preventDefault();
                lastElement?.focus();
            } else if (!event.shiftKey && document.activeElement === lastElement) {
                event.preventDefault();
                firstElement?.focus();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [isActive]);
};

// 任务卡片键盘导航Hook
export const useTaskCardKeyboardNavigation = (
    taskId: string,
    handlers: {
        onToggle: () => void;
        onShowDetails: () => void;
        onEdit: () => void;
        onDelete: () => void;
        onLiquidate: () => void;
    }
) => {
    const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
        // 防止在输入框中触发
        if (
            event.target instanceof HTMLInputElement ||
            event.target instanceof HTMLTextAreaElement ||
            event.target instanceof HTMLSelectElement
        ) {
            return;
        }

        switch (event.key) {
            case 'Enter':
            case ' ':
                event.preventDefault();
                handlers.onShowDetails();
                break;
            case 'p':
            case 'P':
                event.preventDefault();
                handlers.onToggle();
                break;
            case 'e':
            case 'E':
                event.preventDefault();
                handlers.onEdit();
                break;
            case 'l':
            case 'L':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    handlers.onLiquidate();
                }
                break;
            case 'Delete':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    handlers.onDelete();
                }
                break;
        }
    }, [handlers]);

    return { handleKeyDown };
};

// 全局键盘快捷键提示组件
import React from 'react';

interface KeyboardShortcutsHelpProps {
    isOpen: boolean;
    onClose: () => void;
    shortcuts: KeyboardShortcut[];
}

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
    isOpen,
    onClose,
    shortcuts
}) => {
    if (!isOpen) return null;

    const formatShortcut = (shortcut: KeyboardShortcut) => {
        const keys = [];
        if (shortcut.ctrlKey) keys.push('Ctrl');
        if (shortcut.altKey) keys.push('Alt');
        if (shortcut.shiftKey) keys.push('Shift');
        if (shortcut.metaKey) keys.push('⌘');
        keys.push(shortcut.key.toUpperCase());
        return keys.join(' + ');
    };

    return (
        <div
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999
            }}
            onClick={onClose}
        >
            <div
                style={{
                    background: 'white',
                    borderRadius: '8px',
                    padding: '24px',
                    maxWidth: '500px',
                    maxHeight: '80vh',
                    overflow: 'auto',
                    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <h2 style={{ marginTop: 0, marginBottom: '20px' }}>
                    键盘快捷键
                </h2>
                
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                    <tbody>
                        {shortcuts.map((shortcut, index) => (
                            <tr key={index}>
                                <td style={{
                                    padding: '8px 16px 8px 0',
                                    borderBottom: '1px solid #e9ecef'
                                }}>
                                    <kbd style={{
                                        background: '#f8f9fa',
                                        border: '1px solid #dee2e6',
                                        borderRadius: '3px',
                                        padding: '2px 6px',
                                        fontFamily: 'monospace',
                                        fontSize: '12px'
                                    }}>
                                        {formatShortcut(shortcut)}
                                    </kbd>
                                </td>
                                <td style={{
                                    padding: '8px 0',
                                    borderBottom: '1px solid #e9ecef',
                                    color: '#6c757d'
                                }}>
                                    {shortcut.description}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>

                <div style={{ marginTop: '20px', textAlign: 'right' }}>
                    <button
                        onClick={onClose}
                        style={{
                            padding: '8px 16px',
                            background: '#007bff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '4px',
                            cursor: 'pointer'
                        }}
                    >
                        关闭
                    </button>
                </div>
            </div>
        </div>
    );
};

// 任务列表键盘导航
export const useTaskListKeyboardNavigation = () => {
    const [selectedTaskIndex, setSelectedTaskIndex] = useState<number>(-1);
    const [taskElements, setTaskElements] = useState<HTMLElement[]>([]);

    const updateTaskElements = useCallback(() => {
        const elements = document.querySelectorAll<HTMLElement>('.task-card');
        setTaskElements(Array.from(elements));
    }, []);

    const selectTask = useCallback((index: number) => {
        if (index >= 0 && index < taskElements.length) {
            setSelectedTaskIndex(index);
            taskElements[index]?.focus();
            taskElements[index]?.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }, [taskElements]);

    const selectNext = useCallback(() => {
        const nextIndex = selectedTaskIndex + 1;
        if (nextIndex < taskElements.length) {
            selectTask(nextIndex);
        }
    }, [selectedTaskIndex, taskElements.length, selectTask]);

    const selectPrevious = useCallback(() => {
        const prevIndex = selectedTaskIndex - 1;
        if (prevIndex >= 0) {
            selectTask(prevIndex);
        }
    }, [selectedTaskIndex, selectTask]);

    const selectFirst = useCallback(() => {
        selectTask(0);
    }, [selectTask]);

    const selectLast = useCallback(() => {
        selectTask(taskElements.length - 1);
    }, [taskElements.length, selectTask]);

    // 键盘事件处理
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            // 忽略输入框中的按键
            if (
                event.target instanceof HTMLInputElement ||
                event.target instanceof HTMLTextAreaElement ||
                event.target instanceof HTMLSelectElement
            ) {
                return;
            }

            switch (event.key) {
                case 'ArrowDown':
                case 'j':
                    event.preventDefault();
                    selectNext();
                    break;
                case 'ArrowUp':
                case 'k':
                    event.preventDefault();
                    selectPrevious();
                    break;
                case 'Home':
                    event.preventDefault();
                    selectFirst();
                    break;
                case 'End':
                    event.preventDefault();
                    selectLast();
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [selectNext, selectPrevious, selectFirst, selectLast]);

    return {
        selectedTaskIndex,
        updateTaskElements,
        selectTask,
        selectNext,
        selectPrevious,
        selectFirst,
        selectLast
    };
};