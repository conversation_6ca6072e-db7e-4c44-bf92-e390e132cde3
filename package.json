{"name": "futunn-trading-platform", "private": true, "version": "0.1.0", "main": "electron-main/dist/index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src electron-main/src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "electron:dev": "concurrently --kill-others \"vite\" \"tsc -p electron-main/tsconfig.json --watch\" \"sleep 3 && cross-env NODE_ENV=development electron .\"", "electron:build": "vite build && tsc -p electron-main/tsconfig.json && electron-builder --config electron-builder.config.cjs"}, "dependencies": {"@tanstack/react-query": "^4.32.6", "@tauri-apps/api": "^1.5.0", "electron-store": "^10.1.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "^2.8.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^1.5.0", "@types/node": "^24.0.15", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.21", "concurrently": "^9.2.0", "cross-env": "^10.0.0", "electron": "^25.0.0", "electron-builder": "^24.0.0", "electron-debug": "^4.1.0", "electron-devtools-installer": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.0.2", "vite": "^4.4.5", "wait-on": "^8.0.4"}}